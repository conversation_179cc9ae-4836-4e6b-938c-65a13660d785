import { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

// Declare global speech recognition types
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export default function SpeechDiagnostic() {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [logs, setLogs] = useState<string[]>([]);
  const [micPermission, setMicPermission] = useState<string>('unknown');
  const recognitionRef = useRef<any>(null);

  const addLog = useCallback((message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `${timestamp}: ${message}`]);
    console.log(`🔍 ${message}`);
  }, []);

  const checkMicrophonePermission = useCallback(async () => {
    try {
      addLog('Checking microphone permission...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      setMicPermission('granted');
      addLog('✅ Microphone permission granted');
      
      // Test audio levels
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const source = audioContext.createMediaStreamSource(stream);
      source.connect(analyser);
      
      const dataArray = new Uint8Array(analyser.frequencyBinCount);
      analyser.getByteFrequencyData(dataArray);
      
      const hasAudio = dataArray.some(value => value > 0);
      addLog(`🎵 Audio input detected: ${hasAudio}`);
      
      // Cleanup
      source.disconnect();
      audioContext.close();
      stream.getTracks().forEach(track => track.stop());
      
      return true;
    } catch (err: any) {
      setMicPermission('denied');
      addLog(`❌ Microphone permission failed: ${err.message}`);
      setError(`Microphone permission failed: ${err.message}`);
      return false;
    }
  }, [addLog]);

  const testBrowserSpeechRecognition = useCallback(async () => {
    if (isListening) {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
      return;
    }

    addLog('Starting browser speech recognition test...');
    setError(null);
    setTranscript('');

    // Check browser support
    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const errorMsg = 'Browser speech recognition not supported';
      addLog(`❌ ${errorMsg}`);
      setError(errorMsg);
      return;
    }

    // Check microphone permission first
    const hasPermission = await checkMicrophonePermission();
    if (!hasPermission) {
      return;
    }

    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = false;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;
      
      addLog(`🎤 Speech recognition configured: continuous=${recognition.continuous}, interimResults=${recognition.interimResults}, lang=${recognition.lang}`);

      recognition.onstart = () => {
        addLog('✅ Speech recognition started - SPEAK NOW!');
        setIsListening(true);
        setTranscript('🎤 Listening... Please speak clearly!');
      };

      recognition.onresult = (event: any) => {
        addLog(`📝 Speech result received: ${event.results.length} results`);
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = 0; i < event.results.length; i++) {
          const result = event.results[i];
          const confidence = result[0].confidence || 0;
          
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
            addLog(`✅ Final result: "${result[0].transcript}" (confidence: ${Math.round(confidence * 100)}%)`);
          } else {
            interimTranscript += result[0].transcript;
            addLog(`⏳ Interim result: "${result[0].transcript}"`);
          }
        }

        const displayText = finalTranscript || interimTranscript;
        setTranscript(displayText);
        
        if (finalTranscript) {
          addLog(`🎯 Final transcript set: "${finalTranscript}"`);
        }
      };

      recognition.onerror = (event: any) => {
        addLog(`❌ Speech recognition error: ${event.error}`);
        setError(`Speech recognition error: ${event.error}`);
        setIsListening(false);
        
        // Provide specific guidance based on error
        if (event.error === 'no-speech') {
          addLog('💡 TIP: Speak louder and closer to your microphone');
        } else if (event.error === 'not-allowed') {
          addLog('💡 TIP: Allow microphone permission in your browser');
        } else if (event.error === 'audio-capture') {
          addLog('💡 TIP: Check if your microphone is working in other apps');
        }
      };

      recognition.onend = () => {
        addLog('🛑 Speech recognition ended');
        setIsListening(false);
      };

      recognitionRef.current = recognition;
      recognition.start();
      
      // Auto-stop after 10 seconds for testing
      setTimeout(() => {
        if (recognition && recognition.state !== 'inactive') {
          addLog('⏰ Auto-stopping recognition after 10 seconds');
          recognition.stop();
        }
      }, 10000);

    } catch (err: any) {
      addLog(`❌ Failed to start speech recognition: ${err.message}`);
      setError(`Failed to start speech recognition: ${err.message}`);
      setIsListening(false);
    }
  }, [isListening, addLog, checkMicrophonePermission]);

  const clearLogs = useCallback(() => {
    setLogs([]);
    setTranscript('');
    setError(null);
  }, []);

  // Check initial browser capabilities
  useEffect(() => {
    addLog('🔍 Checking browser capabilities...');
    addLog(`🌐 User Agent: ${navigator.userAgent}`);
    addLog(`🎤 Speech Recognition: ${('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) ? 'Available' : 'Not Available'}`);
    addLog(`📱 Media Devices: ${!!navigator.mediaDevices ? 'Available' : 'Not Available'}`);
    addLog(`🎵 MediaRecorder: ${typeof MediaRecorder !== 'undefined' ? 'Available' : 'Not Available'}`);
    
    if (typeof MediaRecorder !== 'undefined') {
      const formats = ['audio/wav', 'audio/webm', 'audio/mp4', 'audio/ogg'];
      formats.forEach(format => {
        addLog(`🎵 ${format}: ${MediaRecorder.isTypeSupported(format) ? 'Supported' : 'Not Supported'}`);
      });
    }
  }, [addLog]);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>🔍 Speech Recognition Diagnostic</span>
          <div className="flex space-x-2">
            <Badge variant={micPermission === 'granted' ? 'default' : micPermission === 'denied' ? 'destructive' : 'secondary'}>
              Mic: {micPermission}
            </Badge>
            <Badge variant={isListening ? 'default' : 'secondary'}>
              {isListening ? 'Listening' : 'Idle'}
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Controls */}
        <div className="flex space-x-2">
          <Button
            onClick={testBrowserSpeechRecognition}
            variant={isListening ? "destructive" : "default"}
            disabled={micPermission === 'denied'}
          >
            {isListening ? "🔴 Stop Test" : "🎤 Test Speech Recognition"}
          </Button>
          <Button onClick={checkMicrophonePermission} variant="outline">
            🔍 Check Microphone
          </Button>
          <Button onClick={clearLogs} variant="outline">
            🗑️ Clear Logs
          </Button>
        </div>

        {/* Current Status */}
        {error && (
          <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
            <div className="text-red-800 dark:text-red-200 font-medium">❌ Error</div>
            <div className="text-red-700 dark:text-red-300 text-sm">{error}</div>
          </div>
        )}

        {transcript && (
          <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="text-green-800 dark:text-green-200 font-medium">📝 Transcript</div>
            <div className="text-green-700 dark:text-green-300">{transcript}</div>
          </div>
        )}

        {/* Logs */}
        <div className="space-y-2">
          <div className="font-medium">📋 Diagnostic Logs</div>
          <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-3 max-h-60 overflow-y-auto">
            {logs.length === 0 ? (
              <div className="text-gray-500 text-sm">No logs yet...</div>
            ) : (
              <div className="space-y-1 text-sm font-mono">
                {logs.map((log, index) => (
                  <div key={index} className="text-gray-700 dark:text-gray-300">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="text-blue-800 dark:text-blue-200 font-medium mb-2">🎯 Test Instructions</div>
          <div className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
            <div>1. Click "Test Speech Recognition" to start</div>
            <div>2. When you see "Listening...", speak clearly: "Hello, this is a test"</div>
            <div>3. Watch the logs to see what happens</div>
            <div>4. The test will auto-stop after 10 seconds</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
