import { useEffect, useRef } from "react";

interface PoseOverlayProps {
  poses: any[];
  videoRef: React.RefObject<HTMLVideoElement>;
  className?: string;
}

export default function PoseOverlay({ poses, videoRef, className = "" }: PoseOverlayProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current || !videoRef.current || poses.length === 0) return;

    const canvas = canvasRef.current;
    const video = videoRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match video
    canvas.width = video.videoWidth || video.clientWidth;
    canvas.height = video.videoHeight || video.clientHeight;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw pose landmarks
    poses.forEach(pose => {
      if (pose.landmarks) {
        drawPose(ctx, pose.landmarks, canvas.width, canvas.height);
      }
    });
  }, [poses, videoRef]);

  const drawPose = (ctx: CanvasRenderingContext2D, landmarks: any[], width: number, height: number) => {
    // Set drawing style
    ctx.strokeStyle = '#00E6B8'; // accent color
    ctx.lineWidth = 3;
    ctx.fillStyle = '#00E6B8';

    // Draw pose connections
    const connections = [
      [11, 12], [11, 13], [13, 15], [12, 14], [14, 16], // Arms
      [11, 23], [12, 24], [23, 24], // Torso
      [23, 25], [25, 27], [24, 26], [26, 28], // Legs
    ];

    connections.forEach(([start, end]) => {
      const startPoint = landmarks[start];
      const endPoint = landmarks[end];
      
      if (startPoint && endPoint && startPoint.visibility > 0.5 && endPoint.visibility > 0.5) {
        ctx.beginPath();
        ctx.moveTo(startPoint.x * width, startPoint.y * height);
        ctx.lineTo(endPoint.x * width, endPoint.y * height);
        ctx.stroke();
      }
    });

    // Draw key joints
    const keyJoints = [11, 12, 13, 14, 15, 16, 23, 24, 25, 26, 27, 28];
    keyJoints.forEach(jointIndex => {
      const joint = landmarks[jointIndex];
      if (joint && joint.visibility > 0.5) {
        ctx.beginPath();
        ctx.arc(joint.x * width, joint.y * height, 5, 0, 2 * Math.PI);
        ctx.fill();
      }
    });
  };

  return (
    <canvas
      ref={canvasRef}
      className={`pointer-events-none ${className}`}
      style={{ 
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
      }}
      data-testid="canvas-pose-overlay"
    />
  );
}
