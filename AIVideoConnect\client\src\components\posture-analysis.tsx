import { useEffect, useState } from "react";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface PostureAnalysisProps {
  poses: any[];
  exercise: string;
  onFormFeedback?: (feedback: FormFeedback) => void;
}

interface FormFeedback {
  score: number;
  issues: string[];
  corrections: string[];
  repCount: number;
}

export default function PostureAnalysis({ poses, exercise, onFormFeedback }: PostureAnalysisProps) {
  const [formFeedback, setFormFeedback] = useState<FormFeedback>({
    score: 0,
    issues: [],
    corrections: [],
    repCount: 0
  });

  const analyzeSquatForm = (pose: any): FormFeedback => {
    const feedback: FormFeedback = {
      score: 100,
      issues: [],
      corrections: [],
      repCount: formFeedback.repCount
    };

    if (!pose?.landmarks) return feedback;

    try {
      // Get key landmarks for squat analysis
      const leftHip = pose.landmarks[23];
      const rightHip = pose.landmarks[24];
      const leftKnee = pose.landmarks[25];
      const rightKnee = pose.landmarks[26];
      const leftAnkle = pose.landmarks[27];
      const rightAnkle = pose.landmarks[28];
      const leftShoulder = pose.landmarks[11];
      const rightShoulder = pose.landmarks[12];

      // Check squat depth
      const avgHipY = (leftHip.y + rightHip.y) / 2;
      const avgKneeY = (leftKnee.y + rightKnee.y) / 2;
      
      if (avgHipY < avgKneeY + 0.05) {
        // Good depth achieved - count as rep
        if (formFeedback.repCount < 5) {
          feedback.repCount = formFeedback.repCount + 1;
        }
      } else {
        feedback.issues.push("Need deeper squat");
        feedback.corrections.push("Lower your hips below knee level");
        feedback.score -= 20;
      }

      // Check knee alignment
      const kneeAlignment = Math.abs(leftKnee.x - rightKnee.x);
      if (kneeAlignment > 0.15) {
        feedback.issues.push("Knees caving inward");
        feedback.corrections.push("Keep knees aligned over toes");
        feedback.score -= 15;
      }

      // Check torso position
      const avgShoulderY = (leftShoulder.y + rightShoulder.y) / 2;
      const torsoLean = Math.abs(avgShoulderY - avgHipY);
      if (torsoLean < 0.1) {
        feedback.issues.push("Torso too upright");
        feedback.corrections.push("Lean slightly forward, chest proud");
        feedback.score -= 10;
      }

      // Check foot stability
      const footStability = Math.abs(leftAnkle.x - rightAnkle.x);
      if (footStability < 0.1 || footStability > 0.3) {
        feedback.issues.push("Foot placement off");
        feedback.corrections.push("Keep feet shoulder-width apart");
        feedback.score -= 10;
      }

    } catch (error) {
      console.error('Error analyzing squat form:', error);
    }

    return feedback;
  };

  const analyzePushupForm = (pose: any): FormFeedback => {
    const feedback: FormFeedback = {
      score: 100,
      issues: [],
      corrections: [],
      repCount: formFeedback.repCount
    };

    if (!pose?.landmarks) return feedback;

    try {
      // Push-up specific analysis would go here
      const leftShoulder = pose.landmarks[11];
      const rightShoulder = pose.landmarks[12];
      const leftElbow = pose.landmarks[13];
      const rightElbow = pose.landmarks[14];
      const leftWrist = pose.landmarks[15];
      const rightWrist = pose.landmarks[16];

      // Check arm alignment
      const elbowAlignment = Math.abs(leftElbow.y - rightElbow.y);
      if (elbowAlignment > 0.1) {
        feedback.issues.push("Uneven arm position");
        feedback.corrections.push("Keep both arms at same height");
        feedback.score -= 15;
      }

      // Check wrist position
      const wristWidth = Math.abs(leftWrist.x - rightWrist.x);
      if (wristWidth < 0.15) {
        feedback.issues.push("Hands too close");
        feedback.corrections.push("Place hands shoulder-width apart");
        feedback.score -= 10;
      }

    } catch (error) {
      console.error('Error analyzing pushup form:', error);
    }

    return feedback;
  };

  useEffect(() => {
    if (poses.length === 0) return;

    const latestPose = poses[poses.length - 1];
    let newFeedback: FormFeedback;

    switch (exercise.toLowerCase()) {
      case 'air squat x5':
      case 'squat':
        newFeedback = analyzeSquatForm(latestPose);
        break;
      case 'pushup':
      case 'push-up':
        newFeedback = analyzePushupForm(latestPose);
        break;
      default:
        newFeedback = {
          score: 85,
          issues: [],
          corrections: ["Maintain good posture"],
          repCount: 0
        };
    }

    setFormFeedback(newFeedback);
    if (onFormFeedback) {
      onFormFeedback(newFeedback);
    }
  }, [poses, exercise, onFormFeedback]);

  const getScoreColor = (score: number) => {
    if (score >= 90) return "bg-green-500";
    if (score >= 70) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getScoreText = (score: number) => {
    if (score >= 90) return "Excellent Form";
    if (score >= 70) return "Good Form";
    return "Needs Improvement";
  };

  return (
    <div className="space-y-3" data-testid="posture-analysis">
      {/* Form Score */}
      <Card className="p-3 bg-card/90 backdrop-blur-sm">
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs font-medium text-muted-foreground">Form Score</span>
          <Badge className={`${getScoreColor(formFeedback.score)} text-white`}>
            {formFeedback.score}/100
          </Badge>
        </div>
        <div className="text-sm font-medium" data-testid="text-form-score">
          {getScoreText(formFeedback.score)}
        </div>
      </Card>

      {/* Rep Counter */}
      <Card className="p-3 bg-card/90 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <span className="text-xs font-medium text-muted-foreground">Reps Completed</span>
          <div className="text-lg font-bold text-primary" data-testid="text-rep-counter">
            {formFeedback.repCount}/5
          </div>
        </div>
        <div className="w-full bg-muted rounded-full h-2 mt-2">
          <div 
            className="bg-primary h-2 rounded-full transition-all duration-300"
            style={{ width: `${(formFeedback.repCount / 5) * 100}%` }}
          />
        </div>
      </Card>

      {/* Form Issues & Corrections */}
      {formFeedback.issues.length > 0 && (
        <Card className="p-3 bg-card/90 backdrop-blur-sm">
          <div className="text-xs font-medium text-muted-foreground mb-2">Form Corrections</div>
          <div className="space-y-1">
            {formFeedback.corrections.map((correction, index) => (
              <div 
                key={index} 
                className="text-xs text-orange-600 dark:text-orange-400 flex items-start space-x-1"
                data-testid={`correction-${index}`}
              >
                <span className="text-orange-500">•</span>
                <span>{correction}</span>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Success Message */}
      {formFeedback.score >= 90 && formFeedback.issues.length === 0 && (
        <Card className="p-3 bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800">
          <div className="text-sm text-green-700 dark:text-green-300 font-medium">
            ✅ Perfect form! Keep it up!
          </div>
        </Card>
      )}
    </div>
  );
}