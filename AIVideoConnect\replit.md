# AI Fitness Assessment Platform

## Overview

This is a FaceTime-style AI fitness assessment platform that conducts personalized initial fitness evaluations through real-time video calls. The system combines voice analysis, pose detection, and conversational AI to assess user fitness levels and generate tailored workout plans. Users interact with a holographic AI trainer avatar that guides them through discovery questions, movement assessments, and provides personalized recommendations with confidence scores.

## User Preferences

Preferred communication style: Simple, everyday language.

## System Architecture

### Frontend Architecture
- **Framework**: React with TypeScript using Vite for build tooling
- **UI Library**: shadcn/ui components built on Radix UI primitives
- **Styling**: Tailwind CSS with custom design tokens for dark theme
- **State Management**: Zustand for assessment state, React Query for server state
- **Routing**: Wouter for lightweight client-side routing
- **3D Graphics**: Three.js for the holographic AI trainer orb avatar with WebGL shaders

### Backend Architecture
- **Runtime**: Node.js with Express.js server
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **WebSocket**: Real-time communication for video call functionality and live data streaming
- **API Design**: RESTful endpoints with structured error handling and request logging

### Real-time Communication
- **Video/Audio**: WebRTC for peer-to-peer video calling with STUN servers
- **Speech Processing**: Web Speech API for real-time speech-to-text transcription
- **Pose Detection**: MediaPipe integration for movement analysis and form assessment
- **Audio Analysis**: Web Audio API for voice prosody and RMS analysis to drive avatar animations

### AI Integration
- **LLM**: OpenAI GPT-5 for intelligent conversation flow and workout plan generation
- **Assessment Logic**: Custom scoring algorithm calculating Time Capacity, Movement Quality, Recovery Readiness, Environmental Fit, and Adherence Signals to produce Goal Attainability Score (GAS)
- **Avatar Animation**: Audio-driven holographic orb with state-based color palettes and particle effects

### Data Model
- **Users**: Authentication and subscription management with Stripe integration
- **Assessment Sessions**: Multi-phase evaluation tracking (welcome → discovery → movement → photo → reveal)
- **Session Events**: Granular event logging for speech, movement, and phase transitions
- **Workout Plans**: Generated fitness programs with coaching tier options

### Database Schema
- Utilizes PostgreSQL with UUID primary keys and JSONB for flexible data storage
- Tracks user progression through assessment phases with intake data and biometric signals
- Stores workout plans with coaching options and Stripe subscription details

### Payment Integration
- Stripe integration for subscription management and payment processing
- Support for coached vs. self-guided workout plan tiers
- Customer and subscription ID tracking linked to user accounts

## External Dependencies

### Core Services
- **Neon Database**: PostgreSQL hosting with serverless connection pooling
- **OpenAI API**: GPT-5 for conversational AI and workout plan generation
- **Stripe**: Payment processing and subscription management

### Media Processing
- **MediaPipe Pose**: Real-time pose estimation and movement analysis
- **Web Speech API**: Browser-native speech recognition
- **WebRTC**: Peer-to-peer video communication

### Development Tools
- **Replit Platform**: Development environment with cartographer and dev banner plugins
- **Drizzle Kit**: Database migrations and schema management
- **ESBuild**: Production bundling for server-side code

### UI/UX Libraries
- **Radix UI**: Accessible component primitives
- **Three.js**: 3D graphics rendering for avatar
- **Lucide React**: Icon library
- **React Hook Form**: Form validation with Zod schemas