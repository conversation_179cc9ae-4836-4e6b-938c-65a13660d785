/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The status of the user's subscription.
 */
export type SubscriptionStatus = "trialing" | "active" | "incomplete" | "incomplete_expired" | "past_due" | "canceled" | "unpaid" | "free";
export declare const SubscriptionStatus: {
    readonly Trialing: "trialing";
    readonly Active: "active";
    readonly Incomplete: "incomplete";
    readonly IncompleteExpired: "incomplete_expired";
    readonly PastDue: "past_due";
    readonly Canceled: "canceled";
    readonly Unpaid: "unpaid";
    readonly Free: "free";
};
