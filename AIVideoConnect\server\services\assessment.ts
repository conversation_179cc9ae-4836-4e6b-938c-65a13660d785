import { AssessmentSession } from "@shared/schema";
import { analyzeIntakeData, generateWorkoutPlan, analyzeMovementQuality } from "./openai";

interface AssessmentScores {
  TC: number; // Time Capacity
  MQ: number; // Movement Quality
  RR: number; // Recovery Readiness
  EF: number; // Environmental Fit
  AS: number; // Adherence Signals
  GAS: number; // Goal Attainability Score
}

class AssessmentService {
  async calculateScores(session: AssessmentSession): Promise<AssessmentScores> {
    const { intake, signals } = session;
    
    // Calculate Time Capacity (0-100)
    const TC = this.calculateTimeCapacity(intake);
    
    // Calculate Movement Quality (0-100) - requires movement analysis  
    const MQ = await this.calculateMovementQuality(signals && (signals as any).movement ? (signals as any).movement : null);
    
    // Calculate Recovery Readiness (0-100)
    const RR = this.calculateRecoveryReadiness(intake);
    
    // Calculate Environmental Fit (0-100)
    const EF = this.calculateEnvironmentalFit(intake);
    
    // Calculate Adherence Signals (0-100)
    const AS = this.calculateAdherenceSignals(intake, signals);
    
    // Calculate Goal Attainability Score (weighted blend)
    const GAS = Math.round(0.28 * TC + 0.22 * MQ + 0.20 * RR + 0.15 * EF + 0.15 * AS);
    
    return { TC, MQ, RR, EF, AS, GAS };
  }

  private calculateTimeCapacity(intake: any): number {
    if (!intake?.time_days || !intake?.time_minutes) return 50;
    
    const weeklyMinutes = intake.time_days * intake.time_minutes;
    
    // Scale based on minimum effective volume
    if (weeklyMinutes >= 150) return 100;
    if (weeklyMinutes >= 120) return 85;
    if (weeklyMinutes >= 90) return 70;
    if (weeklyMinutes >= 60) return 55;
    return 40;
  }

  private async calculateMovementQuality(movementData: any): Promise<number> {
    if (!movementData) return 60; // Default if no movement data
    
    try {
      const analysis = await analyzeMovementQuality(movementData);
      
      // Convert analysis scores to 0-100 scale
      const scores = Object.values(analysis.scores) as number[];
      const avgScore = scores.reduce((a: number, b: number) => a + b, 0) / scores.length;
      return Math.max(0, Math.min(100, avgScore));
    } catch (error) {
      console.error('Movement quality analysis failed:', error);
      return 60;
    }
  }

  private calculateRecoveryReadiness(intake: any): number {
    let score = 50;
    
    if (intake?.sleep_hours) {
      if (intake.sleep_hours >= 8) score += 25;
      else if (intake.sleep_hours >= 7) score += 15;
      else if (intake.sleep_hours >= 6) score += 5;
      else score -= 15;
    }
    
    if (intake?.stress_1_5) {
      const stressImpact = (5 - intake.stress_1_5) * 5; // Lower stress = higher score
      score += stressImpact;
    }
    
    return Math.max(0, Math.min(100, score));
  }

  private calculateEnvironmentalFit(intake: any): number {
    let score = 50;
    
    // Equipment access
    if (intake?.equipment) {
      if (intake.equipment.includes('full gym')) score += 30;
      else if (intake.equipment.includes('DB')) score += 20;
      else if (intake.equipment.includes('bands')) score += 10;
    }
    
    // Schedule stability (inferred from consistency responses)
    if (intake?.schedule_consistent) score += 20;
    
    return Math.max(0, Math.min(100, score));
  }

  private calculateAdherenceSignals(intake: any, signals: any): number {
    let score = 50;
    
    // Past adherence history
    if (intake?.history && !intake.history.includes('start/stop')) score += 20;
    
    // Accountability preference
    if (intake?.accountability_yes) score += 15;
    
    // Engagement signals from voice/face analysis
    if (signals?.voice?.engagement > 0.7) score += 10;
    if (signals?.face?.engagement > 0.7) score += 5;
    
    return Math.max(0, Math.min(100, score));
  }

  async generatePlan(session: AssessmentSession, scores: AssessmentScores): Promise<any> {
    try {
      const plan = await generateWorkoutPlan(session, scores);
      
      // Add success probability and improvement suggestions
      plan.gasScore = scores.GAS;
      plan.improvementSuggestions = this.generateImprovementSuggestions(scores);
      
      return plan;
    } catch (error) {
      console.error('Plan generation failed:', error);
      return this.generateFallbackPlan(session, scores);
    }
  }

  private generateImprovementSuggestions(scores: AssessmentScores): string[] {
    const suggestions = [];
    
    if (scores.TC < 70) {
      suggestions.push("Adding 1 more training day could increase success by 8-12%");
    }
    
    if (scores.MQ < 70) {
      suggestions.push("Weekly form check-ins could improve movement quality by 15-20%");
    }
    
    if (scores.RR < 60) {
      suggestions.push("Improving sleep to 7+ hours could boost recovery by 20%");
    }
    
    return suggestions;
  }

  private generateFallbackPlan(session: AssessmentSession, scores: AssessmentScores): any {
    return {
      gasScore: scores.GAS,
      weeklySchedule: {
        days: 3,
        sessions: ["Strength A", "Conditioning", "Strength B"]
      },
      sampleWorkout: {
        name: "Strength A",
        duration: "30 minutes",
        exercises: [
          { name: "Goblet Squats", sets: "3x8", rpe: "7" },
          { name: "Push-ups", sets: "3xAMRAP", rpe: "8" },
          { name: "Bent Over Rows", sets: "3x10", rpe: "7" }
        ]
      },
      nutritionGuidelines: {
        pattern: "3 meals + 1 snack",
        proteinTarget: "0.8-1.0g per lb goal weight",
        calorieDeficit: "300-500 calories"
      },
      progressForecast: {
        week4: "-3 to -5 lbs",
        week8: "-6 to -9 lbs",
        week12: "-8 to -12 lbs"
      }
    };
  }
}

export const assessmentService = new AssessmentService();
