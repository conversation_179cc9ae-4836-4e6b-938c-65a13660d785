{"hash": "3ef59965", "configHash": "e9789ae3", "lockfileHash": "8d5a0bda", "browserHash": "43778705", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1b850b41", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "b2c11ec6", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "fd26fd0a", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "7df6e3cc", "needsInterop": true}, "@mediapipe/pose": {"src": "../../@mediapipe/pose/pose.js", "file": "@mediapipe_pose.js", "fileHash": "be40babd", "needsInterop": true}, "@radix-ui/react-collapsible": {"src": "../../@radix-ui/react-collapsible/dist/index.mjs", "file": "@radix-ui_react-collapsible.js", "fileHash": "2cf26360", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "d34e2e78", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "2a38b434", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "26abf466", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "fbc4eb70", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "ecb0f977", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "9a0f5109", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "91a67b23", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "2de70f78", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "fa854693", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "bb933251", "needsInterop": false}, "three": {"src": "../../three/build/three.module.js", "file": "three.js", "fileHash": "e84794c9", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "c677e757", "needsInterop": false}}, "chunks": {"chunk-4S5NYQX5": {"file": "chunk-4S5NYQX5.js"}, "chunk-KNXW6S4W": {"file": "chunk-KNXW6S4W.js"}, "chunk-CZ4CEG32": {"file": "chunk-CZ4CEG32.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-R6W36WTT": {"file": "chunk-R6W36WTT.js"}, "chunk-W4HD6ASN": {"file": "chunk-W4HD6ASN.js"}, "chunk-FW7CIZRJ": {"file": "chunk-FW7CIZRJ.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-5S42QOQO": {"file": "chunk-5S42QOQO.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}