{"hash": "c006a0fb", "configHash": "e9789ae3", "lockfileHash": "25504308", "browserHash": "db20606e", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "80092745", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "5c8d60a5", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "48ee6ee1", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "eaccf92f", "needsInterop": true}, "@mediapipe/pose": {"src": "../../@mediapipe/pose/pose.js", "file": "@mediapipe_pose.js", "fileHash": "1948e6ab", "needsInterop": true}, "@radix-ui/react-collapsible": {"src": "../../@radix-ui/react-collapsible/dist/index.mjs", "file": "@radix-ui_react-collapsible.js", "fileHash": "e94e39dd", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "9e09a532", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "41536f15", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "39521e0a", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "058ab42d", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "2a854128", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "a04b7bc4", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "0d6aab19", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "1ed923f3", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "33e1a73b", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "02de84ea", "needsInterop": false}, "three": {"src": "../../three/build/three.module.js", "file": "three.js", "fileHash": "505e4731", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "a185dc86", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-JOKKP55V": {"file": "chunk-JOKKP55V.js"}, "chunk-45UKLGHN": {"file": "chunk-45UKLGHN.js"}, "chunk-XWBQ23N5": {"file": "chunk-XWBQ23N5.js"}, "chunk-36PDMQJ5": {"file": "chunk-36PDMQJ5.js"}, "chunk-B2JJSBLR": {"file": "chunk-B2JJSBLR.js"}, "chunk-TP3OJ47U": {"file": "chunk-TP3OJ47U.js"}, "chunk-5S42QOQO": {"file": "chunk-5S42QOQO.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}