/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Latency optimization level (deprecated)
 */
export type StreamInputQueryParametersOptimizeStreamingLatency = "0" | "1" | "2" | "3" | "4";
export declare const StreamInputQueryParametersOptimizeStreamingLatency: {
    readonly Zero: "0";
    readonly One: "1";
    readonly Two: "2";
    readonly Three: "3";
    readonly Four: "4";
};
