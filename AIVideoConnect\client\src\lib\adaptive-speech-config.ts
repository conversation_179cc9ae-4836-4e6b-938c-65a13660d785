interface SpeechMetrics {
  confidence: number;
  duration: number;
  errorCount: number;
  successCount: number;
  avgConfidence: number;
  lastError?: string;
}

interface AdaptiveSpeechConfig {
  useWhisper: boolean;
  recordingDuration: number;
  language: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  noiseReduction: boolean;
  echoCancellation: boolean;
  autoGainControl: boolean;
  sampleRate: number;
  retryAttempts: number;
  confidenceThreshold: number;
}

export class AdaptiveSpeechManager {
  private metrics: SpeechMetrics = {
    confidence: 0,
    duration: 0,
    errorCount: 0,
    successCount: 0,
    avgConfidence: 0.8
  };

  private baseConfig: AdaptiveSpeechConfig = {
    useWhisper: true,
    recordingDuration: 5000,
    language: 'en-US',
    continuous: true,
    interimResults: true,
    maxAlternatives: 3,
    noiseReduction: true,
    echoCancellation: true,
    autoGainControl: true,
    sampleRate: 16000,
    retryAttempts: 3,
    confidenceThreshold: 0.6
  };

  private currentConfig: AdaptiveSpeechConfig = { ...this.baseConfig };

  constructor() {
    this.loadMetrics();
  }

  private loadMetrics() {
    try {
      const saved = localStorage.getItem('speech-metrics');
      if (saved) {
        this.metrics = { ...this.metrics, ...JSON.parse(saved) };
      }
    } catch (error) {
      console.warn('Failed to load speech metrics:', error);
    }
  }

  private saveMetrics() {
    try {
      localStorage.setItem('speech-metrics', JSON.stringify(this.metrics));
    } catch (error) {
      console.warn('Failed to save speech metrics:', error);
    }
  }

  recordSuccess(confidence: number, duration: number) {
    this.metrics.successCount++;
    this.metrics.confidence = confidence;
    this.metrics.duration = duration;
    
    // Update rolling average confidence
    const totalAttempts = this.metrics.successCount + this.metrics.errorCount;
    this.metrics.avgConfidence = (
      (this.metrics.avgConfidence * (totalAttempts - 1) + confidence) / totalAttempts
    );

    this.adaptConfiguration();
    this.saveMetrics();
    
    console.log('🎯 Speech success recorded:', {
      confidence,
      duration,
      avgConfidence: this.metrics.avgConfidence,
      totalSuccess: this.metrics.successCount
    });
  }

  recordError(error: string) {
    this.metrics.errorCount++;
    this.metrics.lastError = error;
    
    this.adaptConfiguration();
    this.saveMetrics();
    
    console.log('❌ Speech error recorded:', {
      error,
      errorCount: this.metrics.errorCount,
      successRate: this.getSuccessRate()
    });
  }

  private adaptConfiguration() {
    const successRate = this.getSuccessRate();
    const avgConfidence = this.metrics.avgConfidence;
    
    console.log('🔧 Adapting speech configuration:', { successRate, avgConfidence });

    // Reset to base config
    this.currentConfig = { ...this.baseConfig };

    // Adapt based on success rate
    if (successRate < 0.3) {
      // Very poor performance - use most conservative settings
      console.log('📉 Poor performance detected - using conservative settings');
      this.currentConfig.useWhisper = true; // Prefer Whisper for accuracy
      this.currentConfig.recordingDuration = 3000; // Shorter recordings
      this.currentConfig.continuous = false; // Disable continuous mode
      this.currentConfig.retryAttempts = 5; // More retries
      this.currentConfig.confidenceThreshold = 0.4; // Lower threshold
      this.currentConfig.noiseReduction = true;
      this.currentConfig.echoCancellation = true;
      this.currentConfig.autoGainControl = true;
    } else if (successRate < 0.6) {
      // Moderate performance - balanced settings
      console.log('📊 Moderate performance - using balanced settings');
      this.currentConfig.useWhisper = true;
      this.currentConfig.recordingDuration = 4000;
      this.currentConfig.continuous = false;
      this.currentConfig.retryAttempts = 3;
      this.currentConfig.confidenceThreshold = 0.5;
    } else if (successRate > 0.8 && avgConfidence > 0.8) {
      // Excellent performance - optimize for speed
      console.log('🚀 Excellent performance - optimizing for speed');
      this.currentConfig.useWhisper = false; // Use browser API for speed
      this.currentConfig.recordingDuration = 6000; // Longer recordings
      this.currentConfig.continuous = true;
      this.currentConfig.retryAttempts = 2;
      this.currentConfig.confidenceThreshold = 0.7;
    }

    // Adapt based on specific error patterns
    if (this.metrics.lastError) {
      const error = this.metrics.lastError.toLowerCase();
      
      if (error.includes('no speech') || error.includes('not found')) {
        console.log('🔊 Adapting for speech detection issues');
        this.currentConfig.recordingDuration = Math.min(this.currentConfig.recordingDuration + 1000, 8000);
        this.currentConfig.confidenceThreshold = Math.max(this.currentConfig.confidenceThreshold - 0.1, 0.3);
        this.currentConfig.autoGainControl = true;
      } else if (error.includes('network') || error.includes('connection')) {
        console.log('🌐 Adapting for network issues');
        this.currentConfig.useWhisper = false; // Use browser API to avoid network calls
        this.currentConfig.retryAttempts = Math.min(this.currentConfig.retryAttempts + 2, 6);
      } else if (error.includes('permission') || error.includes('not allowed')) {
        console.log('🔒 Permission issues detected');
        // Can't adapt much for permission issues, but ensure we're using the most compatible settings
        this.currentConfig.useWhisper = false;
      }
    }

    // Adapt based on average confidence
    if (avgConfidence < 0.5) {
      console.log('🎯 Low confidence detected - enhancing accuracy');
      this.currentConfig.useWhisper = true; // Whisper generally more accurate
      this.currentConfig.maxAlternatives = 5; // More alternatives
      this.currentConfig.confidenceThreshold = 0.3; // Lower threshold
    }

    console.log('✅ Adapted configuration:', this.currentConfig);
  }

  getConfiguration(): AdaptiveSpeechConfig {
    return { ...this.currentConfig };
  }

  getSuccessRate(): number {
    const total = this.metrics.successCount + this.metrics.errorCount;
    return total > 0 ? this.metrics.successCount / total : 0;
  }

  getMetrics(): SpeechMetrics {
    return { ...this.metrics };
  }

  reset() {
    this.metrics = {
      confidence: 0,
      duration: 0,
      errorCount: 0,
      successCount: 0,
      avgConfidence: 0.8
    };
    this.currentConfig = { ...this.baseConfig };
    this.saveMetrics();
    console.log('🔄 Speech metrics and configuration reset');
  }

  // Get user-friendly recommendations
  getRecommendations(): string[] {
    const recommendations: string[] = [];
    const successRate = this.getSuccessRate();
    const avgConfidence = this.metrics.avgConfidence;

    if (successRate < 0.3) {
      recommendations.push('Try speaking more clearly and slowly');
      recommendations.push('Ensure you\'re in a quiet environment');
      recommendations.push('Check your microphone settings and permissions');
    } else if (successRate < 0.6) {
      recommendations.push('Speak directly into your microphone');
      recommendations.push('Reduce background noise if possible');
    }

    if (avgConfidence < 0.5) {
      recommendations.push('Try speaking louder and more clearly');
      recommendations.push('Use shorter, simpler phrases');
    }

    if (this.metrics.lastError?.includes('no speech')) {
      recommendations.push('Make sure your microphone is not muted');
      recommendations.push('Try speaking immediately when recording starts');
    }

    return recommendations;
  }
}

// Global instance
export const adaptiveSpeechManager = new AdaptiveSpeechManager();
