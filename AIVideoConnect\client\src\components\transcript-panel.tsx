import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface TranscriptPanelProps {
  isOpen: boolean;
  onClose: () => void;
  transcript: string;
  session: any;
}

export default function TranscriptPanel({ isOpen, onClose, transcript, session }: TranscriptPanelProps) {
  const getKeyFacts = () => {
    if (!session?.intake) return [];
    
    const facts = [];
    if (session.intake.time_days) facts.push(`${session.intake.time_days} days/week`);
    if (session.intake.time_minutes) facts.push(`${session.intake.time_minutes}min sessions`);
    if (session.intake.equipment) facts.push(session.intake.equipment.join(', '));
    if (session.intake.goal_text) facts.push(session.intake.goal_text);
    
    return facts;
  };

  const transcriptLines = transcript.split('\n').filter(line => line.trim());

  return (
    <div 
      className={`fixed right-0 top-20 bottom-6 w-80 bg-card border-l border-border transform transition-transform duration-300 z-40 ${
        isOpen ? 'translate-x-0' : 'translate-x-full'
      }`}
      data-testid="panel-transcript"
    >
      <div className="p-6 h-full flex flex-col">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-display font-semibold text-lg">Live Session</h3>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={onClose}
            data-testid="button-close-transcript"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </Button>
        </div>
        
        {/* Key Facts Captured */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-muted-foreground mb-3">Key Facts</h4>
          <div className="flex flex-wrap gap-2">
            {getKeyFacts().map((fact, index) => (
              <Badge 
                key={index}
                variant="secondary"
                className="text-xs"
                data-testid={`badge-fact-${index}`}
              >
                {fact}
              </Badge>
            ))}
          </div>
        </div>
        
        {/* Live Transcript */}
        <div className="flex-1 overflow-hidden">
          <h4 className="text-sm font-medium text-muted-foreground mb-3">Transcript</h4>
          <div className="space-y-3 overflow-y-auto h-full" data-testid="transcript-content">
            {transcriptLines.length > 0 ? (
              transcriptLines.map((line, index) => (
                <div key={index} className="flex space-x-2">
                  <div className="w-6 h-6 rounded-full bg-accent flex-shrink-0 flex items-center justify-center">
                    <div className="w-2 h-2 bg-accent-foreground rounded-full"></div>
                  </div>
                  <div className="flex-1">
                    <div className="text-xs text-muted-foreground">You</div>
                    <div className="text-sm">{line}</div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-sm text-muted-foreground text-center">
                Start speaking to see transcript...
              </div>
            )}
          </div>
        </div>
        
        {/* Micro Offers */}
        <div className="mt-4 pt-4 border-t border-border">
          <h4 className="text-sm font-medium text-muted-foreground mb-3">Available Add-ons</h4>
          <div className="space-y-2">
            <Button 
              variant="outline" 
              className="w-full justify-start text-left h-auto p-3"
              data-testid="button-weekly-checkins"
            >
              <div>
                <div className="text-sm font-medium text-accent">Weekly Check-ins</div>
                <div className="text-xs text-muted-foreground">Stay on track with progress reviews</div>
              </div>
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start text-left h-auto p-3"
              data-testid="button-form-tuneups"
            >
              <div>
                <div className="text-sm font-medium text-primary">Form Tune-ups</div>
                <div className="text-xs text-muted-foreground">Perfect your technique weekly</div>
              </div>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
