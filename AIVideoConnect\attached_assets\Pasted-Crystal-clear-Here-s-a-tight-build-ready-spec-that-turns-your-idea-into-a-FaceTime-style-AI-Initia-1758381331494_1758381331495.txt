Crystal clear. Here’s a tight, build-ready spec that turns your idea into a FaceTime-style AI Initial Assessment that collects rich signals (voice, face, form), runs a proper fitness eval, subtly sells PT, and ends with a brief, punchy plan + % odds to hit the goal—all from photos + live call (no tape-measure hassle).

1) Call UI & Interaction Model
Call window (responsive)

Modes: Vertical (default, phone-like) ⇄ Horizontal ⇄ Picture-in-Picture.

Split: Two video panes:

Left/Top: AI Trainer avatar (TTS + lip-sync) with cue cards (short text prompts, rep counts).

Right/Bottom: User camera feed with real-time overlays:

Pose skeleton + joint angles during form tests

Subtle face/affect markers during Q&A (no medical claims)

Controls (persistent top bar):

End, Mute mic, Mute AI voice, Recenter, Flip camera, Torch (mobile), Privacy toggle (turn off face/affect analysis without ending call)

Context panel (slide-in drawer):

Live transcript (Whisper)

Key facts captured (chips)

Micro-offers (“Weekly Check-ins”, “Form Tune-ups”) with learn-more

Action footer:

“Start Assessment” → “Form Check” → “Preview Plan” → “Save & Upgrade”

2) Session Flow (what actually happens)

P0 goals: build trust, extract real constraints, demonstrate value with visible analysis, present plan + probability, give a natural upsell.

Warm-Up (2–3 min)

AI mirrors name, goal, vibe; sets expectations:

“We’ll ask smart questions, watch a few simple moves, then I’ll show a short plan and your % odds based on today.”

Discovery Q&A (6–8 min) — fine-tuned, specific, free-response captured by Whisper

Constraints: schedule (days, minutes per day), equipment, pain/injury, sleep, stress, diet guardrails.

Levers: preferred training styles, past adherence, social support, accountability preference.

Emotion capture: prosody (pace, pitch variability), facial engagement (attention/probable confidence). Store as soft signals (0-1) with consent.

Movement Screen (5–7 min)

Three simple tests (phone can sit on table):

Air Squat x5 (knee valgus, depth proxy, trunk angle)

Hip Hinge x5 (spine neutrality, ROM)

Shoulder Flexion wall test (scap control proxy)

Auto rep count + red/yellow/green flags; brief corrective cues.

Log mobility, stability, motor control scores (0–100 each).

Photo Capture (1–2 min)

Front / Side photos (shirt fitted/consistent light).

Extract: posture deltas, silhouette ratios, body-fat estimate band (wide, not diagnostic).

Cache for future-look render later (optional).

Reveal (2–3 min)

BRIEF plan preview (1 screen): split days, focus areas, calories/meal pattern, mobility block.

Goal Attainability % + why (plain language).

Delta chart: “4 weeks → likely −3–6 lb” (example), “Strength +10–15% on key lifts”, “Sleep target +30 min”.

Soft Upsell (1–2 min)

“You’ll crush this. If you want me to check form weekly and auto-adjust to keep that % high, that lives on the Coached plan. Try 7-day free?”

Two buttons: Keep Basic / Start Coached Trial

3) What the AI asks (laser-focused)

Minimal set, high signal:

Goal framing: “What’s the one change you want in 8–12 weeks?” (free text)

Time: “How many days/week & minutes/day are realistic?” (chip selection + free text)

Equipment: none / bands / DB / full gym (chips)

History: “What made you stall last time?” (motivation, injuries, life constraints)

Sleep/Stress: “Avg sleep hours?”, “Rate current stress 1–5”

Diet guardrails: allergies, preferences, budget pressure (low/mod/high)

Accountability appetite: “Want weekly check-ins & video form checks?” (yes/no)

Every free-form answer is mirrored back in one line to build trust and to seed the upsell (“You said evenings, 30 min, with knee soreness and want visible arms by 12 weeks—I’ve got you.”)

4) Subtle Sales Layer (behavioral cues)

Moments we nudge value:

After a good corrective: “This cue added 10–15° depth safely. I can tune that weekly so progress never stalls.” → [Learn about Coached]

When % is shown: “We can raise your probability from 62% → ~78% by 1) 2× 10-min mobility boosters, 2) Sunday batch-cook, 3) a weekly 10-min form check (Coached).”

During plan save: “Want me to auto-adjust next week based on your sleep & reps? That’s included in Coached. 7-day free.”

No pushy copy, just competence + option.

5) The “BRIEF but awesome” Plan (exact output)

One screen. Zero fluff.

Goal: Lose ~8–12 lb in 12 weeks; build visible delts/arms.

Confidence to goal: 62% (can reach 78% with weekly tune-ups)

Weekly layout: 4 days (2 Lift, 1 Conditioning, 1 Mobility)

Today → Week 2 focus: Squat pattern + hinge capacity; shoulder flexion control

Session template (Lift A, 28–32 min):

Goblet Squat 4×8 @ RPE 7 (2 min cap)

DB Romanian Deadlift 3×10 @ RPE 7

Push-Up (elevated if needed) 3×AMRAP, stop 2 reps in reserve

Shoulder Flexion Wall Slides 2×12 (tempo 311)

Finisher: Step-ups 2×45s / 30s rest

Conditioning (20–24 min): 4 rounds: brisk walk/jog 3 min + incline 2 min

Mobility (10 min on off-days): 90/90 x10/side, T-spine openers x8/side, ankle rocks x12/side

Food pattern: 3 meals + 1 snack; ~500 kcal/day deficit (est)

Protein target: 0.8–1.0 g/lb goal body weight

Simple plate rule shown with images

Forecast (conservative):

4 weeks: −3–6 lb | 8 weeks: −6–10 lb | 12 weeks: −8–12 lb

Strength: +10–15% on squat/RDL

Next steps: Save plan | Add Coached weekly tune-ups (7-day free)

(Numbers adapt to their time/equipment/photos; no measurements required.)

6) How we compute the Goal Attainability % (transparent & fair)

GAS (0–100) = weighted blend of five pillars:

Time Capacity (TC, 0–100): mapping of (days × minutes) to minimal effective volume for the goal.

Movement Quality (MQ, 0–100): from form tests (valgus, depth, control).

Recovery Readiness (RR, 0–100): sleep hours + reported stress.

Environmental Fit (EF, 0–100): equipment access + schedule stability.

Adherence Signals (AS, 0–100): consistency history + accountability appetite + soft affect (engagement/tone).

Example weights (tunable/learned):
GAS = 0.28·TC + 0.22·MQ + 0.20·RR + 0.15·EF + 0.15·AS

Raise % suggestions: Produced by sensitivity analysis (“+1 day of 20 min raises GAS ~+7pts”; “weekly check-in adds +6–10pts historically”).

Calorie/weight forecast

Estimate TDEE from photos + self-report (broad band), apply deficit from plan → convert to weekly lb using 3,000–3,500 kcal/lb range; show ranges (not promises).

7) Signals we capture (no tape measure)

Photos: posture angles (head fwd, pelvic tilt proxy), silhouette ratios, BF band (wide CI), confidence of estimate.

Form tests: knee valgus deg proxy, hip hinge depth, scap control pass/fail; rep speed uniformity.

Voice: speech rate variance, hesitation markers (ASR conf), prosody variability → engagement proxy.

Face: attention/probable confidence (blink rate, gaze on/off) → engagement proxy.
Always opt-in, clear consent, “turn off visual analysis” toggle.

8) Technical blueprint (so devs can build)

Frontend

React Native (mobile) / React (web)

WebRTC (e.g., Agora/Twilio) for low-latency AV

Canvas/WebGL overlays for pose skeleton; orientation-aware layout

Local fallback if network degrades (audio-only coaching)

AI/Media

STT: Whisper (streaming)

LLM: Your chat brain (for dialog, plan synthesis, upsell timing)

TTS: ElevenLabs/Apple TTS (low-latency)

Avatar: D-ID / Ready Player Me (+ viseme stream for lip-sync)

Pose: MediaPipe/MoveNet (on-device if possible; else edge)

Face/Voice features: lightweight, non-diagnostic engagement metrics

Backend (FastAPI)

/session/start, /session/events (Q&A, form start/stop), /session/end

/assessment/score → returns MQ, RR, EF, AS, TC, GAS

/plan/generate → returns “brief awesome plan” object

/offer/recommend → returns upgrade copy + price experiment bucket

/photo/analyze → posture/BF-band (asynchronous job)

/render/futurelook (optional, async)

Data model (key objects)

{
  "user_id": "uuid",
  "call_id": "uuid",
  "intake": {
    "goal_text": "visible arms, −10 lb in 12 wks",
    "time_days": 4,
    "time_minutes": 30,
    "equipment": ["DB"],
    "sleep_hours": 6.5,
    "stress_1_5": 3,
    "history": "start/stop, knee soreness",
    "accountability_yes": true
  },
  "signals": {
    "pose": {"squat_valgus_deg": 14, "depth_ok": true, "hinge_rom_ok": false},
    "voice": {"prosody_var": 0.61, "speech_rate": 130},
    "face": {"engagement": 0.72}
  },
  "scores": {"TC": 72, "MQ": 64, "RR": 58, "EF": 80, "AS": 70, "GAS": 66},
  "plan": { /* brief plan payload as shown above */ },
  "offers": [{"id":"coached_weekly","copy":"Raise your % to 78% with weekly tune-ups","trial_days":7}]
}


Call loop (pseudo):

onCallStart():
  initWebRTC()
  initWhisperStream()
  avatar.say("Ready to begin? We'll keep this simple and powerful.")

phase = "discovery"
while (callActive):
  if (phase === "discovery"):
    q = nextQuestion(intake_state)
    avatar.ask(q.prompt)
    ans = await stt.capture()
    updateIntake(ans)
    maybeSoftSell(ans) // if user mentions accountability, show chip
    if (intakeComplete) phase = "form"

  if (phase === "form"):
    runPoseTest("squat")
    giveCueIfNeeded()
    runPoseTest("hinge")
    runPoseTest("shoulder")
    phase = "photo"

  if (phase === "photo"):
    promptPhotoCapture()
    enqueuePhotoAnalysis()
    phase = "reveal"

  if (phase === "reveal"):
    scores = scoreAssessment(intake, signals)
    plan = generateBriefPlan(intake, scores)
    showPlan(plan, scores.GAS)
    suggestUpsell(scores)
    phase = "close"


Latency targets

STT partials < 300 ms

TTS play start < 500 ms

Pose overlay < 100 ms from camera frame

Privacy & consent

“Analyze face/voice for engagement?” Opt-in (default off if region requires)

Clear data retention settings; photos optional and deletable.

9) Example upsell lines (tasteful, trainer-like)

“Based on today, you’re at 62% to hit the 12-week goal on your own. With weekly 10-minute tune-ups, we typically raise that to the high-70s. Want me to handle those adjustments for you?”

“You did great. If I watch your squat once a week and tweak loads/tempo, plateaus don’t stick. That’s what Coached is for—first week’s on me.”

10) Risks & mitigations (so we don’t get burned)

False precision: Always show ranges and a “projection, not a promise” note.

Lighting/space limits: Provide 10-second setup guide; degrade gracefully (audio-only cues).

Injury flags: If pain is reported or red flags detected, default to conservative regressions and recommend medical clearance.

11) What you can ship first (MVP order)

Vertical call + dual panes + Whisper + avatar voice

Discovery Q&A with live transcript + chips

Two pose tests (squat, hinge) with overlays + basic cues

Brief plan generator + GAS score + single upsell chip

Photo capture stub (store, analyze later)

From there: shoulder test, future-look render, weekly coach automation.