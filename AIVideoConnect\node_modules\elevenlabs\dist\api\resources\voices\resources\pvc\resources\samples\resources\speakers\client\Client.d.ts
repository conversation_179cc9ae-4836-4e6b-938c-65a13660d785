/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../../../../../environments";
import * as core from "../../../../../../../../../../core";
import * as ElevenLabs from "../../../../../../../../../index";
import { Audio } from "../resources/audio/client/Client";
export declare namespace Speakers {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | environments.ElevenLabsEnvironmentUrls>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string>;
    }
}
export declare class Speakers {
    protected readonly _options: Speakers.Options;
    protected _audio: Audio | undefined;
    constructor(_options?: Speakers.Options);
    get audio(): Audio;
    /**
     * Retrieve the status of the speaker separation process and the list of detected speakers if complete.
     *
     * @param {string} voiceId - Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.
     * @param {string} sampleId - Sample ID to be used
     * @param {Speakers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.pvc.samples.speakers.get("21m00Tcm4TlvDq8ikWAM", "VW7YKqPnjY4h39yTbx2L")
     */
    get(voiceId: string, sampleId: string, requestOptions?: Speakers.RequestOptions): Promise<ElevenLabs.SpeakerSeparationResponseModel>;
    /**
     * Start speaker separation process for a sample
     *
     * @param {string} voiceId - Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.
     * @param {string} sampleId - Sample ID to be used
     * @param {Speakers.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.pvc.samples.speakers.separate("21m00Tcm4TlvDq8ikWAM", "VW7YKqPnjY4h39yTbx2L")
     */
    separate(voiceId: string, sampleId: string, requestOptions?: Speakers.RequestOptions): Promise<ElevenLabs.StartSpeakerSeparationResponseModel>;
}
