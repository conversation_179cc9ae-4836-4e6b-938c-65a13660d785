import { sql } from "drizzle-orm";
import { sqliteTable, text, integer, blob } from "drizzle-orm/sqlite-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// Generate UUID function for SQLite
const generateId = () => crypto.randomUUID();

export const users = sqliteTable("users", {
  id: text("id").primaryKey().$defaultFn(() => generateId()),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
  email: text("email"),
  stripeCustomerId: text("stripe_customer_id"),
  stripeSubscriptionId: text("stripe_subscription_id"),
  createdAt: text("created_at").$defaultFn(() => new Date().toISOString()),
});

export const assessmentSessions = sqliteTable("assessment_sessions", {
  id: text("id").primaryKey().$defaultFn(() => generateId()),
  userId: text("user_id").references(() => users.id),
  status: text("status").notNull().default("active"), // active, completed, cancelled
  phase: text("phase").notNull().default("welcome"), // welcome, discovery, movement, photo, reveal, complete
  personality: text("personality").default("personality1"), // personality1, personality2
  intake: text("intake"), // JSON as text in SQLite
  signals: text("signals"), // JSON as text in SQLite
  scores: text("scores"), // JSON as text in SQLite
  plan: text("plan"), // JSON as text in SQLite
  gasScore: integer("gas_score"),
  createdAt: text("created_at").$defaultFn(() => new Date().toISOString()),
  completedAt: text("completed_at"),
});

export const workoutPlans = sqliteTable("workout_plans", {
  id: text("id").primaryKey().$defaultFn(() => generateId()),
  userId: text("user_id").references(() => users.id).notNull(),
  sessionId: text("session_id").references(() => assessmentSessions.id),
  planData: text("plan_data").notNull(), // JSON as text in SQLite
  isCoached: integer("is_coached", { mode: 'boolean' }).default(false),
  createdAt: text("created_at").$defaultFn(() => new Date().toISOString()),
});

export const sessionEvents = sqliteTable("session_events", {
  id: text("id").primaryKey().$defaultFn(() => generateId()),
  sessionId: text("session_id").references(() => assessmentSessions.id).notNull(),
  eventType: text("event_type").notNull(), // question_answered, movement_detected, phase_changed, etc.
  eventData: text("event_data"), // JSON as text in SQLite
  timestamp: text("timestamp").$defaultFn(() => new Date().toISOString()),
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
  email: true,
});

export const insertAssessmentSessionSchema = createInsertSchema(assessmentSessions).pick({
  userId: true,
  status: true,
  phase: true,
});

export const insertWorkoutPlanSchema = createInsertSchema(workoutPlans).pick({
  userId: true,
  sessionId: true,
  planData: true,
  isCoached: true,
});

export type InsertUser = z.infer<typeof insertUserSchema>;
export type User = typeof users.$inferSelect;
export type InsertAssessmentSession = z.infer<typeof insertAssessmentSessionSchema>;
export type AssessmentSession = typeof assessmentSessions.$inferSelect;
export type InsertWorkoutPlan = z.infer<typeof insertWorkoutPlanSchema>;
export type WorkoutPlan = typeof workoutPlans.$inferSelect;
export type SessionEvent = typeof sessionEvents.$inferSelect;
