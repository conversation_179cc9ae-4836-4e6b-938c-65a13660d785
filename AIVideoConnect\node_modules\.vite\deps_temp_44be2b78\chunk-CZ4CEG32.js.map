{"version": 3, "sources": ["../../@radix-ui/react-dismissable-layer/src/dismissable-layer.tsx", "../../@radix-ui/react-use-escape-keydown/src/use-escape-keydown.tsx", "../../@radix-ui/react-portal/src/portal.tsx", "../../@radix-ui/react-visually-hidden/src/visually-hidden.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Primitive, dispatchDiscreteCustomEvent } from '@radix-ui/react-primitive';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useEscapeKeydown } from '@radix-ui/react-use-escape-keydown';\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayer\n * -----------------------------------------------------------------------------------------------*/\n\nconst DISMISSABLE_LAYER_NAME = 'DismissableLayer';\nconst CONTEXT_UPDATE = 'dismissableLayer.update';\nconst POINTER_DOWN_OUTSIDE = 'dismissableLayer.pointerDownOutside';\nconst FOCUS_OUTSIDE = 'dismissableLayer.focusOutside';\n\nlet originalBodyPointerEvents: string;\n\nconst DismissableLayerContext = React.createContext({\n  layers: new Set<DismissableLayerElement>(),\n  layersWithOutsidePointerEventsDisabled: new Set<DismissableLayerElement>(),\n  branches: new Set<DismissableLayerBranchElement>(),\n});\n\ntype DismissableLayerElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface DismissableLayerProps extends PrimitiveDivProps {\n  /**\n   * When `true`, hover/focus/click interactions will be disabled on elements outside\n   * the `DismissableLayer`. Users will need to click twice on outside elements to\n   * interact with them: once to close the `DismissableLayer`, and again to trigger the element.\n   */\n  disableOutsidePointerEvents?: boolean;\n  /**\n   * Event handler called when the escape key is down.\n   * Can be prevented.\n   */\n  onEscapeKeyDown?: (event: KeyboardEvent) => void;\n  /**\n   * Event handler called when the a `pointerdown` event happens outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void;\n  /**\n   * Event handler called when the focus moves outside of the `DismissableLayer`.\n   * Can be prevented.\n   */\n  onFocusOutside?: (event: FocusOutsideEvent) => void;\n  /**\n   * Event handler called when an interaction happens outside the `DismissableLayer`.\n   * Specifically, when a `pointerdown` event happens outside or focus moves outside of it.\n   * Can be prevented.\n   */\n  onInteractOutside?: (event: PointerDownOutsideEvent | FocusOutsideEvent) => void;\n  /**\n   * Handler called when the `DismissableLayer` should be dismissed\n   */\n  onDismiss?: () => void;\n}\n\nconst DismissableLayer = React.forwardRef<DismissableLayerElement, DismissableLayerProps>(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = React.useContext(DismissableLayerContext);\n    const [node, setNode] = React.useState<DismissableLayerElement | null>(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = React.useState({});\n    const composedRefs = useComposedRefs(forwardedRef, (node) => setNode(node));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1); // prettier-ignore\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled); // prettier-ignore\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target as HTMLElement;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n\n    useEscapeKeydown((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n\n    React.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = 'none';\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (\n          disableOutsidePointerEvents &&\n          context.layersWithOutsidePointerEventsDisabled.size === 1\n        ) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n\n    /**\n     * We purposefully prevent combining this effect with the `disableOutsidePointerEvents` effect\n     * because a change to `disableOutsidePointerEvents` would remove this layer from the stack\n     * and add it to the end again so the layering order wouldn't be _creation order_.\n     * We only want them to be removed from context stacks when unmounted.\n     */\n    React.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n\n    React.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n\n    return (\n      <Primitive.div\n        {...layerProps}\n        ref={composedRefs}\n        style={{\n          pointerEvents: isBodyPointerEventsDisabled\n            ? isPointerEventsEnabled\n              ? 'auto'\n              : 'none'\n            : undefined,\n          ...props.style,\n        }}\n        onFocusCapture={composeEventHandlers(props.onFocusCapture, focusOutside.onFocusCapture)}\n        onBlurCapture={composeEventHandlers(props.onBlurCapture, focusOutside.onBlurCapture)}\n        onPointerDownCapture={composeEventHandlers(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )}\n      />\n    );\n  }\n);\n\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * DismissableLayerBranch\n * -----------------------------------------------------------------------------------------------*/\n\nconst BRANCH_NAME = 'DismissableLayerBranch';\n\ntype DismissableLayerBranchElement = React.ElementRef<typeof Primitive.div>;\ninterface DismissableLayerBranchProps extends PrimitiveDivProps {}\n\nconst DismissableLayerBranch = React.forwardRef<\n  DismissableLayerBranchElement,\n  DismissableLayerBranchProps\n>((props, forwardedRef) => {\n  const context = React.useContext(DismissableLayerContext);\n  const ref = React.useRef<DismissableLayerBranchElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n\n  return <Primitive.div {...props} ref={composedRefs} />;\n});\n\nDismissableLayerBranch.displayName = BRANCH_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype PointerDownOutsideEvent = CustomEvent<{ originalEvent: PointerEvent }>;\ntype FocusOutsideEvent = CustomEvent<{ originalEvent: FocusEvent }>;\n\n/**\n * Listens for `pointerdown` outside a react subtree. We use `pointerdown` rather than `pointerup`\n * to mimic layer dismissing behaviour present in OS.\n * Returns props to pass to the node we want to check for outside events.\n */\nfunction usePointerDownOutside(\n  onPointerDownOutside?: (event: PointerDownOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handlePointerDownOutside = useCallbackRef(onPointerDownOutside) as EventListener;\n  const isPointerInsideReactTreeRef = React.useRef(false);\n  const handleClickRef = React.useRef(() => {});\n\n  React.useEffect(() => {\n    const handlePointerDown = (event: PointerEvent) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n\n        function handleAndDispatchPointerDownOutsideEvent() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        }\n\n        /**\n         * On touch devices, we need to wait for a click event because browsers implement\n         * a ~350ms delay between the time the user stops touching the display and when the\n         * browser executres events. We need to ensure we don't reactivate pointer-events within\n         * this timeframe otherwise the browser may execute events that should have been prevented.\n         *\n         * Additionally, this also lets us deal automatically with cancellations when a click event\n         * isn't raised because the page was considered scrolled/drag-scrolled, long-pressed, etc.\n         *\n         * This is why we also continuously remove the previous listener, because we cannot be\n         * certain that it was raised, and therefore cleaned-up.\n         */\n        if (event.pointerType === 'touch') {\n          ownerDocument.removeEventListener('click', handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent;\n          ownerDocument.addEventListener('click', handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent();\n        }\n      } else {\n        // We need to remove the event listener in case the outside click has been canceled.\n        // See: https://github.com/radix-ui/primitives/issues/2171\n        ownerDocument.removeEventListener('click', handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    /**\n     * if this hook executes in a component that mounts via a `pointerdown` event, the event\n     * would bubble up to the document and trigger a `pointerDownOutside` event. We avoid\n     * this by delaying the event listener registration on the document.\n     * This is not React specific, but rather how the DOM works, ie:\n     * ```\n     * button.addEventListener('pointerdown', () => {\n     *   console.log('I will log');\n     *   document.addEventListener('pointerdown', () => {\n     *     console.log('I will also log');\n     *   })\n     * });\n     */\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener('pointerdown', handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener('pointerdown', handlePointerDown);\n      ownerDocument.removeEventListener('click', handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => (isPointerInsideReactTreeRef.current = true),\n  };\n}\n\n/**\n * Listens for when focus happens outside a react subtree.\n * Returns props to pass to the root (node) of the subtree we want to check.\n */\nfunction useFocusOutside(\n  onFocusOutside?: (event: FocusOutsideEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const handleFocusOutside = useCallbackRef(onFocusOutside) as EventListener;\n  const isFocusInsideReactTreeRef = React.useRef(false);\n\n  React.useEffect(() => {\n    const handleFocus = (event: FocusEvent) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false,\n        });\n      }\n    };\n    ownerDocument.addEventListener('focusin', handleFocus);\n    return () => ownerDocument.removeEventListener('focusin', handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n\n  return {\n    onFocusCapture: () => (isFocusInsideReactTreeRef.current = true),\n    onBlurCapture: () => (isFocusInsideReactTreeRef.current = false),\n  };\n}\n\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\n\nfunction handleAndDispatchCustomEvent<E extends CustomEvent, OriginalEvent extends Event>(\n  name: string,\n  handler: ((event: E) => void) | undefined,\n  detail: { originalEvent: OriginalEvent } & (E extends CustomEvent<infer D> ? D : never),\n  { discrete }: { discrete: boolean }\n) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler as EventListener, { once: true });\n\n  if (discrete) {\n    dispatchDiscreteCustomEvent(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\n\nconst Root = DismissableLayer;\nconst Branch = DismissableLayerBranch;\n\nexport {\n  DismissableLayer,\n  DismissableLayerBranch,\n  //\n  Root,\n  Branch,\n};\nexport type { DismissableLayerProps };\n", "import * as React from 'react';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\n\n/**\n * Listens for when the escape key is down\n */\nfunction useEscapeKeydown(\n  onEscapeKeyDownProp?: (event: KeyboardEvent) => void,\n  ownerDocument: Document = globalThis?.document\n) {\n  const onEscapeKeyDown = useCallbackRef(onEscapeKeyDownProp);\n\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener('keydown', handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener('keydown', handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\nexport { useEscapeKeydown };\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: Element | DocumentFragment | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || (mounted && globalThis?.document?.body);\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{\n          // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n          position: 'absolute',\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: 'hidden',\n          clip: 'rect(0, 0, 0, 0)',\n          whiteSpace: 'nowrap',\n          wordWrap: 'normal',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n};\nexport type { VisuallyHiddenProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAuB;;;ACAvB,YAAuB;AAMvB,SAAS,iBACP,qBACA,gBAA0B,yCAAY,UACtC;AACA,QAAM,kBAAkB,eAAe,mBAAmB;AAEpD,EAAA,gBAAU,MAAM;AACpB,UAAM,gBAAgB,CAAC,UAAyB;AAC9C,UAAI,MAAM,QAAQ,UAAU;AAC1B,wBAAgB,KAAK;MACvB;IACF;AACA,kBAAc,iBAAiB,WAAW,eAAe,EAAE,SAAS,KAAK,CAAC;AAC1E,WAAO,MAAM,cAAc,oBAAoB,WAAW,eAAe,EAAE,SAAS,KAAK,CAAC;EAC5F,GAAG,CAAC,iBAAiB,aAAa,CAAC;AACrC;;;ADqIM,yBAAA;AA/IN,IAAM,yBAAyB;AAC/B,IAAM,iBAAiB;AACvB,IAAM,uBAAuB;AAC7B,IAAM,gBAAgB;AAEtB,IAAI;AAEJ,IAAM,0BAAgC,qBAAc;EAClD,QAAQ,oBAAI,IAA6B;EACzC,wCAAwC,oBAAI,IAA6B;EACzE,UAAU,oBAAI,IAAmC;AACnD,CAAC;AAsCD,IAAM,mBAAyB;EAC7B,CAAC,OAAO,iBAAiB;AACvB,UAAM;MACJ,8BAA8B;MAC9B;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAAI;AACJ,UAAM,UAAgB,kBAAW,uBAAuB;AACxD,UAAM,CAAC,MAAM,OAAO,IAAU,gBAAyC,IAAI;AAC3E,UAAM,iBAAgB,6BAAM,mBAAiB,yCAAY;AACzD,UAAM,CAAC,EAAE,KAAK,IAAU,gBAAS,CAAC,CAAC;AACnC,UAAM,eAAe,gBAAgB,cAAc,CAACC,UAAS,QAAQA,KAAI,CAAC;AAC1E,UAAM,SAAS,MAAM,KAAK,QAAQ,MAAM;AACxC,UAAM,CAAC,4CAA4C,IAAI,CAAC,GAAG,QAAQ,sCAAsC,EAAE,MAAM,EAAE;AACnH,UAAM,oDAAoD,OAAO,QAAQ,4CAA4C;AACrH,UAAM,QAAQ,OAAO,OAAO,QAAQ,IAAI,IAAI;AAC5C,UAAM,8BAA8B,QAAQ,uCAAuC,OAAO;AAC1F,UAAM,yBAAyB,SAAS;AAExC,UAAM,qBAAqB,sBAAsB,CAAC,UAAU;AAC1D,YAAM,SAAS,MAAM;AACrB,YAAM,wBAAwB,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,SAAS,MAAM,CAAC;AAC5F,UAAI,CAAC,0BAA0B,sBAAuB;AACtD,mEAAuB;AACvB,6DAAoB;AACpB,UAAI,CAAC,MAAM,iBAAkB;IAC/B,GAAG,aAAa;AAEhB,UAAM,eAAe,gBAAgB,CAAC,UAAU;AAC9C,YAAM,SAAS,MAAM;AACrB,YAAM,kBAAkB,CAAC,GAAG,QAAQ,QAAQ,EAAE,KAAK,CAAC,WAAW,OAAO,SAAS,MAAM,CAAC;AACtF,UAAI,gBAAiB;AACrB,uDAAiB;AACjB,6DAAoB;AACpB,UAAI,CAAC,MAAM,iBAAkB;IAC/B,GAAG,aAAa;AAEhB,qBAAiB,CAAC,UAAU;AAC1B,YAAM,iBAAiB,UAAU,QAAQ,OAAO,OAAO;AACvD,UAAI,CAAC,eAAgB;AACrB,yDAAkB;AAClB,UAAI,CAAC,MAAM,oBAAoB,WAAW;AACxC,cAAM,eAAe;AACrB,kBAAU;MACZ;IACF,GAAG,aAAa;AAEV,IAAA,iBAAU,MAAM;AACpB,UAAI,CAAC,KAAM;AACX,UAAI,6BAA6B;AAC/B,YAAI,QAAQ,uCAAuC,SAAS,GAAG;AAC7D,sCAA4B,cAAc,KAAK,MAAM;AACrD,wBAAc,KAAK,MAAM,gBAAgB;QAC3C;AACA,gBAAQ,uCAAuC,IAAI,IAAI;MACzD;AACA,cAAQ,OAAO,IAAI,IAAI;AACvB,qBAAe;AACf,aAAO,MAAM;AACX,YACE,+BACA,QAAQ,uCAAuC,SAAS,GACxD;AACA,wBAAc,KAAK,MAAM,gBAAgB;QAC3C;MACF;IACF,GAAG,CAAC,MAAM,eAAe,6BAA6B,OAAO,CAAC;AAQxD,IAAA,iBAAU,MAAM;AACpB,aAAO,MAAM;AACX,YAAI,CAAC,KAAM;AACX,gBAAQ,OAAO,OAAO,IAAI;AAC1B,gBAAQ,uCAAuC,OAAO,IAAI;AAC1D,uBAAe;MACjB;IACF,GAAG,CAAC,MAAM,OAAO,CAAC;AAEZ,IAAA,iBAAU,MAAM;AACpB,YAAM,eAAe,MAAM,MAAM,CAAC,CAAC;AACnC,eAAS,iBAAiB,gBAAgB,YAAY;AACtD,aAAO,MAAM,SAAS,oBAAoB,gBAAgB,YAAY;IACxE,GAAG,CAAC,CAAC;AAEL,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,OAAO;UACL,eAAe,8BACX,yBACE,SACA,SACF;UACJ,GAAG,MAAM;QACX;QACA,gBAAgB,qBAAqB,MAAM,gBAAgB,aAAa,cAAc;QACtF,eAAe,qBAAqB,MAAM,eAAe,aAAa,aAAa;QACnF,sBAAsB;UACpB,MAAM;UACN,mBAAmB;QACrB;MAAA;IACF;EAEJ;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,cAAc;AAKpB,IAAM,yBAA+B,kBAGnC,CAAC,OAAO,iBAAiB;AACzB,QAAM,UAAgB,kBAAW,uBAAuB;AACxD,QAAM,MAAY,cAAsC,IAAI;AAC5D,QAAM,eAAe,gBAAgB,cAAc,GAAG;AAEhD,EAAA,iBAAU,MAAM;AACpB,UAAM,OAAO,IAAI;AACjB,QAAI,MAAM;AACR,cAAQ,SAAS,IAAI,IAAI;AACzB,aAAO,MAAM;AACX,gBAAQ,SAAS,OAAO,IAAI;MAC9B;IACF;EACF,GAAG,CAAC,QAAQ,QAAQ,CAAC;AAErB,aAAO,wBAAC,UAAU,KAAV,EAAe,GAAG,OAAO,KAAK,aAAA,CAAc;AACtD,CAAC;AAED,uBAAuB,cAAc;AAYrC,SAAS,sBACP,sBACA,gBAA0B,yCAAY,UACtC;AACA,QAAM,2BAA2B,eAAe,oBAAoB;AACpE,QAAM,8BAAoC,cAAO,KAAK;AACtD,QAAM,iBAAuB,cAAO,MAAM;EAAC,CAAC;AAEtC,EAAA,iBAAU,MAAM;AACpB,UAAM,oBAAoB,CAAC,UAAwB;AACjD,UAAI,MAAM,UAAU,CAAC,4BAA4B,SAAS;AAGxD,YAASC,4CAAT,WAAoD;AAClD;YACE;YACA;YACA;YACA,EAAE,UAAU,KAAK;UACnB;QACF;AAPS,YAAA,2CAAAA;AAFT,cAAM,cAAc,EAAE,eAAe,MAAM;AAuB3C,YAAI,MAAM,gBAAgB,SAAS;AACjC,wBAAc,oBAAoB,SAAS,eAAe,OAAO;AACjE,yBAAe,UAAUA;AACzB,wBAAc,iBAAiB,SAAS,eAAe,SAAS,EAAE,MAAM,KAAK,CAAC;QAChF,OAAO;AACLA,oDAAyC;QAC3C;MACF,OAAO;AAGL,sBAAc,oBAAoB,SAAS,eAAe,OAAO;MACnE;AACA,kCAA4B,UAAU;IACxC;AAcA,UAAM,UAAU,OAAO,WAAW,MAAM;AACtC,oBAAc,iBAAiB,eAAe,iBAAiB;IACjE,GAAG,CAAC;AACJ,WAAO,MAAM;AACX,aAAO,aAAa,OAAO;AAC3B,oBAAc,oBAAoB,eAAe,iBAAiB;AAClE,oBAAc,oBAAoB,SAAS,eAAe,OAAO;IACnE;EACF,GAAG,CAAC,eAAe,wBAAwB,CAAC;AAE5C,SAAO;;IAEL,sBAAsB,MAAO,4BAA4B,UAAU;EACrE;AACF;AAMA,SAAS,gBACP,gBACA,gBAA0B,yCAAY,UACtC;AACA,QAAM,qBAAqB,eAAe,cAAc;AACxD,QAAM,4BAAkC,cAAO,KAAK;AAE9C,EAAA,iBAAU,MAAM;AACpB,UAAM,cAAc,CAAC,UAAsB;AACzC,UAAI,MAAM,UAAU,CAAC,0BAA0B,SAAS;AACtD,cAAM,cAAc,EAAE,eAAe,MAAM;AAC3C,qCAA6B,eAAe,oBAAoB,aAAa;UAC3E,UAAU;QACZ,CAAC;MACH;IACF;AACA,kBAAc,iBAAiB,WAAW,WAAW;AACrD,WAAO,MAAM,cAAc,oBAAoB,WAAW,WAAW;EACvE,GAAG,CAAC,eAAe,kBAAkB,CAAC;AAEtC,SAAO;IACL,gBAAgB,MAAO,0BAA0B,UAAU;IAC3D,eAAe,MAAO,0BAA0B,UAAU;EAC5D;AACF;AAEA,SAAS,iBAAiB;AACxB,QAAM,QAAQ,IAAI,YAAY,cAAc;AAC5C,WAAS,cAAc,KAAK;AAC9B;AAEA,SAAS,6BACP,MACA,SACA,QACA,EAAE,SAAS,GACX;AACA,QAAM,SAAS,OAAO,cAAc;AACpC,QAAM,QAAQ,IAAI,YAAY,MAAM,EAAE,SAAS,OAAO,YAAY,MAAM,OAAO,CAAC;AAChF,MAAI,QAAS,QAAO,iBAAiB,MAAM,SAA0B,EAAE,MAAM,KAAK,CAAC;AAEnF,MAAI,UAAU;AACZ,gCAA4B,QAAQ,KAAK;EAC3C,OAAO;AACL,WAAO,cAAc,KAAK;EAC5B;AACF;AAEA,IAAM,OAAO;AACb,IAAM,SAAS;;;AE9Vf,IAAAC,SAAuB;AACvB,uBAAqB;AAyBO,IAAAC,sBAAA;AAjB5B,IAAM,cAAc;AAWpB,IAAM,SAAe,kBAAuC,CAAC,OAAO,iBAAiB;;AACnF,QAAM,EAAE,WAAW,eAAe,GAAG,YAAY,IAAI;AACrD,QAAM,CAAC,SAAS,UAAU,IAAU,gBAAS,KAAK;AAClD,mBAAgB,MAAM,WAAW,IAAI,GAAG,CAAC,CAAC;AAC1C,QAAM,YAAY,iBAAkB,aAAW,8CAAY,aAAZ,mBAAsB;AACrE,SAAO,YACH,iBAAAC,QAAS,iBAAa,yBAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc,GAAI,SAAS,IACtF;AACN,CAAC;AAED,OAAO,cAAc;;;AC9BrB,IAAAC,SAAuB;AAgBjB,IAAAC,sBAAA;AATN,IAAM,OAAO;AAMb,IAAM,iBAAuB;EAC3B,CAAC,OAAO,iBAAiB;AACvB,eACE;MAAC,UAAU;MAAV;QACE,GAAG;QACJ,KAAK;QACL,OAAO;;UAEL,UAAU;UACV,QAAQ;UACR,OAAO;UACP,QAAQ;UACR,SAAS;UACT,QAAQ;UACR,UAAU;UACV,MAAM;UACN,YAAY;UACZ,UAAU;UACV,GAAG,MAAM;QACX;MAAA;IACF;EAEJ;AACF;AAEA,eAAe,cAAc;AAI7B,IAAMC,QAAO;", "names": ["React", "node", "handleAndDispatchPointerDownOutsideEvent", "React", "import_jsx_runtime", "ReactDOM", "React", "import_jsx_runtime", "Root"]}