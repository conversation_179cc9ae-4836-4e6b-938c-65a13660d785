import OpenAI from "openai";

// the newest OpenAI model is "gpt-5" which was released August 7, 2025. do not change this unless explicitly requested by the user
let openai: OpenAI | null = null;

function getOpenAI(): OpenAI {
  if (!openai) {
    const apiKey = process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_ENV_VAR;
    if (!apiKey || apiKey === "your_openai_api_key_here") {
      throw new Error("OpenAI API key not configured. Please set OPENAI_API_KEY in your .env file.");
    }
    openai = new OpenAI({ apiKey });
  }
  return openai;
}

export async function analyzeIntakeData(intake: any): Promise<{
  constraints: any;
  levers: any;
  emotionalSignals: any;
}> {
  try {
    const response = await getOpenAI().chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content: "You are a fitness assessment expert. Analyze the user's intake data and extract constraints, levers, and emotional signals. Respond with JSON in this format: { 'constraints': {}, 'levers': {}, 'emotionalSignals': {} }"
        },
        {
          role: "user",
          content: `Analyze this fitness assessment intake: ${JSON.stringify(intake)}`
        }
      ],
      response_format: { type: "json_object" },
    });

    return JSON.parse(response.choices[0].message.content || "{}");
  } catch (error: any) {
    throw new Error("Failed to analyze intake data: " + error.message);
  }
}

export async function generateWorkoutPlan(sessionData: any, scores: any): Promise<any> {
  try {
    const response = await getOpenAI().chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content: "You are an expert fitness coach. Generate a detailed, personalized workout plan based on the assessment data and scores. Include weekly schedule, exercise selections, progressions, and nutrition guidelines. Respond with structured JSON."
        },
        {
          role: "user",
          content: `Generate a workout plan for this assessment data: ${JSON.stringify({ sessionData, scores })}`
        }
      ],
      response_format: { type: "json_object" },
    });

    return JSON.parse(response.choices[0].message.content || "{}");
  } catch (error: any) {
    throw new Error("Failed to generate workout plan: " + error.message);
  }
}

export async function analyzeMovementQuality(movementData: any): Promise<{
  scores: any;
  recommendations: string[];
}> {
  try {
    const response = await getOpenAI().chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content: "You are a movement analysis expert. Analyze pose detection data and provide movement quality scores and recommendations. Respond with JSON: { 'scores': {}, 'recommendations': [] }"
        },
        {
          role: "user",
          content: `Analyze this movement data: ${JSON.stringify(movementData)}`
        }
      ],
      response_format: { type: "json_object" },
    });

    return JSON.parse(response.choices[0].message.content || "{}");
  } catch (error: any) {
    throw new Error("Failed to analyze movement quality: " + error.message);
  }
}

export async function generateTTS(text: string, voice: string = "alloy"): Promise<Buffer> {
  try {
    const response = await getOpenAI().audio.speech.create({
      model: "tts-1",
      voice: voice as any, // OpenAI supports: alloy, echo, fable, onyx, nova, shimmer
      input: text,
    });

    const buffer = Buffer.from(await response.arrayBuffer());
    return buffer;
  } catch (error: any) {
    throw new Error("Failed to generate TTS: " + error.message);
  }
}

export async function transcribeAudio(audioBuffer: Buffer, language: string = "en", mimeType: string = "audio/webm"): Promise<{
  text: string;
  confidence?: number;
  duration?: number;
  segments?: any[];
}> {
  try {
    console.log(`🎙️ Transcribing audio: ${audioBuffer.length} bytes, type: ${mimeType}, language: ${language}`);

    if (audioBuffer.length === 0) {
      throw new Error("Audio buffer is empty");
    }

    if (audioBuffer.length < 1000) {
      throw new Error("Audio buffer too small - may not contain valid audio data");
    }

    // Determine file extension from MIME type
    let extension = "webm";
    if (mimeType.includes("mp4")) extension = "mp4";
    else if (mimeType.includes("wav")) extension = "wav";
    else if (mimeType.includes("ogg")) extension = "ogg";

    // Create a temporary file-like object for the audio buffer
    const audioFile = new File([audioBuffer], `audio.${extension}`, { type: mimeType });

    console.log(`📤 Sending to Whisper API: ${audioFile.size} bytes as ${audioFile.type}`);

    const response = await getOpenAI().audio.transcriptions.create({
      file: audioFile,
      model: "whisper-1",
      language: language,
      response_format: "verbose_json",
      temperature: 0.1, // Lower temperature for more consistent results
      prompt: "This is a conversation with an AI fitness trainer. The user is speaking about fitness, health, goals, and exercises.", // Context prompt for better accuracy
    });

    console.log(`✅ Whisper transcription successful: "${response.text}"`);
    console.log(`📊 Segments: ${response.segments?.length || 0}, Duration: ${response.duration}s`);

    // Calculate average confidence from segments
    let avgConfidence = 0.8; // Default confidence
    if (response.segments && response.segments.length > 0) {
      const logProbs = response.segments
        .map(seg => seg.avg_logprob)
        .filter(prob => prob !== undefined && prob !== null);

      if (logProbs.length > 0) {
        const avgLogProb = logProbs.reduce((sum, prob) => sum + prob, 0) / logProbs.length;
        avgConfidence = Math.max(0.1, Math.min(1.0, Math.exp(avgLogProb))); // Clamp between 0.1 and 1.0
      }
    }

    // Filter out very short or likely incorrect transcriptions
    if (response.text.trim().length < 2) {
      throw new Error("Transcription too short - please speak more clearly");
    }

    return {
      text: response.text.trim(),
      confidence: avgConfidence,
      duration: response.duration,
      segments: response.segments
    };
  } catch (error: any) {
    console.error("❌ Whisper transcription failed:", error);

    // Provide more specific error messages
    if (error.message.includes("Invalid file format")) {
      throw new Error("Audio format not supported. Please check your microphone settings.");
    } else if (error.message.includes("Audio file is too short")) {
      throw new Error("Audio too short. Please speak for at least 1 second.");
    } else if (error.message.includes("rate limit")) {
      throw new Error("Too many requests. Please wait a moment and try again.");
    } else if (error.message.includes("quota")) {
      throw new Error("API quota exceeded. Please try again later.");
    } else {
      throw new Error(`Transcription failed: ${error.message}`);
    }
  }
}

export async function generateAIResponse(userMessage: string, phase: string = 'welcome'): Promise<string> {
  try {
    const systemPrompts = {
      welcome: "You are an energetic AI fitness trainer starting a personalized assessment. Be welcoming and motivating. Keep responses under 2 sentences.",
      discovery: "You are asking discovery questions about fitness goals and schedule. Be encouraging and ask follow-up questions. Keep responses under 2 sentences.", 
      movement: "You are coaching someone through exercises, providing real-time form feedback. Be supportive and give specific movement cues. Keep responses under 2 sentences.",
      photo: "You are guiding someone through posture photos for analysis. Be clear about positioning. Keep responses under 2 sentences.",
      reveal: "You are revealing fitness assessment results and workout recommendations. Be encouraging and specific. Keep responses under 2 sentences."
    };

    const completion = await getOpenAI().chat.completions.create({
      model: "gpt-5",
      messages: [
        {
          role: "system",
          content: systemPrompts[phase as keyof typeof systemPrompts] || systemPrompts.welcome
        },
        {
          role: "user", 
          content: userMessage
        }
      ],
      max_tokens: 100,
      temperature: 0.7,
    });

    return completion.choices[0]?.message?.content || "Great! Let's continue with your assessment.";
  } catch (error: any) {
    console.error('OpenAI conversation error:', error);
    return "Perfect! Let's keep moving forward with your fitness journey.";
  }
}
