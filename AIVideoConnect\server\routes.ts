import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import Strip<PERSON> from "stripe";
import { storage } from "./storage";
import { assessmentService } from "./services/assessment";
import { generateAIResponse, transcribeAudio } from "./services/openai";
import { generateElevenLabsTTS, AI_TRAINER_VOICE_ID } from "./services/elevenlabs";
import { characterAIService, PersonalityType } from "./services/characterai";
import { insertAssessmentSessionSchema } from "@shared/schema";

// Extend WebSocket interface to include sessionId
interface ExtendedWebSocket extends WebSocket {
  sessionId?: string;
}

// Initialize Stripe only if secret key is available
const stripe = process.env.STRIPE_SECRET_KEY 
  ? new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: "2025-08-27.basil",
    })
  : null;

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // WebSocket server for real-time communication
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });
  
  wss.on('connection', (ws) => {
    console.log('WebSocket client connected');
    
    ws.on('message', async (message) => {
      try {
        const data = JSON.parse(message.toString());
        
        switch (data.type) {
          case 'join_session':
            ws.sessionId = data.sessionId;
            ws.send(JSON.stringify({ type: 'session_joined', sessionId: data.sessionId }));
            break;
            
          case 'speech_data':
            // Handle speech-to-text data and generate AI response
            if (ws.sessionId) {
              // Store the speech event (only if session exists)
              try {
                if (ws.sessionId !== 'test-session') {
                  await storage.createSessionEvent({
                    sessionId: ws.sessionId,
                    eventType: 'speech_detected',
                    eventData: { transcript: data.transcript, confidence: data.confidence }
                  });
                }
              } catch (dbError) {
                console.warn('⚠️ Failed to store session event:', dbError);
                // Continue processing even if database storage fails
              }
              
              // Get current session to determine phase
              const session = await storage.getAssessmentSession(ws.sessionId);
              const currentPhase = session?.phase || 'welcome';
              
              // Generate AI response using OpenAI
              try {
                const aiResponse = await generateAIResponse(data.transcript, currentPhase);
                
                // Send AI response back to the client
                ws.send(JSON.stringify({ 
                  type: 'ai_response', 
                  message: aiResponse,
                  phase: currentPhase
                }));
                
                console.log(`AI responded in ${currentPhase} phase:`, aiResponse);
              } catch (error) {
                console.error('Failed to generate AI response:', error);
                // Send fallback response
                ws.send(JSON.stringify({ 
                  type: 'ai_response', 
                  message: "I hear you! Let's continue with your assessment.",
                  phase: currentPhase
                }));
              }
              
              // Broadcast to other clients in session
              wss.clients.forEach(client => {
                if (client !== ws && client.readyState === WebSocket.OPEN && client.sessionId === ws.sessionId) {
                  client.send(JSON.stringify({ type: 'speech_update', data: data }));
                }
              });
            }
            break;
            
          case 'pose_data':
            // Handle pose detection data
            if (ws.sessionId) {
              try {
                if (ws.sessionId !== 'test-session') {
                  await storage.createSessionEvent({
                    sessionId: ws.sessionId,
                    eventType: 'pose_detected',
                    eventData: data.poses
                  });
                }
              } catch (dbError) {
                console.warn('⚠️ Failed to store pose event:', dbError);
                // Continue processing even if database storage fails
              }
            }
            break;
            
          case 'whisper_audio':
            // Handle Whisper audio transcription
            if (ws.sessionId && data.audio) {
              try {
                console.log(`🎙️ Processing Whisper audio for session ${ws.sessionId}`);

                // Decode base64 audio
                const audioBuffer = Buffer.from(data.audio, 'base64');
                console.log(`📦 Audio buffer size: ${audioBuffer.length} bytes`);

                // Transcribe with Whisper using provided MIME type
                const mimeType = data.mimeType || 'audio/webm';
                const language = data.language || 'en';

                const transcription = await transcribeAudio(audioBuffer, language, mimeType);
                console.log(`✅ Whisper transcription: "${transcription.text}" (confidence: ${Math.round((transcription.confidence || 0.8) * 100)}%)`);

                // Store the speech event with detailed metadata (only if session exists)
                try {
                  if (ws.sessionId && ws.sessionId !== 'test-session') {
                    await storage.createSessionEvent({
                      sessionId: ws.sessionId,
                      eventType: 'speech_detected',
                      eventData: {
                        transcript: transcription.text,
                        confidence: transcription.confidence || 0.8,
                        duration: transcription.duration,
                        source: 'whisper',
                        mimeType: mimeType,
                        audioSize: audioBuffer.length,
                        segments: transcription.segments?.length || 0
                      }
                    });
                  }
                } catch (dbError) {
                  console.warn('⚠️ Failed to store session event:', dbError);
                  // Continue processing even if database storage fails
                }

                // Get current session to determine phase
                const session = await storage.getAssessmentSession(ws.sessionId);
                const currentPhase = session?.phase || 'welcome';

                // Generate AI response
                const aiResponse = await generateAIResponse(transcription.text, currentPhase);

                // Send AI response back to the client with detailed transcription info
                ws.send(JSON.stringify({
                  type: 'ai_response',
                  message: aiResponse,
                  phase: currentPhase,
                  transcript: transcription.text,
                  confidence: transcription.confidence,
                  duration: transcription.duration,
                  source: 'whisper'
                }));

                console.log(`🤖 AI Response sent: "${aiResponse}"`);
              } catch (error: any) {
                console.error('❌ Whisper transcription error:', error);

                // Send specific error message based on error type
                let errorMessage = "I'm having trouble hearing you. Could you try again?";
                let errorType = 'transcription_failed';

                if (error.message.includes('too short')) {
                  errorMessage = "Please speak for a bit longer. I need at least a few words to understand you.";
                  errorType = 'audio_too_short';
                } else if (error.message.includes('format')) {
                  errorMessage = "There's an issue with your audio format. Please check your microphone settings.";
                  errorType = 'audio_format_error';
                } else if (error.message.includes('quota') || error.message.includes('rate limit')) {
                  errorMessage = "I'm processing too many requests right now. Please wait a moment and try again.";
                  errorType = 'api_limit_error';
                }

                ws.send(JSON.stringify({
                  type: 'ai_response',
                  message: errorMessage,
                  error: errorType,
                  errorDetails: error.message
                }));
              }
            }
            break;

          case 'tts_audio':
            // Handle TTS audio analysis for orb animation
            if (ws.sessionId) {
              wss.clients.forEach(client => {
                if (client !== ws && client.readyState === WebSocket.OPEN && client.sessionId === ws.sessionId) {
                  client.send(JSON.stringify({ type: 'orb_animation', data: data.audioData }));
                }
              });
            }
            break;
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });
    
    ws.on('close', () => {
      console.log('WebSocket client disconnected');
    });
  });

  // Assessment session endpoints
  app.post("/api/assessment/start", async (req, res) => {
    try {
      const { userId } = req.body;
      
      // Ensure the demo user exists - check by username first
      let user = await storage.getUserByUsername(userId);
      if (!user) {
        user = await storage.createUser({
          username: userId,
          password: "demo-password", // In a real app, this would be properly handled
          email: `${userId}@demo.com`
        });
      }
      
      const session = await storage.createAssessmentSession({
        userId: user.id,
        status: "active",
        phase: "welcome"
      });
      
      res.json(session);
    } catch (error: any) {
      res.status(500).json({ message: "Error starting assessment: " + error.message });
    }
  });

  app.get("/api/assessment/:id", async (req, res) => {
    try {
      const session = await storage.getAssessmentSession(req.params.id);
      if (!session) {
        return res.status(404).json({ message: "Session not found" });
      }
      res.json(session);
    } catch (error: any) {
      res.status(500).json({ message: "Error fetching session: " + error.message });
    }
  });

  app.patch("/api/assessment/:id", async (req, res) => {
    try {
      const { personality } = req.body;
      
      const session = await storage.updateAssessmentSession(req.params.id, {
        personality
      });
      
      res.json(session);
    } catch (error: any) {
      res.status(500).json({ message: "Error updating session: " + error.message });
    }
  });

  app.post("/api/assessment/:id/intake", async (req, res) => {
    try {
      const { answers } = req.body;
      
      const session = await storage.updateAssessmentSession(req.params.id, {
        intake: answers,
        phase: "movement"
      });
      
      res.json(session);
    } catch (error: any) {
      res.status(500).json({ message: "Error updating intake: " + error.message });
    }
  });

  app.post("/api/assessment/:id/movement", async (req, res) => {
    try {
      const { movementData } = req.body;
      
      const session = await storage.updateAssessmentSession(req.params.id, {
        signals: { movement: movementData },
        phase: "photo"
      });
      
      res.json(session);
    } catch (error: any) {
      res.status(500).json({ message: "Error updating movement data: " + error.message });
    }
  });

  app.post("/api/assessment/:id/photos", async (req, res) => {
    try {
      const { photos } = req.body;
      
      const session = await storage.getAssessmentSession(req.params.id);
      if (!session) {
        return res.status(404).json({ message: "Session not found" });
      }
      
      const updatedSignals = { ...session.signals, photos };
      
      const updatedSession = await storage.updateAssessmentSession(req.params.id, {
        signals: updatedSignals,
        phase: "reveal"
      });
      
      res.json(updatedSession);
    } catch (error: any) {
      res.status(500).json({ message: "Error updating photos: " + error.message });
    }
  });

  app.post("/api/assessment/:id/next-phase", async (req, res) => {
    try {
      const session = await storage.getAssessmentSession(req.params.id);
      if (!session) {
        return res.status(404).json({ message: "Session not found" });
      }

      // Define phase progression
      const phaseOrder = ["welcome", "discovery", "movement", "photo", "reveal"];
      const currentIndex = phaseOrder.indexOf(session.phase);
      const nextPhase = currentIndex < phaseOrder.length - 1 ? phaseOrder[currentIndex + 1] : session.phase;
      
      // Update session phase
      const updatedSession = await storage.updateAssessmentSession(req.params.id, {
        phase: nextPhase
      });
      
      res.json(updatedSession);
    } catch (error: any) {
      res.status(500).json({ message: "Error updating phase: " + error.message });
    }
  });

  app.post("/api/assessment/:id/complete", async (req, res) => {
    try {
      const session = await storage.getAssessmentSession(req.params.id);
      if (!session) {
        return res.status(404).json({ message: "Session not found" });
      }
      
      // Generate assessment scores and plan
      const scores = await assessmentService.calculateScores(session);
      const plan = await assessmentService.generatePlan(session, scores);
      
      const completedSession = await storage.updateAssessmentSession(req.params.id, {
        scores,
        plan,
        gasScore: scores.GAS,
        status: "completed",
        phase: "complete",
        completedAt: new Date()
      });
      
      res.json(completedSession);
    } catch (error: any) {
      res.status(500).json({ message: "Error completing assessment: " + error.message });
    }
  });

  // TTS endpoint using ElevenLabs with AI Trainer voice
  app.post("/api/tts", async (req, res) => {
    try {
      const { text, personality } = req.body;

      if (!text) {
        return res.status(400).json({ message: "Text is required" });
      }

      console.log(`🎤 Generating ElevenLabs TTS for: "${text.substring(0, 50)}..."`);

      // Use the specific AI Trainer voice you provided
      const audioBuffer = await generateElevenLabsTTS(text, AI_TRAINER_VOICE_ID);

      res.set({
        'Content-Type': 'audio/mpeg',
        'Content-Length': audioBuffer.length,
        'Cache-Control': 'no-cache',
      });

      res.send(audioBuffer);
    } catch (error: any) {
      console.error('❌ ElevenLabs TTS generation error:', error);
      res.status(500).json({ message: "Error generating TTS: " + error.message });
    }
  });

  // Character.AI Chat endpoints
  app.post("/api/character-ai/chat", async (req, res) => {
    try {
      const { personality, sessionId, message } = req.body;
      
      if (!personality || !sessionId || !message) {
        return res.status(400).json({ message: "Personality, sessionId, and message are required" });
      }
      
      const response = await characterAIService.sendMessage(personality, sessionId, message);
      res.json({ response });
    } catch (error: any) {
      console.error('Character.AI chat error:', error);
      res.status(500).json({ message: "Error sending message: " + error.message });
    }
  });

  app.get("/api/character-ai/personalities", async (req, res) => {
    try {
      const personalities = await Promise.all([
        characterAIService.getPersonalityInfo('personality1'),
        characterAIService.getPersonalityInfo('personality2')
      ]);
      
      res.json({
        personality1: personalities[0],
        personality2: personalities[1]
      });
    } catch (error: any) {
      console.error('Error fetching personality info:', error);
      res.status(500).json({ message: "Error fetching personalities: " + error.message });
    }
  });

  // Workout plan endpoints
  app.post("/api/plans", async (req, res) => {
    try {
      const { userId, sessionId, planData, isCoached } = req.body;
      
      const plan = await storage.createWorkoutPlan({
        userId,
        sessionId,
        planData,
        isCoached: isCoached || false
      });
      
      res.json(plan);
    } catch (error: any) {
      res.status(500).json({ message: "Error creating plan: " + error.message });
    }
  });

  app.get("/api/users/:userId/plans", async (req, res) => {
    try {
      const plans = await storage.getUserWorkoutPlans(req.params.userId);
      res.json(plans);
    } catch (error: any) {
      res.status(500).json({ message: "Error fetching plans: " + error.message });
    }
  });

  // Stripe subscription endpoint
  app.post('/api/create-subscription', async (req, res) => {
    try {
      if (!stripe) {
        return res.status(503).json({ 
          error: { message: 'Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.' } 
        });
      }

      const { userId, email, username } = req.body;
      
      const customer = await stripe.customers.create({
        email,
        name: username,
      });

      const subscription = await stripe.subscriptions.create({
        customer: customer.id,
        items: [{
          price: process.env.STRIPE_PRICE_ID || 'price_default',
        }],
        payment_behavior: 'default_incomplete',
        expand: ['latest_invoice.payment_intent'],
      });

      await storage.updateUserStripeInfo(userId, customer.id, subscription.id);

      res.json({
        subscriptionId: subscription.id,
        clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
      });
    } catch (error: any) {
      res.status(400).json({ error: { message: error.message } });
    }
  });

  // Health check endpoint
  app.get("/api/health", (req, res) => {
    res.json({
      status: "ok",
      timestamp: new Date().toISOString(),
      services: {
        database: "connected",
        websocket: "active"
      }
    });
  });

  return httpServer;
}
