export interface AudioAnalysisData {
  rms: number;
  pitch: number;
  viseme?: string;
}

export class AudioAnalyzer {
  private audioContext: AudioContext | null = null;
  private analyzer: AnalyserNode | null = null;
  private dataArray: Uint8Array | null = null;
  private source: MediaStreamAudioSourceNode | null = null;
  private isAnalyzing = false;
  private animationFrame: number | null = null;

  constructor(private onFrame: (data: AudioAnalysisData) => void) {}

  async start(stream: MediaStream): Promise<void> {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.analyzer = this.audioContext.createAnalyser();
      this.analyzer.fftSize = 2048;
      
      const bufferLength = this.analyzer.frequencyBinCount;
      this.dataArray = new Uint8Array(bufferLength);
      
      this.source = this.audioContext.createMediaStreamSource(stream);
      this.source.connect(this.analyzer);
      
      this.isAnalyzing = true;
      this.analyze();
    } catch (error) {
      console.error('Failed to start audio analysis:', error);
    }
  }

  stop(): void {
    this.isAnalyzing = false;
    
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
    
    if (this.source) {
      this.source.disconnect();
      this.source = null;
    }
    
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    
    this.analyzer = null;
    this.dataArray = null;
  }

  private analyze = (): void => {
    if (!this.isAnalyzing || !this.analyzer || !this.dataArray) return;

    this.analyzer.getByteFrequencyData(this.dataArray);
    
    // Calculate RMS (Root Mean Square) for amplitude
    let sum = 0;
    for (let i = 0; i < this.dataArray.length; i++) {
      const normalized = this.dataArray[i] / 255;
      sum += normalized * normalized;
    }
    const rms = Math.sqrt(sum / this.dataArray.length);
    
    // Estimate fundamental frequency (pitch)
    const pitch = this.estimatePitch(this.dataArray);
    
    this.onFrame({
      rms: Math.min(rms * 2, 1), // Amplify and clamp to [0,1]
      pitch,
    });
    
    this.animationFrame = requestAnimationFrame(this.analyze);
  };

  private estimatePitch(dataArray: Uint8Array): number {
    // Simple pitch estimation using the strongest frequency bin
    let maxAmplitude = 0;
    let maxIndex = 0;
    
    // Focus on speech frequency range (80-1000 Hz)
    const minIndex = Math.floor((80 * dataArray.length) / (this.audioContext!.sampleRate / 2));
    const maxFreqIndex = Math.floor((1000 * dataArray.length) / (this.audioContext!.sampleRate / 2));
    
    for (let i = minIndex; i < Math.min(maxFreqIndex, dataArray.length); i++) {
      if (dataArray[i] > maxAmplitude) {
        maxAmplitude = dataArray[i];
        maxIndex = i;
      }
    }
    
    // Convert bin index to frequency
    const frequency = (maxIndex * this.audioContext!.sampleRate / 2) / dataArray.length;
    
    // Return frequency or default if no strong signal
    return maxAmplitude > 10 ? frequency : 180; // Default to ~180Hz
  }
}
