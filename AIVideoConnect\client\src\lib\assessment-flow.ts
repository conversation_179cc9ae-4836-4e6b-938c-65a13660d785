// Assessment Flow Logic - Manages the complete fitness assessment workflow
export interface AssessmentPhase {
  id: string;
  name: string;
  description: string;
  questions?: AssessmentQuestion[];
  exercises?: Exercise[];
  duration?: number; // in seconds
  nextPhase?: string;
}

export interface AssessmentQuestion {
  id: string;
  text: string;
  type: 'text' | 'choice' | 'scale' | 'boolean';
  options?: string[];
  required: boolean;
  followUp?: string;
}

export interface Exercise {
  id: string;
  name: string;
  instructions: string;
  reps: number;
  duration: number; // in seconds
  cues: string[];
  targetJoints: string[];
}

export interface AssessmentState {
  currentPhase: string;
  responses: Record<string, any>;
  movementData: Record<string, any>;
  photos: string[];
  startTime: Date;
  phaseStartTime: Date;
}

// Assessment phases configuration
export const ASSESSMENT_PHASES: Record<string, AssessmentPhase> = {
  welcome: {
    id: 'welcome',
    name: 'Welcome',
    description: 'Initial greeting and setup',
    duration: 30,
    nextPhase: 'discovery'
  },
  
  discovery: {
    id: 'discovery',
    name: '<PERSON>',
    description: 'Goal setting and lifestyle assessment',
    questions: [
      {
        id: 'primary_goal',
        text: "What's the one change you want to see in the next 8-12 weeks?",
        type: 'text',
        required: true,
        followUp: "That's a great goal! What's been your biggest challenge in achieving this before?"
      },
      {
        id: 'time_availability',
        text: "How many days per week can you realistically commit to working out?",
        type: 'choice',
        options: ['2-3 days', '4-5 days', '6-7 days'],
        required: true,
        followUp: "Perfect! And how much time per session works best for you?"
      },
      {
        id: 'session_duration',
        text: "How long should each workout session be?",
        type: 'choice',
        options: ['20-30 minutes', '30-45 minutes', '45-60 minutes', '60+ minutes'],
        required: true
      },
      {
        id: 'experience_level',
        text: "How would you describe your current fitness level?",
        type: 'choice',
        options: ['Beginner', 'Some experience', 'Intermediate', 'Advanced'],
        required: true
      },
      {
        id: 'equipment_access',
        text: "What equipment do you have access to?",
        type: 'choice',
        options: ['None (bodyweight only)', 'Basic (dumbbells, bands)', 'Home gym setup', 'Full gym access'],
        required: true
      },
      {
        id: 'past_injuries',
        text: "Do you have any injuries or areas of concern I should know about?",
        type: 'text',
        required: false,
        followUp: "Thanks for sharing that. I'll make sure to account for that in your program."
      },
      {
        id: 'motivation_level',
        text: "On a scale of 1-10, how motivated are you to stick with a fitness program?",
        type: 'scale',
        required: true,
        followUp: "I love that energy! What usually helps keep you motivated?"
      }
    ],
    nextPhase: 'movement'
  },
  
  movement: {
    id: 'movement',
    name: 'Movement Assessment',
    description: 'Functional movement screening',
    exercises: [
      {
        id: 'air_squat',
        name: 'Air Squat',
        instructions: 'Stand with feet shoulder-width apart. Lower down as if sitting in a chair, then stand back up.',
        reps: 5,
        duration: 30,
        cues: [
          'Keep your chest up',
          'Push your knees out',
          'Go as low as comfortable',
          'Drive through your heels'
        ],
        targetJoints: ['hip', 'knee', 'ankle']
      },
      {
        id: 'forward_fold',
        name: 'Forward Fold',
        instructions: 'Stand tall, then slowly bend forward reaching toward your toes.',
        reps: 1,
        duration: 15,
        cues: [
          'Keep your legs straight',
          'Reach down slowly',
          'Don\'t force it',
          'Feel the stretch in your hamstrings'
        ],
        targetJoints: ['hip', 'spine']
      },
      {
        id: 'shoulder_reach',
        name: 'Overhead Reach',
        instructions: 'Raise both arms straight up overhead as high as you can.',
        reps: 3,
        duration: 20,
        cues: [
          'Keep your core engaged',
          'Reach as high as possible',
          'Don\'t arch your back',
          'Hold for a moment at the top'
        ],
        targetJoints: ['shoulder', 'thoracic_spine']
      }
    ],
    duration: 120,
    nextPhase: 'photo'
  },
  
  photo: {
    id: 'photo',
    name: 'Posture Analysis',
    description: 'Photo capture for posture assessment',
    duration: 60,
    nextPhase: 'reveal'
  },
  
  reveal: {
    id: 'reveal',
    name: 'Results & Plan',
    description: 'Assessment results and personalized plan reveal',
    duration: 120,
    nextPhase: 'complete'
  }
};

// Assessment flow controller
export class AssessmentFlowController {
  private state: AssessmentState;
  private onPhaseChange?: (phase: string) => void;
  private onQuestionComplete?: (questionId: string, answer: any) => void;
  private onExerciseComplete?: (exerciseId: string, data: any) => void;

  constructor(
    initialPhase: string = 'welcome',
    callbacks?: {
      onPhaseChange?: (phase: string) => void;
      onQuestionComplete?: (questionId: string, answer: any) => void;
      onExerciseComplete?: (exerciseId: string, data: any) => void;
    }
  ) {
    this.state = {
      currentPhase: initialPhase,
      responses: {},
      movementData: {},
      photos: [],
      startTime: new Date(),
      phaseStartTime: new Date()
    };
    
    this.onPhaseChange = callbacks?.onPhaseChange;
    this.onQuestionComplete = callbacks?.onQuestionComplete;
    this.onExerciseComplete = callbacks?.onExerciseComplete;
  }

  getCurrentPhase(): AssessmentPhase {
    return ASSESSMENT_PHASES[this.state.currentPhase];
  }

  getState(): AssessmentState {
    return { ...this.state };
  }

  nextPhase(): boolean {
    const currentPhase = this.getCurrentPhase();
    if (currentPhase.nextPhase) {
      this.state.currentPhase = currentPhase.nextPhase;
      this.state.phaseStartTime = new Date();
      this.onPhaseChange?.(this.state.currentPhase);
      return true;
    }
    return false;
  }

  recordResponse(questionId: string, answer: any): void {
    this.state.responses[questionId] = {
      answer,
      timestamp: new Date()
    };
    this.onQuestionComplete?.(questionId, answer);
  }

  recordMovementData(exerciseId: string, data: any): void {
    this.state.movementData[exerciseId] = {
      ...data,
      timestamp: new Date()
    };
    this.onExerciseComplete?.(exerciseId, data);
  }

  addPhoto(photoData: string): void {
    this.state.photos.push(photoData);
  }

  getProgress(): number {
    const phases = Object.keys(ASSESSMENT_PHASES);
    const currentIndex = phases.indexOf(this.state.currentPhase);
    return (currentIndex + 1) / phases.length;
  }

  getTimeInCurrentPhase(): number {
    return Date.now() - this.state.phaseStartTime.getTime();
  }

  getTotalTime(): number {
    return Date.now() - this.state.startTime.getTime();
  }

  // Get contextual prompts based on current phase and progress
  getContextualPrompt(): string {
    const phase = this.getCurrentPhase();
    const timeInPhase = this.getTimeInCurrentPhase() / 1000; // seconds
    
    switch (phase.id) {
      case 'welcome':
        return "Welcome! I'm your AI fitness trainer. I'm excited to help you reach your goals. Let's start with a quick assessment to create your perfect program.";
      
      case 'discovery':
        const questions = phase.questions || [];
        const answeredCount = Object.keys(this.state.responses).length;
        if (answeredCount === 0) {
          return questions[0]?.text || "Let's talk about your fitness goals.";
        } else if (answeredCount < questions.length) {
          return questions[answeredCount]?.text || "Tell me more about your fitness journey.";
        } else {
          return "Great! I have everything I need about your goals. Now let's see how you move.";
        }
      
      case 'movement':
        const exercises = phase.exercises || [];
        const completedExercises = Object.keys(this.state.movementData).length;
        if (completedExercises < exercises.length) {
          const currentExercise = exercises[completedExercises];
          return `Let's do ${currentExercise.reps} ${currentExercise.name}. ${currentExercise.instructions}`;
        } else {
          return "Excellent work! Your movement looks great. Now let's capture some photos for posture analysis.";
        }
      
      case 'photo':
        if (this.state.photos.length === 0) {
          return "Please stand facing the camera with your arms at your sides. I'll capture a front view photo.";
        } else if (this.state.photos.length === 1) {
          return "Perfect! Now please turn to your side so I can get a profile view.";
        } else {
          return "Great photos! I'm analyzing your posture now. Let me show you what I found.";
        }
      
      case 'reveal':
        return "Based on your assessment, I've created a personalized program just for you. Here's what I recommend...";
      
      default:
        return "Let's continue with your fitness assessment.";
    }
  }

  // Generate AI coaching cues based on movement data
  generateMovementCue(exerciseId: string, poseData: any): string {
    const exercise = this.getCurrentPhase().exercises?.find(ex => ex.id === exerciseId);
    if (!exercise) return "Keep going, you're doing great!";

    // Simple movement analysis (in real implementation, this would be more sophisticated)
    const cues = exercise.cues;
    const randomCue = cues[Math.floor(Math.random() * cues.length)];
    
    return randomCue;
  }
}
