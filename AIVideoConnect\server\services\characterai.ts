import CharacterAI from "node_characterai";

// Character IDs extracted from your chat links
export const CHARACTER_PERSONALITIES = {
  personality1: "YtyKM_d257C8nB3kdE644HNFN8j2e4aYUPEuFTOTdE0",
  personality2: "X5nZi05MggIP7xqfoJiOGUhsv4-8U3SOifDWlFeIjKs"
} as const;

export type PersonalityType = keyof typeof CHARACTER_PERSONALITIES;

class CharacterAIService {
  private characterAI: CharacterAI;
  private isAuthenticated = false;
  private activeChats = new Map<string, any>();

  constructor() {
    this.characterAI = new CharacterAI();
  }

  async authenticate(): Promise<void> {
    if (this.isAuthenticated) return;

    try {
      const token = process.env.CHARACTER_AI_TOKEN;
      
      if (token && token !== "default_token") {
        // Use account token for unlimited messages
        await this.characterAI.authenticateWithToken(token);
      } else {
        // Fall back to guest mode (limited messages)
        await this.characterAI.authenticateAsGuest();
      }
      
      this.isAuthenticated = true;
      console.log("Character.AI authenticated successfully");
    } catch (error) {
      console.error("Character.AI authentication failed:", error);
      throw new Error("Failed to authenticate with Character.AI");
    }
  }

  async getChatForPersonality(personality: PersonalityType, sessionId: string): Promise<any> {
    await this.authenticate();
    
    const chatKey = `${sessionId}-${personality}`;
    
    if (this.activeChats.has(chatKey)) {
      return this.activeChats.get(chatKey);
    }

    try {
      const characterId = CHARACTER_PERSONALITIES[personality];
      const chat = await this.characterAI.createOrContinueChat(characterId);
      this.activeChats.set(chatKey, chat);
      return chat;
    } catch (error) {
      console.error(`Failed to create chat for ${personality}:`, error);
      throw new Error(`Failed to initialize ${personality} chat`);
    }
  }

  async sendMessage(
    personality: PersonalityType,
    sessionId: string,
    message: string
  ): Promise<string> {
    try {
      const chat = await this.getChatForPersonality(personality, sessionId);
      const response = await chat.sendAndAwaitResponse(message, true);
      return response.text;
    } catch (error) {
      console.error(`Character.AI message failed for ${personality}:`, error);
      throw new Error(`Failed to get response from ${personality}`);
    }
  }

  async getPersonalityInfo(personality: PersonalityType): Promise<any> {
    await this.authenticate();
    
    try {
      const characterId = CHARACTER_PERSONALITIES[personality];
      return await this.characterAI.getCharacter(characterId);
    } catch (error) {
      console.error(`Failed to get character info for ${personality}:`, error);
      return null;
    }
  }

  // Clean up chats for a session
  clearSessionChats(sessionId: string): void {
    const keysToDelete = Array.from(this.activeChats.keys()).filter(key => 
      key.startsWith(sessionId)
    );
    keysToDelete.forEach(key => this.activeChats.delete(key));
  }
}

export const characterAIService = new CharacterAIService();