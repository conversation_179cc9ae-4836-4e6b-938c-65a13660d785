/**
 * This file was auto-generated by Fe<PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export type SystemToolConfigInputParams = ElevenLabs.SystemToolConfigInputParams.EndCall | ElevenLabs.SystemToolConfigInputParams.LanguageDetection | ElevenLabs.SystemToolConfigInputParams.TransferToAgent | ElevenLabs.SystemToolConfigInputParams.TransferToNumber;
export declare namespace SystemToolConfigInputParams {
    interface EndCall extends ElevenLabs.EndCallToolConfig {
        system_tool_type: "end_call";
    }
    interface LanguageDetection extends ElevenLabs.LanguageDetectionToolConfig {
        system_tool_type: "language_detection";
    }
    interface TransferToAgent extends ElevenLabs.TransferToAgentToolConfig {
        system_tool_type: "transfer_to_agent";
    }
    interface TransferToNumber extends ElevenLabs.TransferToNumberToolConfig {
        system_tool_type: "transfer_to_number";
    }
}
