import { useRef, useEffect, useState } from "react";
import OrbAvatar from "./orb-avatar";
import PoseOverlay from "./pose-overlay";
import { usePoseDetection } from "@/hooks/use-pose-detection";
import { useTTS } from "@/hooks/use-tts";
import { useWebSocket } from "@/hooks/use-websocket";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import PostureAnalysis from "./posture-analysis";

interface VideoCallProps {
  session: any;
  localStream: MediaStream | null;
  remoteStream: MediaStream | null;
  transcript: string;
  onPersonalityChange?: (personality: string) => void;
}

export default function VideoCall({ session, localStream, remoteStream, transcript, onPersonalityChange }: VideoCallProps) {
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const remoteVideoRef = useRef<HTMLVideoElement>(null);
  const [currentPrompt, setCurrentPrompt] = useState("What's the one change you want to see in the next 8-12 weeks?");
  const [repCount, setRepCount] = useState(0);
  const [currentExercise, setCurrentExercise] = useState("Air Squat x5");
  const [selectedPersonality, setSelectedPersonality] = useState(session?.personality || "personality1");
  const [formFeedback, setFormFeedback] = useState<any>(null);

  const { poses, isDetecting, startDetection, stopDetection } = usePoseDetection(localVideoRef);
  const { speak, isPlaying, error: ttsError } = useTTS();
  const { isConnected, subscribe, connect, disconnect } = useWebSocket();

  useEffect(() => {
    if (localVideoRef.current && localStream) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  useEffect(() => {
    if (remoteVideoRef.current && remoteStream) {
      remoteVideoRef.current.srcObject = remoteStream;
    }
  }, [remoteStream]);

  useEffect(() => {
    if (session?.phase === "movement" && !isDetecting) {
      startDetection();
    } else if (session?.phase !== "movement" && isDetecting) {
      stopDetection();
    }
  }, [session?.phase, isDetecting, startDetection, stopDetection]);

  // Update prompts based on session phase and speak them
  useEffect(() => {
    let newPrompt = currentPrompt;
    
    if (session?.phase === "discovery") {
      newPrompt = "How many days per week can you realistically train?";
    } else if (session?.phase === "movement") {
      newPrompt = "Let's check your squat form. Do 5 air squats.";
      setCurrentExercise("Air Squat x5");
    } else if (session?.phase === "photo") {
      newPrompt = "Please take front and side photos for posture analysis.";
    } else if (session?.phase === "welcome") {
      newPrompt = "Welcome! I'm your AI fitness trainer. I'll guide you through a personalized assessment to create your perfect workout plan.";
    }
    
    if (newPrompt !== currentPrompt) {
      setCurrentPrompt(newPrompt);
      // Speak the new prompt with selected personality voice
      speak(newPrompt, selectedPersonality);
    }
  }, [session?.phase, currentPrompt, speak]);

  // Connect to WebSocket when session is available
  useEffect(() => {
    if (session?.id) {
      connect(session.id);
    }
    return () => {
      disconnect();
    };
  }, [session?.id, connect, disconnect]);

  // Subscribe to AI responses
  useEffect(() => {
    const unsubscribe = subscribe('ai_response', (data) => {
      console.log('Received AI response:', data.message);
      setCurrentPrompt(data.message);
      // Speak the AI response using current values
      speak(data.message, selectedPersonality);
    });

    return unsubscribe;
  }, [subscribe, speak, selectedPersonality]);

  const getOrbState = () => {
    switch (session?.phase) {
      case "welcome": return "welcome";
      case "discovery": return "discovery";
      case "movement": return "coaching";
      case "photo": return "coaching";
      case "reveal": return "reveal";
      default: return "idle";
    }
  };

  return (
    <div className="grid lg:grid-cols-2 gap-6 mb-6">
      {/* AI Trainer Pane */}
      <Card className="bg-card rounded-2xl border border-border overflow-hidden aspect-video relative">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10"></div>
        
        {/* Holographic Orb Avatar */}
        <div className="absolute inset-0 flex items-center justify-center">
          <OrbAvatar
            state={getOrbState()}
            engagement={0.8}
            intensity={0.6}
            className="scale-75 lg:scale-100"
            data-testid="ai-orb"
          />
        </div>
        
        {/* AI State Indicator */}
        <div className="absolute top-4 left-4 bg-card/80 backdrop-blur-sm rounded-full px-3 py-1 border border-border">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-accent rounded-full animate-pulse"></div>
            <span className="text-xs font-medium text-accent capitalize" data-testid="text-ai-state">
              {session?.phase || "Loading"} Mode
            </span>
          </div>
        </div>

        {/* Personality Selector */}
        <div className="absolute top-4 right-4">
          <div className="bg-card/80 backdrop-blur-sm rounded-xl p-3 border border-border">
            <div className="text-xs text-muted-foreground mb-2">AI Trainer</div>
            <Select 
              value={selectedPersonality} 
              onValueChange={(value) => {
                setSelectedPersonality(value);
                if (onPersonalityChange) {
                  onPersonalityChange(value);
                }
              }}
              data-testid="select-personality"
            >
              <SelectTrigger className="w-32 h-7 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="personality1">Alex (Nova)</SelectItem>
                <SelectItem value="personality2">Jordan (Onyx)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Current Cue Card */}
        <div className="absolute bottom-6 left-6 right-6">
          <div className="bg-card/90 backdrop-blur-sm rounded-xl p-4 border border-border shadow-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm text-muted-foreground">AI Trainer is saying:</div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => speak(currentPrompt, selectedPersonality)}
                disabled={isPlaying}
                className="h-6 px-2"
                data-testid="button-repeat-audio"
              >
                {isPlaying ? "🔊" : "🔊"}
              </Button>
            </div>
            <div className="text-foreground font-medium" data-testid="text-current-prompt">
              {currentPrompt}
            </div>
            {ttsError && (
              <div className="text-xs text-red-500 mt-1">
                Audio unavailable: {ttsError}
              </div>
            )}
          </div>
        </div>
      </Card>
      
      {/* User Camera Pane */}
      <Card className="bg-card rounded-2xl border border-border overflow-hidden aspect-video relative">
        {/* User Video */}
        <video
          ref={localVideoRef}
          autoPlay
          muted
          playsInline
          className="absolute inset-0 w-full h-full object-cover"
          data-testid="video-local-stream"
        />
        
        {/* Pose Detection Overlay */}
        {session?.phase === "movement" && (
          <PoseOverlay 
            poses={poses}
            videoRef={localVideoRef}
            className="absolute inset-0"
          />
        )}
        
        {/* Real-time Posture Analysis */}
        {session?.phase === "movement" && (
          <div className="absolute top-4 right-4 w-64">
            <PostureAnalysis
              poses={poses}
              exercise={currentExercise}
              onFormFeedback={setFormFeedback}
            />
          </div>
        )}
        
        {/* Rep Counter */}
        {session?.phase === "movement" && formFeedback && (
          <div className="absolute bottom-4 right-4">
            <div className="bg-primary/90 backdrop-blur-sm rounded-full w-16 h-16 flex items-center justify-center border-2 border-primary-foreground/20">
              <div className="text-center">
                <div className="text-xl font-bold text-primary-foreground" data-testid="text-rep-count">
                  {formFeedback.repCount || 0}
                </div>
                <div className="text-xs text-primary-foreground/80">reps</div>
              </div>
            </div>
          </div>
        )}
        
        {/* Exercise Instruction */}
        {session?.phase === "movement" && (
          <div className="absolute bottom-4 left-4">
            <div className="bg-card/90 backdrop-blur-sm rounded-xl p-3 border border-border">
              <div className="text-xs text-muted-foreground mb-1">Current Exercise</div>
              <div className="text-sm font-medium text-foreground" data-testid="text-current-exercise">
                {currentExercise}
              </div>
              <div className="text-xs text-accent">
                {5 - (formFeedback?.repCount || 0)} more to go
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
