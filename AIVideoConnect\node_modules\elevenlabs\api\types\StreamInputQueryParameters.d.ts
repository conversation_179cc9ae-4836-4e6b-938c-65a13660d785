/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface StreamInputQueryParameters {
    /** The model ID to use */
    model_id?: string;
    /** The ISO 639-1 language code (for Turbo v2.5 and Flash v2.5 models only) */
    language_code?: string;
    /** Whether to enable logging of the request */
    enable_logging?: string;
    /** Whether to enable SSML parsing */
    enable_ssml_parsing?: boolean;
    /** Latency optimization level (deprecated) */
    optimize_streaming_latency?: ElevenLabs.StreamInputQueryParametersOptimizeStreamingLatency;
    output_format?: ElevenLabs.TextToSpeechOutputFormatEnum;
    /** Timeout for inactivity before connection is closed */
    inactivity_timeout?: number;
    /** Whether to include timing data with every audio chunk */
    sync_alignment?: boolean;
    /** This parameter focuses on reducing the latency by disabling the chunk schedule and all buffers. It is only recommended when sending full sentences or phrases, sending partial phrases will result in highly reduced quality. By default it's set to false. */
    auto_mode?: boolean;
    apply_text_normalization?: ElevenLabs.TextToSpeechApplyTextNormalizationEnum;
    /** If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed. Must be an integer between 0 and 4294967295. */
    seed?: number;
}
