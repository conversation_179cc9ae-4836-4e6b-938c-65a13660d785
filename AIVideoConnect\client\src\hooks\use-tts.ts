import { useState, useCallback, useRef } from "react";

export function useTTS() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [audioUnlocked, setAudioUnlocked] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  const speak = useCallback(async (text: string, personality?: string) => {
    if (isPlaying) return;

    try {
      setIsPlaying(true);
      setError(null);

      // Stop any existing audio
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // Generate TTS audio
      const response = await fetch("/api/tts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ text, personality }),
      });

      if (!response.ok) {
        throw new Error(`TTS failed: ${response.statusText}`);
      }

      // Create audio blob and play
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      
      const audio = new Audio(audioUrl);
      audioRef.current = audio;

      audio.onended = () => {
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);
        audioRef.current = null;
      };

      audio.onerror = () => {
        setError("Audio playback failed");
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);
        audioRef.current = null;
      };

      try {
        await audio.play();
        if (!audioUnlocked) {
          setAudioUnlocked(true);
        }
      } catch (playError: any) {
        if (playError.name === 'NotAllowedError') {
          setError("Click the speaker button to enable audio");
        } else {
          throw playError;
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "TTS failed");
      setIsPlaying(false);
    }
  }, [isPlaying, audioUnlocked]);

  const stop = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current = null;
    }
    setIsPlaying(false);
  }, []);

  return {
    speak,
    stop,
    isPlaying,
    error,
    audioUnlocked,
  };
}