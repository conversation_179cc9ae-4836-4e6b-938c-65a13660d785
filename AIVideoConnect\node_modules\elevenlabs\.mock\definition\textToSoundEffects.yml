types:
  TextToSoundEffectsConvertRequestOutputFormat:
    enum:
      - mp3_22050_32
      - mp3_44100_32
      - mp3_44100_64
      - mp3_44100_96
      - mp3_44100_128
      - mp3_44100_192
      - pcm_8000
      - pcm_16000
      - pcm_22050
      - pcm_24000
      - pcm_44100
      - pcm_48000
      - ulaw_8000
      - alaw_8000
      - opus_48000_32
      - opus_48000_64
      - opus_48000_96
      - opus_48000_128
      - opus_48000_192
    docs: >-
      Output format of the generated audio. Formatted as
      codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is
      represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be
      subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires
      you to be subscribed to Pro tier or above. Note that the μ-law format
      (sometimes written mu-law, often approximated as u-law) is commonly used
      for Twilio audio inputs.
    default: mp3_44100_128
    source:
      openapi: openapi.json
imports:
  root: __package__.yml
service:
  auth: false
  base-path: ''
  endpoints:
    convert:
      path: /v1/sound-generation
      method: POST
      auth: false
      docs: >-
        Turn text into sound effects for your videos, voice-overs or video games
        using the most advanced sound effects model in the world.
      source:
        openapi: openapi.json
      display-name: Create sound effect
      request:
        name: CreateSoundEffectRequest
        query-parameters:
          output_format:
            type: optional<TextToSoundEffectsConvertRequestOutputFormat>
            default: mp3_44100_128
            docs: >-
              Output format of the generated audio. Formatted as
              codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at
              32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate
              requires you to be subscribed to Creator tier or above. PCM with
              44.1kHz sample rate requires you to be subscribed to Pro tier or
              above. Note that the μ-law format (sometimes written mu-law, often
              approximated as u-law) is commonly used for Twilio audio inputs.
        body:
          properties:
            text:
              type: string
              docs: The text that will get converted into a sound effect.
            duration_seconds:
              type: optional<double>
              docs: >-
                The duration of the sound which will be generated in seconds.
                Must be at least 0.5 and at most 22. If set to None we will
                guess the optimal duration using the prompt. Defaults to None.
            prompt_influence:
              type: optional<double>
              docs: >-
                A higher prompt influence makes your generation follow the
                prompt more closely while also making generations less variable.
                Must be a value between 0 and 1. Defaults to 0.3.
        content-type: application/json
      response:
        docs: The generated sound effect as an MP3 file
        type: file
        status-code: 200
      errors:
        - root.UnprocessableEntityError
      examples:
        - request:
            text: Spacious braam suitable for high-impact movie trailer moments
  source:
    openapi: openapi.json
