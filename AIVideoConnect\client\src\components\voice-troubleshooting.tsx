import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface VoiceTroubleshootingProps {
  error?: string | null;
  isVisible?: boolean;
  onClose?: () => void;
  recommendations?: string[];
}

export default function VoiceTroubleshooting({ error, isVisible = false, onClose, recommendations = [] }: VoiceTroubleshootingProps) {
  const [openSections, setOpenSections] = useState<string[]>(['common']);

  const toggleSection = (section: string) => {
    setOpenSections(prev => 
      prev.includes(section) 
        ? prev.filter(s => s !== section)
        : [...prev, section]
    );
  };

  const troubleshootingSteps = [
    {
      id: 'permissions',
      title: '🔒 Microphone Permissions',
      priority: 'high',
      steps: [
        'Click the microphone icon in your browser\'s address bar',
        'Select "Always allow" for microphone access',
        'Refresh the page and try again',
        'Check your browser settings: Settings > Privacy > Microphone'
      ]
    },
    {
      id: 'hardware',
      title: '🎤 Hardware Issues',
      priority: 'high',
      steps: [
        'Check if your microphone is properly connected',
        'Test your microphone in other applications (e.g., voice recorder)',
        'Try unplugging and reconnecting your microphone',
        'Check microphone volume levels in system settings',
        'Ensure microphone is not muted in system settings'
      ]
    },
    {
      id: 'browser',
      title: '🌐 Browser Compatibility',
      priority: 'medium',
      steps: [
        'Use Chrome, Edge, or Safari for best compatibility',
        'Update your browser to the latest version',
        'Disable browser extensions that might block microphone access',
        'Try opening the page in an incognito/private window',
        'Clear browser cache and cookies'
      ]
    },
    {
      id: 'audio',
      title: '🔊 Audio Quality',
      priority: 'medium',
      steps: [
        'Speak clearly and at normal volume',
        'Reduce background noise',
        'Position microphone 6-12 inches from your mouth',
        'Avoid speaking too fast or too slow',
        'Try speaking in shorter phrases (3-5 seconds)'
      ]
    },
    {
      id: 'network',
      title: '🌐 Network Issues',
      priority: 'low',
      steps: [
        'Check your internet connection',
        'Try refreshing the page',
        'Disable VPN if you\'re using one',
        'Try switching to a different network',
        'Contact support if issues persist'
      ]
    }
  ];

  const getErrorSpecificSteps = (errorMessage: string) => {
    const lowerError = errorMessage.toLowerCase();
    
    if (lowerError.includes('permission') || lowerError.includes('not allowed')) {
      return ['permissions'];
    } else if (lowerError.includes('no speech') || lowerError.includes('not found')) {
      return ['hardware', 'audio'];
    } else if (lowerError.includes('not supported') || lowerError.includes('browser')) {
      return ['browser'];
    } else if (lowerError.includes('network') || lowerError.includes('connection')) {
      return ['network'];
    } else {
      return ['permissions', 'hardware'];
    }
  };

  const relevantSections = error ? getErrorSpecificSteps(error) : ['permissions', 'hardware', 'audio'];

  if (!isVisible) return null;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <span>🔧 Voice Recognition Troubleshooting</span>
          </CardTitle>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          )}
        </div>
        {error && (
          <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
            <div className="text-sm text-red-800 dark:text-red-200">
              <strong>Current Error:</strong> {error}
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Follow these steps to resolve voice recognition issues:
        </div>

        {/* Adaptive Recommendations */}
        {recommendations.length > 0 && (
          <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="font-medium text-green-800 dark:text-green-200 mb-2">
              🎯 Personalized Recommendations
            </div>
            <ul className="space-y-1 text-sm text-green-700 dark:text-green-300">
              {recommendations.map((rec, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-green-500">•</span>
                  <span>{rec}</span>
                </li>
              ))}
            </ul>
            <div className="text-xs text-green-600 dark:text-green-400 mt-2">
              These recommendations are based on your recent speech recognition patterns.
            </div>
          </div>
        )}

        {troubleshootingSteps
          .filter(step => relevantSections.includes(step.id))
          .map((section) => (
            <Collapsible
              key={section.id}
              open={openSections.includes(section.id)}
              onOpenChange={() => toggleSection(section.id)}
            >
              <CollapsibleTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between p-4 h-auto"
                >
                  <div className="flex items-center space-x-2">
                    <span>{section.title}</span>
                    <Badge 
                      variant={section.priority === 'high' ? 'destructive' : section.priority === 'medium' ? 'default' : 'secondary'}
                    >
                      {section.priority} priority
                    </Badge>
                  </div>
                  <span className="text-lg">
                    {openSections.includes(section.id) ? '−' : '+'}
                  </span>
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-2">
                <div className="p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                  <ol className="space-y-2 text-sm">
                    {section.steps.map((step, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="flex-shrink-0 w-5 h-5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full flex items-center justify-center text-xs font-medium">
                          {index + 1}
                        </span>
                        <span>{step}</span>
                      </li>
                    ))}
                  </ol>
                </div>
              </CollapsibleContent>
            </Collapsible>
          ))}

        <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="text-sm text-blue-800 dark:text-blue-200">
            <strong>💡 Quick Test:</strong> Try saying "Hello, can you hear me?" clearly and wait for a response. 
            If the issue persists, work through the steps above in order of priority.
          </div>
        </div>

        <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
          Still having issues? The speech test component in the top-right corner can help diagnose specific problems.
        </div>
      </CardContent>
    </Card>
  );
}
