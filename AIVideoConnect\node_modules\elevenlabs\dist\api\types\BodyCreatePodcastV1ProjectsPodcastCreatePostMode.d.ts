/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * The type of podcast to generate. Can be 'conversation', an interaction between two voices, or 'bulletin', a monologue.
 */
export type BodyCreatePodcastV1ProjectsPodcastCreatePostMode = ElevenLabs.BodyCreatePodcastV1ProjectsPodcastCreatePostMode.Conversation | ElevenLabs.BodyCreatePodcastV1ProjectsPodcastCreatePostMode.Bulletin;
export declare namespace BodyCreatePodcastV1ProjectsPodcastCreatePostMode {
    interface Conversation extends ElevenLabs.PodcastConversationMode {
        type: "conversation";
    }
    interface Bulletin extends ElevenLabs.PodcastBulletinMode {
        type: "bulletin";
    }
}
