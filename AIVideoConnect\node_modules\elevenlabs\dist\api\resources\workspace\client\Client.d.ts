/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as environments from "../../../../environments";
import * as core from "../../../../core";
import * as ElevenLabs from "../../../index";
export declare namespace Workspace {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | environments.ElevenLabsEnvironmentUrls>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string>;
    }
}
export declare class Workspace {
    protected readonly _options: Workspace.Options;
    constructor(_options?: Workspace.Options);
    /**
     * Searches for user groups in the workspace. Multiple or no groups may be returned.
     *
     * @param {ElevenLabs.SearchUserGroupsV1WorkspaceGroupsSearchGetRequest} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.searchUserGroups({
     *         name: "name"
     *     })
     */
    searchUserGroups(request: ElevenLabs.SearchUserGroupsV1WorkspaceGroupsSearchGetRequest, requestOptions?: Workspace.RequestOptions): Promise<ElevenLabs.WorkspaceGroupByNameResponseModel[]>;
    /**
     * Removes a member from the specified group. This endpoint may only be called by workspace administrators.
     *
     * @param {string} groupId - The ID of the target group.
     * @param {ElevenLabs.BodyDeleteMemberFromUserGroupV1WorkspaceGroupsGroupIdMembersRemovePost} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.deleteMemberFromUserGroup("group_id", {
     *         email: "email"
     *     })
     */
    deleteMemberFromUserGroup(groupId: string, request: ElevenLabs.BodyDeleteMemberFromUserGroupV1WorkspaceGroupsGroupIdMembersRemovePost, requestOptions?: Workspace.RequestOptions): Promise<ElevenLabs.DeleteWorkspaceGroupMemberResponseModel>;
    /**
     * Adds a member of your workspace to the specified group. This endpoint may only be called by workspace administrators.
     *
     * @param {string} groupId - The ID of the target group.
     * @param {ElevenLabs.AddMemberToGroupRequest} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.addMemberToUserGroup("group_id", {
     *         email: "email"
     *     })
     */
    addMemberToUserGroup(groupId: string, request: ElevenLabs.AddMemberToGroupRequest, requestOptions?: Workspace.RequestOptions): Promise<ElevenLabs.AddWorkspaceGroupMemberResponseModel>;
    /**
     * Sends an email invitation to join your workspace to the provided email. If the user doesn't have an account they will be prompted to create one. If the user accepts this invite they will be added as a user to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators. If the user is already in the workspace a 400 error will be returned.
     *
     * @param {ElevenLabs.InviteUserRequest} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.inviteUser({
     *         email: "<EMAIL>"
     *     })
     */
    inviteUser(request: ElevenLabs.InviteUserRequest, requestOptions?: Workspace.RequestOptions): Promise<ElevenLabs.AddWorkspaceInviteResponseModel>;
    /**
     * Sends email invitations to join your workspace to the provided emails. Requires all email addresses to be part of a verified domain. If the users don't have an account they will be prompted to create one. If the users accept these invites they will be added as users to your workspace and your subscription using one of your seats. This endpoint may only be called by workspace administrators.
     *
     * @param {ElevenLabs.BodyInviteMultipleUsersV1WorkspaceInvitesAddBulkPost} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.inviteMultipleUsers({
     *         emails: ["emails"]
     *     })
     */
    inviteMultipleUsers(request: ElevenLabs.BodyInviteMultipleUsersV1WorkspaceInvitesAddBulkPost, requestOptions?: Workspace.RequestOptions): Promise<ElevenLabs.AddWorkspaceInviteResponseModel>;
    /**
     * Invalidates an existing email invitation. The invitation will still show up in the inbox it has been delivered to, but activating it to join the workspace won't work. This endpoint may only be called by workspace administrators.
     *
     * @param {ElevenLabs.BodyDeleteExistingInvitationV1WorkspaceInvitesDelete} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.deleteExistingInvitation({
     *         email: "<EMAIL>"
     *     })
     */
    deleteExistingInvitation(request: ElevenLabs.BodyDeleteExistingInvitationV1WorkspaceInvitesDelete, requestOptions?: Workspace.RequestOptions): Promise<ElevenLabs.DeleteWorkspaceInviteResponseModel>;
    /**
     * Updates attributes of a workspace member. Apart from the email identifier, all parameters will remain unchanged unless specified. This endpoint may only be called by workspace administrators.
     *
     * @param {ElevenLabs.UpdateMemberRequest} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.updateMember({
     *         email: "email"
     *     })
     */
    updateMember(request: ElevenLabs.UpdateMemberRequest, requestOptions?: Workspace.RequestOptions): Promise<ElevenLabs.UpdateWorkspaceMemberResponseModel>;
    /**
     * Deletes a workspace member. This endpoint may only be called by workspace administrators.
     *
     * @param {ElevenLabs.BodyDeleteMemberV1WorkspaceMembersDelete} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.deleteMember({
     *         email: "email"
     *     })
     */
    deleteMember(request: ElevenLabs.BodyDeleteMemberV1WorkspaceMembersDelete, requestOptions?: Workspace.RequestOptions): Promise<ElevenLabs.DeleteWorkspaceMemberResponseModel>;
    /**
     * Gets the metadata of a resource by ID.
     *
     * @param {string} resourceId - The ID of the target resource.
     * @param {ElevenLabs.GetResourceV1WorkspaceResourcesResourceIdGetRequest} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.getResource("resource_id", {
     *         resource_type: "voice"
     *     })
     */
    getResource(resourceId: string, request: ElevenLabs.GetResourceV1WorkspaceResourcesResourceIdGetRequest, requestOptions?: Workspace.RequestOptions): Promise<ElevenLabs.ResourceMetadataResponseModel>;
    /**
     * Grants a role on a workspace resource to a user or a group. It overrides any existing role this user/service account/group/workspace api key has on the resource. To target a user or service account, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. The resource will be shared with the service account associated with the api key. You must have admin access to the resource to share it.
     *
     * @param {string} resourceId - The ID of the target resource.
     * @param {ElevenLabs.BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePost} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.shareWorkspaceResource("resource_id", {
     *         role: "admin",
     *         resource_type: "voice"
     *     })
     */
    shareWorkspaceResource(resourceId: string, request: ElevenLabs.BodyShareWorkspaceResourceV1WorkspaceResourcesResourceIdSharePost, requestOptions?: Workspace.RequestOptions): Promise<unknown>;
    /**
     * Removes any existing role on a workspace resource from a user, service account, group or workspace api key. To target a user or service account, pass only the user email. The user must be in your workspace. To target a group, pass only the group id. To target a workspace api key, pass the api key id. The resource will be unshared from the service account associated with the api key. You must have admin access to the resource to unshare it. You cannot remove permissions from the user who created the resource.
     *
     * @param {string} resourceId - The ID of the target resource.
     * @param {ElevenLabs.BodyUnshareWorkspaceResourceV1WorkspaceResourcesResourceIdUnsharePost} request
     * @param {Workspace.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.workspace.unshareWorkspaceResource("resource_id", {
     *         resource_type: "voice"
     *     })
     */
    unshareWorkspaceResource(resourceId: string, request: ElevenLabs.BodyUnshareWorkspaceResourceV1WorkspaceResourcesResourceIdUnsharePost, requestOptions?: Workspace.RequestOptions): Promise<unknown>;
}
