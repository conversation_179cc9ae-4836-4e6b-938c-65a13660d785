/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../index";
/**
 * @example
 *     {
 *         resource_type: "voice"
 *     }
 */
export interface BodyUnshareWorkspaceResourceV1WorkspaceResourcesResourceIdUnsharePost {
    /** Resource type of the target resource. */
    resource_type: ElevenLabs.WorkspaceResourceType;
    /** The email of the user or service account. */
    user_email?: string;
    /** The ID of the target group. To target the permissions principals have by default on this resource, use the value 'default'. */
    group_id?: string;
    /** The ID of the target workspace API key. This isn't the same as the key itself that would you pass in the header for authentication. Workspace admins can find this in the workspace settings UI. */
    workspace_api_key_id?: string;
}
