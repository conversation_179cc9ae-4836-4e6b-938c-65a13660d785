@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Outfit:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(220, 15%, 6%);
  --foreground: hsl(220, 20%, 98%);
  --card: hsl(220, 15%, 8%);
  --card-foreground: hsl(220, 15%, 95%);
  --popover: hsl(220, 15%, 8%);
  --popover-foreground: hsl(220, 15%, 95%);
  --primary: hsl(213, 100%, 50%);
  --primary-foreground: hsl(220, 20%, 98%);
  --secondary: hsl(177, 70%, 35%);
  --secondary-foreground: hsl(220, 20%, 98%);
  --muted: hsl(220, 15%, 15%);
  --muted-foreground: hsl(220, 15%, 65%);
  --accent: hsl(167, 85%, 45%);
  --accent-foreground: hsl(220, 15%, 10%);
  --destructive: hsl(0, 70%, 50%);
  --destructive-foreground: hsl(220, 20%, 98%);
  --border: hsl(220, 15%, 20%);
  --input: hsl(220, 15%, 15%);
  --ring: hsl(213, 100%, 50%);
  --radius: 12px;
  --font-sans: Inter, system-ui, sans-serif;
  --font-display: Outfit, Inter, system-ui, sans-serif;
  --font-mono: Menlo, monospace;
}

.dark {
  --background: hsl(220, 15%, 6%);
  --foreground: hsl(220, 20%, 98%);
  --card: hsl(220, 15%, 8%);
  --card-foreground: hsl(220, 15%, 95%);
  --popover: hsl(220, 15%, 8%);
  --popover-foreground: hsl(220, 15%, 95%);
  --primary: hsl(213, 100%, 50%);
  --primary-foreground: hsl(220, 20%, 98%);
  --secondary: hsl(177, 70%, 35%);
  --secondary-foreground: hsl(220, 20%, 98%);
  --muted: hsl(220, 15%, 15%);
  --muted-foreground: hsl(220, 15%, 65%);
  --accent: hsl(167, 85%, 45%);
  --accent-foreground: hsl(220, 15%, 10%);
  --destructive: hsl(0, 70%, 50%);
  --destructive-foreground: hsl(220, 20%, 98%);
  --border: hsl(220, 15%, 20%);
  --input: hsl(220, 15%, 15%);
  --ring: hsl(213, 100%, 50%);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@keyframes pulse-glow {
  0%, 100% { 
    transform: scale(1);
  }
  50% { 
    transform: scale(1.05);
  }
}

@keyframes orbit {
  0% { transform: rotate(0deg) translateX(80px) rotate(0deg); }
  100% { transform: rotate(360deg) translateX(80px) rotate(-360deg); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes breathing {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.1); opacity: 1; }
}

.orb-container {
  position: relative;
  width: 200px;
  height: 200px;
  margin: 0 auto;
}

.ai-orb {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  position: relative;
  animation: pulse-glow 2s ease-in-out infinite;
}

.ai-orb::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(0, 230, 184, 0.1), transparent 70%);
  animation: breathing 4s ease-in-out infinite;
}

.orb-particles {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 4px;
  height: 4px;
  background: rgba(0, 230, 184, 0.8);
  border-radius: 50%;
  animation: orbit 8s linear infinite;
}

.particle-1 { animation-delay: 0s; }
.particle-2 { animation-delay: 1s; }
.particle-3 { animation-delay: 2s; }
.particle-4 { animation-delay: 3s; }

.holographic-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: float 3s ease-in-out infinite;
}
