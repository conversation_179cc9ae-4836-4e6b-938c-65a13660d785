/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Duration of the generated podcast. Must be one of:
 * short - produces podcasts shorter than 3 minutes.
 * default - produces podcasts roughly between 3-7 minutes.
 * long - prodces podcasts longer than 7 minutes.
 */
export type BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale = "short" | "default" | "long";
export declare const BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale: {
    readonly Short: "short";
    readonly Default: "default";
    readonly Long: "long";
};
