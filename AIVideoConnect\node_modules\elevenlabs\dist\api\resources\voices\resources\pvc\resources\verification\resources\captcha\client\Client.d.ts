/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as environments from "../../../../../../../../../../environments";
import * as core from "../../../../../../../../../../core";
import * as ElevenLabs from "../../../../../../../../../index";
export declare namespace Captcha {
    interface Options {
        environment?: core.Supplier<environments.ElevenLabsEnvironment | environments.ElevenLabsEnvironmentUrls>;
        /** Specify a custom URL to connect the client to. */
        baseUrl?: core.Supplier<string>;
        /** Override the xi-api-key header */
        apiKey?: core.Supplier<string | undefined>;
    }
    interface RequestOptions {
        /** The maximum time to wait for a response in seconds. */
        timeoutInSeconds?: number;
        /** The number of times to retry the request. Defaults to 2. */
        maxRetries?: number;
        /** A hook to abort the request. */
        abortSignal?: AbortSignal;
        /** Override the xi-api-key header */
        apiKey?: string | undefined;
        /** Additional headers to include in the request. */
        headers?: Record<string, string>;
    }
}
export declare class Captcha {
    protected readonly _options: Captcha.Options;
    constructor(_options?: Captcha.Options);
    /**
     * Get captcha for PVC voice verification.
     *
     * @param {string} voiceId - Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices to list all the available voices.
     * @param {Captcha.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.pvc.verification.captcha.get("21m00Tcm4TlvDq8ikWAM")
     */
    get(voiceId: string, requestOptions?: Captcha.RequestOptions): Promise<void>;
    /**
     * Submit captcha verification for PVC voice.
     *
     * @param {string} voiceId
     * @param {ElevenLabs.voices.pvc.verification.BodyVerifyPvcVoiceCaptchaV1VoicesPvcVoiceIdCaptchaPost} request
     * @param {Captcha.RequestOptions} requestOptions - Request-specific configuration.
     *
     * @throws {@link ElevenLabs.UnprocessableEntityError}
     *
     * @example
     *     await client.voices.pvc.verification.captcha.verify("21m00Tcm4TlvDq8ikWAM", {
     *         recording: fs.createReadStream("/path/to/your/file")
     *     })
     */
    verify(voiceId: string, request: ElevenLabs.voices.pvc.verification.BodyVerifyPvcVoiceCaptchaV1VoicesPvcVoiceIdCaptchaPost, requestOptions?: Captcha.RequestOptions): Promise<ElevenLabs.VerifyPvcVoiceCaptchaResponseModel>;
}
