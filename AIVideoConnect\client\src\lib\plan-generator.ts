// Personalized Plan Generation System
import { GoalAttainmentScore } from './goal-attainment-scoring';

export interface WorkoutPlan {
  id: string;
  name: string;
  description: string;
  duration: string; // e.g., "12 weeks"
  frequency: string; // e.g., "3x per week"
  
  // Weekly structure
  weeklySchedule: WeeklySchedule;
  
  // Exercise library
  exercises: Exercise[];
  
  // Progression plan
  progression: ProgressionPlan;
  
  // Nutrition guidelines
  nutrition: NutritionGuidelines;
  
  // Success metrics
  metrics: SuccessMetrics;
}

export interface WeeklySchedule {
  totalSessions: number;
  sessionTypes: SessionType[];
  restDays: number[];
}

export interface SessionType {
  id: string;
  name: string;
  duration: number; // minutes
  exercises: string[]; // exercise IDs
  focus: string; // e.g., "strength", "cardio", "mobility"
}

export interface Exercise {
  id: string;
  name: string;
  category: string;
  equipment: string[];
  instructions: string;
  videoUrl?: string;
  
  // Programming
  sets?: number;
  reps?: string; // e.g., "8-12" or "30 seconds"
  rest?: string; // e.g., "60 seconds"
  
  // Modifications
  beginner?: string;
  advanced?: string;
  
  // Targeted muscles
  primaryMuscles: string[];
  secondaryMuscles: string[];
}

export interface ProgressionPlan {
  weeks: WeekProgression[];
  milestones: Milestone[];
}

export interface WeekProgression {
  week: number;
  focus: string;
  intensity: number; // 1-10 scale
  volume: number; // relative to baseline
  newExercises?: string[];
}

export interface Milestone {
  week: number;
  description: string;
  metrics: string[];
}

export interface NutritionGuidelines {
  calorieTarget?: number;
  macroSplit: {
    protein: number; // percentage
    carbs: number;
    fats: number;
  };
  mealTiming: string[];
  hydration: string;
  supplements?: string[];
}

export interface SuccessMetrics {
  primary: string; // main goal metric
  secondary: string[];
  trackingFrequency: string;
  expectedProgress: {
    week4: string;
    week8: string;
    week12: string;
  };
}

export class PersonalizedPlanGenerator {
  
  generatePlan(
    responses: Record<string, any>,
    movementData: Record<string, any>,
    gasScore: GoalAttainmentScore
  ): WorkoutPlan {
    
    const goal = this.parseGoal(responses.primary_goal?.answer || '');
    const timeAvailable = responses.time_availability?.answer || '3-4 days';
    const sessionDuration = responses.session_duration?.answer || '30-45 minutes';
    const equipment = responses.equipment_access?.answer || 'Basic (dumbbells, bands)';
    const experience = responses.experience_level?.answer || 'Beginner';
    
    // Generate plan based on assessment
    const plan: WorkoutPlan = {
      id: `plan_${Date.now()}`,
      name: this.generatePlanName(goal, experience),
      description: this.generatePlanDescription(goal, gasScore.GAS),
      duration: "12 weeks",
      frequency: this.mapTimeToFrequency(timeAvailable),
      
      weeklySchedule: this.createWeeklySchedule(timeAvailable, sessionDuration, goal),
      exercises: this.selectExercises(equipment, experience, goal, movementData),
      progression: this.createProgressionPlan(gasScore, goal),
      nutrition: this.createNutritionGuidelines(goal, responses),
      metrics: this.defineSuccessMetrics(goal)
    };
    
    return plan;
  }
  
  private parseGoal(goalText: string): string {
    const lowerGoal = goalText.toLowerCase();
    
    if (lowerGoal.includes('lose') || lowerGoal.includes('weight') || lowerGoal.includes('fat')) {
      return 'weight_loss';
    } else if (lowerGoal.includes('muscle') || lowerGoal.includes('build') || lowerGoal.includes('gain')) {
      return 'muscle_gain';
    } else if (lowerGoal.includes('strength') || lowerGoal.includes('strong')) {
      return 'strength';
    } else if (lowerGoal.includes('endurance') || lowerGoal.includes('cardio') || lowerGoal.includes('run')) {
      return 'endurance';
    } else if (lowerGoal.includes('tone') || lowerGoal.includes('lean')) {
      return 'body_recomposition';
    } else {
      return 'general_fitness';
    }
  }
  
  private generatePlanName(goal: string, experience: string): string {
    const goalNames = {
      weight_loss: 'Fat Loss Transformation',
      muscle_gain: 'Muscle Building Program',
      strength: 'Strength Development Plan',
      endurance: 'Cardio Conditioning Program',
      body_recomposition: 'Body Recomposition Plan',
      general_fitness: 'Complete Fitness Program'
    };
    
    const experiencePrefix = {
      'Beginner': 'Beginner\'s',
      'Some experience': 'Progressive',
      'Intermediate': 'Intermediate',
      'Advanced': 'Advanced'
    };
    
    return `${experiencePrefix[experience as keyof typeof experiencePrefix] || 'Custom'} ${goalNames[goal as keyof typeof goalNames] || 'Fitness Program'}`;
  }
  
  private generatePlanDescription(goal: string, gasScore: number): string {
    const confidence = gasScore >= 75 ? 'high' : gasScore >= 60 ? 'moderate' : 'building';
    
    const descriptions = {
      weight_loss: `A comprehensive fat loss program designed to help you shed pounds while maintaining muscle. With your ${confidence} success probability, we'll focus on sustainable habits and progressive overload.`,
      muscle_gain: `A structured muscle building program that will help you add lean mass effectively. Your assessment shows ${confidence} potential for growth with consistent effort.`,
      strength: `A strength-focused program that will build your power and confidence. Based on your assessment, you have ${confidence} potential for significant strength gains.`,
      endurance: `A cardio conditioning program that will improve your cardiovascular fitness and endurance. Your readiness indicates ${confidence} success potential.`,
      body_recomposition: `A balanced program combining fat loss and muscle gain for a leaner, stronger physique. Your assessment suggests ${confidence} success probability.`,
      general_fitness: `A well-rounded fitness program covering strength, cardio, and mobility. Your assessment shows ${confidence} potential for overall fitness improvement.`
    };
    
    return descriptions[goal as keyof typeof descriptions] || descriptions.general_fitness;
  }
  
  private mapTimeToFrequency(timeAvailable: string): string {
    const mapping = {
      '2-3 days': '3x per week',
      '4-5 days': '4x per week',
      '6-7 days': '5x per week'
    };
    return mapping[timeAvailable as keyof typeof mapping] || '3x per week';
  }
  
  private createWeeklySchedule(timeAvailable: string, sessionDuration: string, goal: string): WeeklySchedule {
    const sessions = timeAvailable === '2-3 days' ? 3 : timeAvailable === '4-5 days' ? 4 : 5;
    const duration = sessionDuration.includes('20-30') ? 25 : sessionDuration.includes('30-45') ? 37 : 50;
    
    const sessionTypes: SessionType[] = [];
    
    if (goal === 'strength') {
      sessionTypes.push(
        { id: 'upper', name: 'Upper Body Strength', duration, exercises: ['push_up', 'row', 'shoulder_press'], focus: 'strength' },
        { id: 'lower', name: 'Lower Body Strength', duration, exercises: ['squat', 'deadlift', 'lunge'], focus: 'strength' },
        { id: 'full', name: 'Full Body Power', duration, exercises: ['burpee', 'mountain_climber', 'plank'], focus: 'strength' }
      );
    } else if (goal === 'weight_loss') {
      sessionTypes.push(
        { id: 'hiit', name: 'HIIT Cardio', duration, exercises: ['jumping_jacks', 'burpee', 'high_knees'], focus: 'cardio' },
        { id: 'strength', name: 'Strength Circuit', duration, exercises: ['squat', 'push_up', 'row'], focus: 'strength' },
        { id: 'active', name: 'Active Recovery', duration, exercises: ['walk', 'stretch', 'yoga'], focus: 'mobility' }
      );
    } else {
      // General fitness
      sessionTypes.push(
        { id: 'strength', name: 'Strength Training', duration, exercises: ['squat', 'push_up', 'row'], focus: 'strength' },
        { id: 'cardio', name: 'Cardio Conditioning', duration, exercises: ['jumping_jacks', 'mountain_climber'], focus: 'cardio' },
        { id: 'mobility', name: 'Mobility & Recovery', duration, exercises: ['stretch', 'yoga', 'foam_roll'], focus: 'mobility' }
      );
    }
    
    return {
      totalSessions: sessions,
      sessionTypes: sessionTypes.slice(0, sessions),
      restDays: sessions === 3 ? [2, 4, 6] : sessions === 4 ? [2, 5] : [2]
    };
  }
  
  private selectExercises(equipment: string, experience: string, goal: string, movementData: Record<string, any>): Exercise[] {
    // This would be a comprehensive exercise database in a real implementation
    const baseExercises: Exercise[] = [
      {
        id: 'squat',
        name: 'Bodyweight Squat',
        category: 'Lower Body',
        equipment: ['None'],
        instructions: 'Stand with feet shoulder-width apart, lower down as if sitting back into a chair, then stand up.',
        sets: 3,
        reps: '8-12',
        rest: '60 seconds',
        beginner: 'Use a chair for support',
        advanced: 'Add jump or single leg',
        primaryMuscles: ['Quadriceps', 'Glutes'],
        secondaryMuscles: ['Hamstrings', 'Core']
      },
      {
        id: 'push_up',
        name: 'Push-up',
        category: 'Upper Body',
        equipment: ['None'],
        instructions: 'Start in plank position, lower chest to ground, push back up.',
        sets: 3,
        reps: '5-10',
        rest: '60 seconds',
        beginner: 'Knee push-ups or wall push-ups',
        advanced: 'Decline or single arm variations',
        primaryMuscles: ['Chest', 'Triceps'],
        secondaryMuscles: ['Shoulders', 'Core']
      }
      // More exercises would be added here...
    ];
    
    // Filter and modify based on equipment, experience, and movement assessment
    return baseExercises.map(exercise => {
      // Adjust reps/sets based on experience
      if (experience === 'Beginner') {
        exercise.sets = Math.max(2, (exercise.sets || 3) - 1);
      } else if (experience === 'Advanced') {
        exercise.sets = (exercise.sets || 3) + 1;
      }
      
      return exercise;
    });
  }
  
  private createProgressionPlan(gasScore: GoalAttainmentScore, goal: string): ProgressionPlan {
    const weeks: WeekProgression[] = [];
    
    // 12-week progression
    for (let week = 1; week <= 12; week++) {
      let intensity = 5; // baseline
      let volume = 1.0; // baseline
      
      if (week <= 2) {
        intensity = 4; // Easy start
        volume = 0.8;
      } else if (week <= 4) {
        intensity = 5;
        volume = 0.9;
      } else if (week <= 8) {
        intensity = 6;
        volume = 1.0;
      } else {
        intensity = 7;
        volume = 1.1;
      }
      
      weeks.push({
        week,
        focus: week <= 4 ? 'Foundation' : week <= 8 ? 'Development' : 'Peak',
        intensity,
        volume
      });
    }
    
    const milestones: Milestone[] = [
      {
        week: 4,
        description: 'Foundation Complete',
        metrics: ['Form mastery', 'Consistency established']
      },
      {
        week: 8,
        description: 'Midpoint Assessment',
        metrics: ['Strength gains', 'Body composition changes']
      },
      {
        week: 12,
        description: 'Goal Achievement',
        metrics: ['Final measurements', 'Performance tests']
      }
    ];
    
    return { weeks, milestones };
  }
  
  private createNutritionGuidelines(goal: string, responses: Record<string, any>): NutritionGuidelines {
    const macroSplits = {
      weight_loss: { protein: 35, carbs: 35, fats: 30 },
      muscle_gain: { protein: 30, carbs: 45, fats: 25 },
      strength: { protein: 30, carbs: 40, fats: 30 },
      endurance: { protein: 20, carbs: 55, fats: 25 },
      body_recomposition: { protein: 35, carbs: 35, fats: 30 },
      general_fitness: { protein: 25, carbs: 45, fats: 30 }
    };
    
    return {
      macroSplit: macroSplits[goal as keyof typeof macroSplits] || macroSplits.general_fitness,
      mealTiming: [
        'Eat protein with every meal',
        'Time carbs around workouts',
        'Stay consistent with meal timing'
      ],
      hydration: 'Aim for 8-10 glasses of water daily',
      supplements: goal === 'muscle_gain' ? ['Protein powder', 'Creatine'] : ['Multivitamin']
    };
  }
  
  private defineSuccessMetrics(goal: string): SuccessMetrics {
    const metrics = {
      weight_loss: {
        primary: 'Body weight',
        secondary: ['Body fat percentage', 'Waist circumference', 'Energy levels'],
        expectedProgress: {
          week4: '3-5 lbs lost',
          week8: '6-10 lbs lost',
          week12: '10-15 lbs lost'
        }
      },
      muscle_gain: {
        primary: 'Lean body mass',
        secondary: ['Strength gains', 'Muscle measurements', 'Performance'],
        expectedProgress: {
          week4: '2-3 lbs gained',
          week8: '4-6 lbs gained',
          week12: '6-10 lbs gained'
        }
      },
      strength: {
        primary: 'Strength benchmarks',
        secondary: ['Exercise progression', 'Form quality', 'Confidence'],
        expectedProgress: {
          week4: '20% strength increase',
          week8: '40% strength increase',
          week12: '60% strength increase'
        }
      }
    };
    
    const defaultMetrics = {
      primary: 'Overall fitness',
      secondary: ['Strength', 'Endurance', 'Flexibility'],
      expectedProgress: {
        week4: 'Improved consistency',
        week8: 'Noticeable improvements',
        week12: 'Significant transformation'
      }
    };
    
    return {
      ...(metrics[goal as keyof typeof metrics] || defaultMetrics),
      trackingFrequency: 'Weekly check-ins'
    };
  }
}
