import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface AssessmentResultsProps {
  isOpen: boolean;
  onClose: () => void;
  session: any;
}

export default function AssessmentResults({ isOpen, onClose, session }: AssessmentResultsProps) {
  const [showStripeForm, setShowStripeForm] = useState(false);

  const createPlanMutation = useMutation({
    mutationFn: async (isCoached: boolean) => {
      const response = await apiRequest("POST", "/api/plans", {
        userId: "demo-user",
        sessionId: session?.id,
        planData: session?.plan,
        isCoached,
      });
      return response.json();
    },
    onSuccess: () => {
      onClose();
    },
  });

  const startSubscriptionMutation = useMutation({
    mutationFn: async () => {
      const response = await apiRequest("POST", "/api/create-subscription", {
        userId: "demo-user",
        email: "<EMAIL>",
        username: "Demo User",
      });
      return response.json();
    },
    onSuccess: (data) => {
      // In a real app, redirect to Stripe Checkout
      console.log("Subscription created:", data);
      setShowStripeForm(true);
    },
  });

  if (!isOpen || !session) return null;

  const scores = session.scores || {};
  const plan = session.plan || {};

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="max-w-4xl w-full max-h-[90vh] overflow-y-auto" data-testid="modal-assessment-results">
        <CardContent className="p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <h2 className="font-display text-3xl font-bold mb-2">Your Personalized Plan</h2>
            <p className="text-muted-foreground">Based on your assessment, movement analysis, and goals</p>
          </div>
          
          {/* Goal Attainability Score */}
          <div className="bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl p-6 mb-8 border border-primary/20">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="font-display text-xl font-semibold mb-1">Goal Attainability</h3>
                <p className="text-sm text-muted-foreground">Your likelihood of success with this plan</p>
              </div>
              <div className="text-right">
                <div className="text-4xl font-bold text-primary" data-testid="text-gas-score">
                  {session.gasScore || scores.GAS || 67}%
                </div>
                <div className="text-sm text-muted-foreground">Current odds</div>
              </div>
            </div>
            
            {/* Score Breakdown */}
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-secondary" data-testid="text-time-capacity">
                  {scores.TC || 72}
                </div>
                <div className="text-xs text-muted-foreground">Time Capacity</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-accent" data-testid="text-movement-quality">
                  {scores.MQ || 64}
                </div>
                <div className="text-xs text-muted-foreground">Movement Quality</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary" data-testid="text-recovery-readiness">
                  {scores.RR || 58}
                </div>
                <div className="text-xs text-muted-foreground">Recovery</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-accent" data-testid="text-environment-fit">
                  {scores.EF || 80}
                </div>
                <div className="text-xs text-muted-foreground">Environment</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-secondary" data-testid="text-adherence-signals">
                  {scores.AS || 70}
                </div>
                <div className="text-xs text-muted-foreground">Adherence</div>
              </div>
            </div>
            
            {/* Improvement Suggestion */}
            <div className="bg-accent/10 rounded-xl p-4 border border-accent/20">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 rounded-full bg-accent flex items-center justify-center flex-shrink-0">
                  <svg className="w-4 h-4 text-accent-foreground" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,20H6C4.89,20 4,19.1 4,18V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z"/>
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-accent mb-1">Boost to 78% success rate</div>
                  <div className="text-sm text-foreground">Weekly form check-ins and auto-adjustments can raise your probability by 11 points</div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Plan Details */}
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            
            {/* Training Plan */}
            <div>
              <h3 className="font-display text-xl font-semibold mb-4">Training Schedule</h3>
              <div className="space-y-4">
                
                <div className="bg-muted/30 rounded-xl p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-medium">4 Days per Week</span>
                    <span className="text-sm text-muted-foreground">28-32 min sessions</span>
                  </div>
                  <div className="text-sm text-muted-foreground">2 Strength • 1 Conditioning • 1 Mobility</div>
                </div>
                
                <Card className="p-4">
                  <div className="font-medium mb-3">Sample Strength Session</div>
                  <div className="space-y-2 text-sm">
                    {plan.sampleWorkout?.exercises?.map((exercise: any, index: number) => (
                      <div key={index} className="flex justify-between">
                        <span>{exercise.name}</span>
                        <span className="text-muted-foreground">{exercise.sets}</span>
                      </div>
                    )) || (
                      <>
                        <div className="flex justify-between">
                          <span>Goblet Squats</span>
                          <span className="text-muted-foreground">4×8 @ RPE 7</span>
                        </div>
                        <div className="flex justify-between">
                          <span>DB Romanian Deadlifts</span>
                          <span className="text-muted-foreground">3×10 @ RPE 7</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Push-ups</span>
                          <span className="text-muted-foreground">3×AMRAP</span>
                        </div>
                      </>
                    )}
                  </div>
                </Card>
              </div>
            </div>
            
            {/* Nutrition & Progress */}
            <div>
              <h3 className="font-display text-xl font-semibold mb-4">Nutrition & Targets</h3>
              <div className="space-y-4">
                
                <div className="bg-muted/30 rounded-xl p-4">
                  <div className="font-medium mb-2">Daily Pattern</div>
                  <div className="text-sm text-muted-foreground">3 meals + 1 snack • ~500 cal deficit</div>
                  <div className="text-sm text-muted-foreground mt-1">Protein: 0.8-1.0g per lb goal weight</div>
                </div>
                
                <Card className="p-4">
                  <div className="font-medium mb-3">Expected Progress</div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">4 weeks</span>
                      <Badge variant="secondary">-3 to -6 lbs</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">8 weeks</span>
                      <Badge variant="secondary">-6 to -10 lbs</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">12 weeks</span>
                      <Badge variant="secondary">-8 to -12 lbs</Badge>
                    </div>
                  </div>
                </Card>
              </div>
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex flex-col md:flex-row gap-4">
            <Button 
              variant="outline"
              className="flex-1"
              onClick={() => createPlanMutation.mutate(false)}
              disabled={createPlanMutation.isPending}
              data-testid="button-save-basic-plan"
            >
              <div className="text-center">
                <div>Save Basic Plan</div>
                <div className="text-sm text-muted-foreground">Get your plan and track progress</div>
              </div>
            </Button>
            
            <Button 
              className="flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90"
              onClick={() => startSubscriptionMutation.mutate()}
              disabled={startSubscriptionMutation.isPending}
              data-testid="button-start-coached-trial"
            >
              <div className="text-center">
                <div>Start Coached Trial</div>
                <div className="text-sm opacity-80">7 days free • Weekly tune-ups</div>
              </div>
            </Button>
          </div>

          {showStripeForm && (
            <div className="mt-6 p-4 bg-muted/50 rounded-xl border border-border">
              <div className="text-center">
                <p className="text-sm text-muted-foreground mb-2">
                  Subscription created successfully!
                </p>
                <p className="text-xs text-muted-foreground">
                  In a production app, this would redirect to Stripe Checkout.
                </p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
