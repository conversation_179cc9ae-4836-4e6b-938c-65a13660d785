// Goal Attainment Scoring System - Calculates realistic goal achievement percentages
export interface GoalAttainmentFactors {
  // Movement Quality (MQ) - 0-100
  movementQuality: number;
  
  // Readiness Rating (RR) - 0-100  
  readinessRating: number;
  
  // Engagement Factor (EF) - 0-100
  engagementFactor: number;
  
  // Adherence Score (AS) - 0-100
  adherenceScore: number;
  
  // Time Commitment (TC) - 0-100
  timeCommitment: number;
}

export interface GoalAttainmentScore {
  // Goal Attainment Score (GAS) - final percentage
  GAS: number;
  
  // Individual factor scores
  factors: GoalAttainmentFactors;
  
  // Confidence intervals
  confidence: {
    low: number;    // 25th percentile
    high: number;   // 75th percentile
  };
  
  // Key limiting factors
  limitingFactors: string[];
  
  // Recommendations to improve score
  improvements: string[];
}

export class GoalAttainmentCalculator {
  
  // Calculate Movement Quality score from pose detection data
  calculateMovementQuality(movementData: Record<string, any>): number {
    let totalScore = 0;
    let exerciseCount = 0;
    
    // Analyze each exercise
    for (const [exerciseId, data] of Object.entries(movementData)) {
      let exerciseScore = 50; // baseline
      
      switch (exerciseId) {
        case 'air_squat':
          exerciseScore = this.analyzeSquatQuality(data);
          break;
        case 'forward_fold':
          exerciseScore = this.analyzeFoldQuality(data);
          break;
        case 'shoulder_reach':
          exerciseScore = this.analyzeShoulderQuality(data);
          break;
      }
      
      totalScore += exerciseScore;
      exerciseCount++;
    }
    
    return exerciseCount > 0 ? totalScore / exerciseCount : 50;
  }
  
  private analyzeSquatQuality(data: any): number {
    // Simplified analysis - in real implementation would use pose landmarks
    let score = 70; // baseline for attempting the exercise
    
    if (data.poses && data.poses.length > 0) {
      const pose = data.poses[0];
      
      // Check for key movement patterns
      if (pose.score > 0.7) score += 10; // Good pose detection confidence
      if (data.repsCompleted >= 5) score += 10; // Completed all reps
      if (data.formFeedback?.includes('good')) score += 10; // Positive form feedback
    }
    
    return Math.min(score, 100);
  }
  
  private analyzeFoldQuality(data: any): number {
    let score = 70;
    
    if (data.poses && data.poses.length > 0) {
      const pose = data.poses[0];
      if (pose.score > 0.7) score += 15; // Good flexibility demonstration
    }
    
    return Math.min(score, 100);
  }
  
  private analyzeShoulderQuality(data: any): number {
    let score = 70;
    
    if (data.poses && data.poses.length > 0) {
      const pose = data.poses[0];
      if (pose.score > 0.7) score += 15; // Good shoulder mobility
    }
    
    return Math.min(score, 100);
  }
  
  // Calculate Readiness Rating from assessment responses
  calculateReadinessRating(responses: Record<string, any>): number {
    let score = 50; // baseline
    
    // Motivation level (high impact)
    if (responses.motivation_level?.answer >= 8) score += 20;
    else if (responses.motivation_level?.answer >= 6) score += 10;
    else if (responses.motivation_level?.answer >= 4) score += 5;
    
    // Experience level
    const experience = responses.experience_level?.answer;
    if (experience === 'Advanced') score += 15;
    else if (experience === 'Intermediate') score += 10;
    else if (experience === 'Some experience') score += 5;
    
    // Equipment access
    const equipment = responses.equipment_access?.answer;
    if (equipment === 'Full gym access') score += 10;
    else if (equipment === 'Home gym setup') score += 8;
    else if (equipment === 'Basic (dumbbells, bands)') score += 5;
    
    // Injury considerations (negative impact)
    if (responses.past_injuries?.answer && responses.past_injuries.answer.length > 10) {
      score -= 10; // Has significant injury history
    }
    
    return Math.min(Math.max(score, 0), 100);
  }
  
  // Calculate Engagement Factor from voice/facial analysis
  calculateEngagementFactor(sessionData: any): number {
    let score = 70; // baseline assumption of good engagement
    
    // Voice analysis (if available)
    if (sessionData.voiceMetrics) {
      const voice = sessionData.voiceMetrics;
      if (voice.averageEnergy > 0.6) score += 10;
      if (voice.responseLatency < 2000) score += 5; // Quick responses
      if (voice.totalSpeakingTime > 60000) score += 5; // Engaged in conversation
    }
    
    // Facial engagement (if available and consented)
    if (sessionData.facialMetrics && sessionData.consentToFacialAnalysis) {
      const facial = sessionData.facialMetrics;
      if (facial.averageSmileIntensity > 0.3) score += 10;
      if (facial.eyeContactPercentage > 0.7) score += 5;
    }
    
    // Session completion rate
    if (sessionData.completionRate > 0.9) score += 10;
    else if (sessionData.completionRate > 0.7) score += 5;
    
    return Math.min(score, 100);
  }
  
  // Calculate Adherence Score from time commitment and lifestyle factors
  calculateAdherenceScore(responses: Record<string, any>): number {
    let score = 60; // baseline
    
    // Time availability (realistic expectations)
    const timeAvailable = responses.time_availability?.answer;
    const sessionDuration = responses.session_duration?.answer;
    
    // Realistic time commitment gets higher score
    if (timeAvailable === '2-3 days' && sessionDuration === '20-30 minutes') score += 20;
    else if (timeAvailable === '4-5 days' && sessionDuration === '30-45 minutes') score += 15;
    else if (timeAvailable === '6-7 days') score -= 5; // Often unrealistic
    
    // Goal specificity
    const goal = responses.primary_goal?.answer || '';
    if (goal.length > 20 && (goal.includes('lose') || goal.includes('gain') || goal.includes('build'))) {
      score += 10; // Specific, actionable goal
    }
    
    return Math.min(Math.max(score, 0), 100);
  }
  
  // Calculate Time Commitment score
  calculateTimeCommitment(responses: Record<string, any>): number {
    let score = 50;
    
    const timeAvailable = responses.time_availability?.answer;
    const sessionDuration = responses.session_duration?.answer;
    
    // Calculate weekly time commitment
    let weeklyMinutes = 0;
    
    if (timeAvailable === '2-3 days') weeklyMinutes = 2.5;
    else if (timeAvailable === '4-5 days') weeklyMinutes = 4.5;
    else if (timeAvailable === '6-7 days') weeklyMinutes = 6.5;
    
    if (sessionDuration === '20-30 minutes') weeklyMinutes *= 25;
    else if (sessionDuration === '30-45 minutes') weeklyMinutes *= 37.5;
    else if (sessionDuration === '45-60 minutes') weeklyMinutes *= 52.5;
    else if (sessionDuration === '60+ minutes') weeklyMinutes *= 70;
    
    // Score based on total weekly commitment
    if (weeklyMinutes >= 150) score = 90; // Excellent commitment
    else if (weeklyMinutes >= 120) score = 80; // Very good
    else if (weeklyMinutes >= 90) score = 70; // Good
    else if (weeklyMinutes >= 60) score = 60; // Adequate
    else score = 40; // May be insufficient
    
    return score;
  }
  
  // Main calculation method
  calculateGoalAttainmentScore(
    responses: Record<string, any>,
    movementData: Record<string, any>,
    sessionData: any
  ): GoalAttainmentScore {
    
    const factors: GoalAttainmentFactors = {
      movementQuality: this.calculateMovementQuality(movementData),
      readinessRating: this.calculateReadinessRating(responses),
      engagementFactor: this.calculateEngagementFactor(sessionData),
      adherenceScore: this.calculateAdherenceScore(responses),
      timeCommitment: this.calculateTimeCommitment(responses)
    };
    
    // Weighted average for final GAS
    const weights = {
      movementQuality: 0.15,
      readinessRating: 0.25,
      engagementFactor: 0.20,
      adherenceScore: 0.25,
      timeCommitment: 0.15
    };
    
    const GAS = Math.round(
      factors.movementQuality * weights.movementQuality +
      factors.readinessRating * weights.readinessRating +
      factors.engagementFactor * weights.engagementFactor +
      factors.adherenceScore * weights.adherenceScore +
      factors.timeCommitment * weights.timeCommitment
    );
    
    // Calculate confidence intervals (±10% typically)
    const confidence = {
      low: Math.max(GAS - 10, 0),
      high: Math.min(GAS + 10, 100)
    };
    
    // Identify limiting factors
    const limitingFactors: string[] = [];
    const improvements: string[] = [];
    
    if (factors.movementQuality < 60) {
      limitingFactors.push('Movement Quality');
      improvements.push('Focus on form and mobility work');
    }
    if (factors.readinessRating < 60) {
      limitingFactors.push('Readiness');
      improvements.push('Build consistency with easier workouts first');
    }
    if (factors.engagementFactor < 60) {
      limitingFactors.push('Engagement');
      improvements.push('Find activities you truly enjoy');
    }
    if (factors.adherenceScore < 60) {
      limitingFactors.push('Adherence');
      improvements.push('Set more realistic time commitments');
    }
    if (factors.timeCommitment < 60) {
      limitingFactors.push('Time Commitment');
      improvements.push('Increase weekly training frequency or duration');
    }
    
    return {
      GAS,
      factors,
      confidence,
      limitingFactors,
      improvements
    };
  }
}
