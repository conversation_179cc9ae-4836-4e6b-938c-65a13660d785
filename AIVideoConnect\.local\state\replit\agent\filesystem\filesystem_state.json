{"file_contents": {"drizzle.config.ts": {"content": "import { defineConfig } from \"drizzle-kit\";\n\nif (!process.env.DATABASE_URL) {\n  throw new Error(\"DATABASE_URL, ensure the database is provisioned\");\n}\n\nexport default defineConfig({\n  out: \"./migrations\",\n  schema: \"./shared/schema.ts\",\n  dialect: \"postgresql\",\n  dbCredentials: {\n    url: process.env.DATABASE_URL,\n  },\n});\n", "size_bytes": 325}, "postcss.config.js": {"content": "export default {\n  plugins: {\n    tailwindcss: {},\n    autoprefixer: {},\n  },\n}\n", "size_bytes": 80}, "tailwind.config.ts": {"content": "import type { Config } from \"tailwindcss\";\n\nexport default {\n  darkMode: [\"class\"],\n  content: [\"./client/index.html\", \"./client/src/**/*.{js,jsx,ts,tsx}\"],\n  theme: {\n    extend: {\n      borderRadius: {\n        lg: \"var(--radius)\",\n        md: \"calc(var(--radius) - 2px)\",\n        sm: \"calc(var(--radius) - 4px)\",\n      },\n      colors: {\n        background: \"var(--background)\",\n        foreground: \"var(--foreground)\",\n        card: {\n          DEFAULT: \"var(--card)\",\n          foreground: \"var(--card-foreground)\",\n        },\n        popover: {\n          DEFAULT: \"var(--popover)\",\n          foreground: \"var(--popover-foreground)\",\n        },\n        primary: {\n          DEFAULT: \"var(--primary)\",\n          foreground: \"var(--primary-foreground)\",\n        },\n        secondary: {\n          DEFAULT: \"var(--secondary)\",\n          foreground: \"var(--secondary-foreground)\",\n        },\n        muted: {\n          DEFAULT: \"var(--muted)\",\n          foreground: \"var(--muted-foreground)\",\n        },\n        accent: {\n          DEFAULT: \"var(--accent)\",\n          foreground: \"var(--accent-foreground)\",\n        },\n        destructive: {\n          DEFAULT: \"var(--destructive)\",\n          foreground: \"var(--destructive-foreground)\",\n        },\n        border: \"var(--border)\",\n        input: \"var(--input)\",\n        ring: \"var(--ring)\",\n        chart: {\n          \"1\": \"var(--chart-1)\",\n          \"2\": \"var(--chart-2)\",\n          \"3\": \"var(--chart-3)\",\n          \"4\": \"var(--chart-4)\",\n          \"5\": \"var(--chart-5)\",\n        },\n        sidebar: {\n          DEFAULT: \"var(--sidebar-background)\",\n          foreground: \"var(--sidebar-foreground)\",\n          primary: \"var(--sidebar-primary)\",\n          \"primary-foreground\": \"var(--sidebar-primary-foreground)\",\n          accent: \"var(--sidebar-accent)\",\n          \"accent-foreground\": \"var(--sidebar-accent-foreground)\",\n          border: \"var(--sidebar-border)\",\n          ring: \"var(--sidebar-ring)\",\n        },\n      },\n      fontFamily: {\n        sans: [\"var(--font-sans)\"],\n        serif: [\"var(--font-serif)\"],\n        mono: [\"var(--font-mono)\"],\n        display: [\"var(--font-display)\"],\n      },\n      keyframes: {\n        \"accordion-down\": {\n          from: {\n            height: \"0\",\n          },\n          to: {\n            height: \"var(--radix-accordion-content-height)\",\n          },\n        },\n        \"accordion-up\": {\n          from: {\n            height: \"var(--radix-accordion-content-height)\",\n          },\n          to: {\n            height: \"0\",\n          },\n        },\n        \"pulse-glow\": {\n          \"0%, 100%\": {\n            transform: \"scale(1)\",\n          },\n          \"50%\": {\n            transform: \"scale(1.05)\",\n          },\n        },\n        \"orbit\": {\n          \"0%\": { transform: \"rotate(0deg) translateX(80px) rotate(0deg)\" },\n          \"100%\": { transform: \"rotate(360deg) translateX(80px) rotate(-360deg)\" },\n        },\n        \"float\": {\n          \"0%, 100%\": { transform: \"translateY(0px)\" },\n          \"50%\": { transform: \"translateY(-10px)\" },\n        },\n        \"breathing\": {\n          \"0%, 100%\": { transform: \"scale(1)\", opacity: \"0.8\" },\n          \"50%\": { transform: \"scale(1.1)\", opacity: \"1\" },\n        },\n      },\n      animation: {\n        \"accordion-down\": \"accordion-down 0.2s ease-out\",\n        \"accordion-up\": \"accordion-up 0.2s ease-out\",\n        \"pulse-glow\": \"pulse-glow 2s ease-in-out infinite\",\n        \"orbit\": \"orbit 8s linear infinite\",\n        \"float\": \"float 3s ease-in-out infinite\",\n        \"breathing\": \"breathing 4s ease-in-out infinite\",\n      },\n    },\n  },\n  plugins: [require(\"tailwindcss-animate\"), require(\"@tailwindcss/typography\")],\n} satisfies Config;\n", "size_bytes": 3687}, "vite.config.ts": {"content": "import { defineConfig } from \"vite\";\nimport react from \"@vitejs/plugin-react\";\nimport path from \"path\";\nimport runtimeErrorOverlay from \"@replit/vite-plugin-runtime-error-modal\";\n\nexport default defineConfig({\n  plugins: [\n    react(),\n    runtimeErrorOverlay(),\n    ...(process.env.NODE_ENV !== \"production\" &&\n    process.env.REPL_ID !== undefined\n      ? [\n          await import(\"@replit/vite-plugin-cartographer\").then((m) =>\n            m.cartographer(),\n          ),\n          await import(\"@replit/vite-plugin-dev-banner\").then((m) =>\n            m.devBanner(),\n          ),\n        ]\n      : []),\n  ],\n  resolve: {\n    alias: {\n      \"@\": path.resolve(import.meta.dirname, \"client\", \"src\"),\n      \"@shared\": path.resolve(import.meta.dirname, \"shared\"),\n      \"@assets\": path.resolve(import.meta.dirname, \"attached_assets\"),\n    },\n  },\n  root: path.resolve(import.meta.dirname, \"client\"),\n  build: {\n    outDir: path.resolve(import.meta.dirname, \"dist/public\"),\n    emptyOutDir: true,\n  },\n  server: {\n    fs: {\n      strict: true,\n      deny: [\"**/.*\"],\n    },\n  },\n});\n", "size_bytes": 1080}, "server/db.ts": {"content": "import { Pool, neonConfig } from '@neondatabase/serverless';\nimport { drizzle } from 'drizzle-orm/neon-serverless';\nimport ws from \"ws\";\nimport * as schema from \"@shared/schema\";\n\nneonConfig.webSocketConstructor = ws;\n\nif (!process.env.DATABASE_URL) {\n  throw new Error(\n    \"DATABASE_URL must be set. Did you forget to provision a database?\",\n  );\n}\n\nexport const pool = new Pool({ connectionString: process.env.DATABASE_URL });\nexport const db = drizzle({ client: pool, schema });", "size_bytes": 482}, "server/index.ts": {"content": "import express, { type Request, Response, NextFunction } from \"express\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\n\nconst app = express();\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\n(async () => {\n  const server = await registerRoutes(app);\n\n  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n    const status = err.status || err.statusCode || 500;\n    const message = err.message || \"Internal Server Error\";\n\n    res.status(status).json({ message });\n    throw err;\n  });\n\n  // importantly only setup vite in development and after\n  // setting up all the other routes so the catch-all route\n  // doesn't interfere with the other routes\n  if (app.get(\"env\") === \"development\") {\n    await setupVite(app, server);\n  } else {\n    serveStatic(app);\n  }\n\n  // ALWAYS serve the app on the port specified in the environment variable PORT\n  // Other ports are firewalled. Default to 5000 if not specified.\n  // this serves both the API and the client.\n  // It is the only port that is not firewalled.\n  const port = parseInt(process.env.PORT || '5000', 10);\n  server.listen({\n    port,\n    host: \"0.0.0.0\",\n    reusePort: true,\n  }, () => {\n    log(`serving on port ${port}`);\n  });\n})();\n", "size_bytes": 2066}, "server/routes.ts": {"content": "import type { Express } from \"express\";\nimport { createServer, type Server } from \"http\";\nimport { WebSocketServer, WebSocket } from \"ws\";\nimport <PERSON><PERSON> from \"stripe\";\nimport { storage } from \"./storage\";\nimport { assessmentService } from \"./services/assessment\";\nimport { insertAssessmentSessionSchema } from \"@shared/schema\";\n\n// Extend WebSocket interface to include sessionId\ninterface ExtendedWebSocket extends WebSocket {\n  sessionId?: string;\n}\n\n// Initialize Stripe only if secret key is available\nconst stripe = process.env.STRIPE_SECRET_KEY \n  ? new Stripe(process.env.STRIPE_SECRET_KEY, {\n      apiVersion: \"2025-08-27.basil\",\n    })\n  : null;\n\nexport async function registerRoutes(app: Express): Promise<Server> {\n  const httpServer = createServer(app);\n\n  // WebSocket server for real-time communication\n  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });\n  \n  wss.on('connection', (ws) => {\n    console.log('WebSocket client connected');\n    \n    ws.on('message', async (message) => {\n      try {\n        const data = JSON.parse(message.toString());\n        \n        switch (data.type) {\n          case 'join_session':\n            ws.sessionId = data.sessionId;\n            ws.send(JSON.stringify({ type: 'session_joined', sessionId: data.sessionId }));\n            break;\n            \n          case 'speech_data':\n            // Handle speech-to-text data\n            if (ws.sessionId) {\n              await storage.createSessionEvent({\n                sessionId: ws.sessionId,\n                eventType: 'speech_detected',\n                eventData: { transcript: data.transcript, confidence: data.confidence }\n              });\n              \n              // Broadcast to other clients in session\n              wss.clients.forEach(client => {\n                if (client !== ws && client.readyState === WebSocket.OPEN && client.sessionId === ws.sessionId) {\n                  client.send(JSON.stringify({ type: 'speech_update', data: data }));\n                }\n              });\n            }\n            break;\n            \n          case 'pose_data':\n            // Handle pose detection data\n            if (ws.sessionId) {\n              await storage.createSessionEvent({\n                sessionId: ws.sessionId,\n                eventType: 'pose_detected',\n                eventData: data.poses\n              });\n            }\n            break;\n            \n          case 'tts_audio':\n            // Handle TTS audio analysis for orb animation\n            if (ws.sessionId) {\n              wss.clients.forEach(client => {\n                if (client !== ws && client.readyState === WebSocket.OPEN && client.sessionId === ws.sessionId) {\n                  client.send(JSON.stringify({ type: 'orb_animation', data: data.audioData }));\n                }\n              });\n            }\n            break;\n        }\n      } catch (error) {\n        console.error('WebSocket message error:', error);\n      }\n    });\n    \n    ws.on('close', () => {\n      console.log('WebSocket client disconnected');\n    });\n  });\n\n  // Assessment session endpoints\n  app.post(\"/api/assessment/start\", async (req, res) => {\n    try {\n      const { userId } = req.body;\n      \n      // Ensure the demo user exists - check by username first\n      let user = await storage.getUserByUsername(userId);\n      if (!user) {\n        user = await storage.createUser({\n          username: userId,\n          password: \"demo-password\", // In a real app, this would be properly handled\n          email: `${userId}@demo.com`\n        });\n      }\n      \n      const session = await storage.createAssessmentSession({\n        userId: user.id,\n        status: \"active\",\n        phase: \"welcome\"\n      });\n      \n      res.json(session);\n    } catch (error: any) {\n      res.status(500).json({ message: \"Error starting assessment: \" + error.message });\n    }\n  });\n\n  app.get(\"/api/assessment/:id\", async (req, res) => {\n    try {\n      const session = await storage.getAssessmentSession(req.params.id);\n      if (!session) {\n        return res.status(404).json({ message: \"Session not found\" });\n      }\n      res.json(session);\n    } catch (error: any) {\n      res.status(500).json({ message: \"Error fetching session: \" + error.message });\n    }\n  });\n\n  app.post(\"/api/assessment/:id/intake\", async (req, res) => {\n    try {\n      const { answers } = req.body;\n      \n      const session = await storage.updateAssessmentSession(req.params.id, {\n        intake: answers,\n        phase: \"movement\"\n      });\n      \n      res.json(session);\n    } catch (error: any) {\n      res.status(500).json({ message: \"Error updating intake: \" + error.message });\n    }\n  });\n\n  app.post(\"/api/assessment/:id/movement\", async (req, res) => {\n    try {\n      const { movementData } = req.body;\n      \n      const session = await storage.updateAssessmentSession(req.params.id, {\n        signals: { movement: movementData },\n        phase: \"photo\"\n      });\n      \n      res.json(session);\n    } catch (error: any) {\n      res.status(500).json({ message: \"Error updating movement data: \" + error.message });\n    }\n  });\n\n  app.post(\"/api/assessment/:id/photos\", async (req, res) => {\n    try {\n      const { photos } = req.body;\n      \n      const session = await storage.getAssessmentSession(req.params.id);\n      if (!session) {\n        return res.status(404).json({ message: \"Session not found\" });\n      }\n      \n      const updatedSignals = { ...session.signals, photos };\n      \n      const updatedSession = await storage.updateAssessmentSession(req.params.id, {\n        signals: updatedSignals,\n        phase: \"reveal\"\n      });\n      \n      res.json(updatedSession);\n    } catch (error: any) {\n      res.status(500).json({ message: \"Error updating photos: \" + error.message });\n    }\n  });\n\n  app.post(\"/api/assessment/:id/complete\", async (req, res) => {\n    try {\n      const session = await storage.getAssessmentSession(req.params.id);\n      if (!session) {\n        return res.status(404).json({ message: \"Session not found\" });\n      }\n      \n      // Generate assessment scores and plan\n      const scores = await assessmentService.calculateScores(session);\n      const plan = await assessmentService.generatePlan(session, scores);\n      \n      const completedSession = await storage.updateAssessmentSession(req.params.id, {\n        scores,\n        plan,\n        gasScore: scores.GAS,\n        status: \"completed\",\n        phase: \"complete\",\n        completedAt: new Date()\n      });\n      \n      res.json(completedSession);\n    } catch (error: any) {\n      res.status(500).json({ message: \"Error completing assessment: \" + error.message });\n    }\n  });\n\n  // Workout plan endpoints\n  app.post(\"/api/plans\", async (req, res) => {\n    try {\n      const { userId, sessionId, planData, isCoached } = req.body;\n      \n      const plan = await storage.createWorkoutPlan({\n        userId,\n        sessionId,\n        planData,\n        isCoached: isCoached || false\n      });\n      \n      res.json(plan);\n    } catch (error: any) {\n      res.status(500).json({ message: \"Error creating plan: \" + error.message });\n    }\n  });\n\n  app.get(\"/api/users/:userId/plans\", async (req, res) => {\n    try {\n      const plans = await storage.getUserWorkoutPlans(req.params.userId);\n      res.json(plans);\n    } catch (error: any) {\n      res.status(500).json({ message: \"Error fetching plans: \" + error.message });\n    }\n  });\n\n  // Stripe subscription endpoint\n  app.post('/api/create-subscription', async (req, res) => {\n    try {\n      if (!stripe) {\n        return res.status(503).json({ \n          error: { message: 'Stripe is not configured. Please set STRIPE_SECRET_KEY environment variable.' } \n        });\n      }\n\n      const { userId, email, username } = req.body;\n      \n      const customer = await stripe.customers.create({\n        email,\n        name: username,\n      });\n\n      const subscription = await stripe.subscriptions.create({\n        customer: customer.id,\n        items: [{\n          price: process.env.STRIPE_PRICE_ID || 'price_default',\n        }],\n        payment_behavior: 'default_incomplete',\n        expand: ['latest_invoice.payment_intent'],\n      });\n\n      await storage.updateUserStripeInfo(userId, customer.id, subscription.id);\n\n      res.json({\n        subscriptionId: subscription.id,\n        clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,\n      });\n    } catch (error: any) {\n      res.status(400).json({ error: { message: error.message } });\n    }\n  });\n\n  return httpServer;\n}\n", "size_bytes": 8535}, "server/storage.ts": {"content": "import { users, assessmentSessions, workoutPlans, sessionEvents, type User, type InsertUser, type AssessmentSession, type InsertAssessmentSession, type WorkoutPlan, type InsertWorkoutPlan, type SessionEvent } from \"@shared/schema\";\nimport { db } from \"./db\";\nimport { eq, desc } from \"drizzle-orm\";\n\nexport interface IStorage {\n  getUser(id: string): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n  updateUserStripeInfo(userId: string, stripeCustomerId: string, stripeSubscriptionId: string): Promise<User>;\n  \n  createAssessmentSession(session: InsertAssessmentSession): Promise<AssessmentSession>;\n  getAssessmentSession(id: string): Promise<AssessmentSession | undefined>;\n  updateAssessmentSession(id: string, updates: Partial<AssessmentSession>): Promise<AssessmentSession>;\n  \n  createWorkoutPlan(plan: InsertWorkoutPlan): Promise<WorkoutPlan>;\n  getUserWorkoutPlans(userId: string): Promise<WorkoutPlan[]>;\n  \n  createSessionEvent(event: { sessionId: string; eventType: string; eventData?: any }): Promise<SessionEvent>;\n  getSessionEvents(sessionId: string): Promise<SessionEvent[]>;\n}\n\nexport class DatabaseStorage implements IStorage {\n  async getUser(id: string): Promise<User | undefined> {\n    const [user] = await db.select().from(users).where(eq(users.id, id));\n    return user || undefined;\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    const [user] = await db.select().from(users).where(eq(users.username, username));\n    return user || undefined;\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    const [user] = await db\n      .insert(users)\n      .values(insertUser)\n      .returning();\n    return user;\n  }\n\n  async updateUserStripeInfo(userId: string, stripeCustomerId: string, stripeSubscriptionId: string): Promise<User> {\n    const [user] = await db\n      .update(users)\n      .set({ stripeCustomerId, stripeSubscriptionId })\n      .where(eq(users.id, userId))\n      .returning();\n    return user;\n  }\n\n  async createAssessmentSession(session: InsertAssessmentSession): Promise<AssessmentSession> {\n    const [newSession] = await db\n      .insert(assessmentSessions)\n      .values(session)\n      .returning();\n    return newSession;\n  }\n\n  async getAssessmentSession(id: string): Promise<AssessmentSession | undefined> {\n    const [session] = await db.select().from(assessmentSessions).where(eq(assessmentSessions.id, id));\n    return session || undefined;\n  }\n\n  async updateAssessmentSession(id: string, updates: Partial<AssessmentSession>): Promise<AssessmentSession> {\n    const [session] = await db\n      .update(assessmentSessions)\n      .set(updates)\n      .where(eq(assessmentSessions.id, id))\n      .returning();\n    return session;\n  }\n\n  async createWorkoutPlan(plan: InsertWorkoutPlan): Promise<WorkoutPlan> {\n    const [newPlan] = await db\n      .insert(workoutPlans)\n      .values(plan)\n      .returning();\n    return newPlan;\n  }\n\n  async getUserWorkoutPlans(userId: string): Promise<WorkoutPlan[]> {\n    return await db.select().from(workoutPlans).where(eq(workoutPlans.userId, userId)).orderBy(desc(workoutPlans.createdAt));\n  }\n\n  async createSessionEvent(event: { sessionId: string; eventType: string; eventData?: any }): Promise<SessionEvent> {\n    const [newEvent] = await db\n      .insert(sessionEvents)\n      .values(event)\n      .returning();\n    return newEvent;\n  }\n\n  async getSessionEvents(sessionId: string): Promise<SessionEvent[]> {\n    return await db.select().from(sessionEvents).where(eq(sessionEvents.sessionId, sessionId)).orderBy(desc(sessionEvents.timestamp));\n  }\n}\n\nexport const storage = new DatabaseStorage();\n", "size_bytes": 3725}, "server/vite.ts": {"content": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createServer as createViteServer, createLogger } from \"vite\";\nimport { type Server } from \"http\";\nimport viteConfig from \"../vite.config\";\nimport { nanoid } from \"nanoid\";\n\nconst viteLogger = createLogger();\n\nexport function log(message: string, source = \"express\") {\n  const formattedTime = new Date().toLocaleTimeString(\"en-US\", {\n    hour: \"numeric\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: true,\n  });\n\n  console.log(`${formattedTime} [${source}] ${message}`);\n}\n\nexport async function setupVite(app: Express, server: Server) {\n  const serverOptions = {\n    middlewareMode: true,\n    hmr: { server },\n    allowedHosts: true as const,\n  };\n\n  const vite = await createViteServer({\n    ...viteConfig,\n    configFile: false,\n    customLogger: {\n      ...viteLogger,\n      error: (msg, options) => {\n        viteLogger.error(msg, options);\n        process.exit(1);\n      },\n    },\n    server: serverOptions,\n    appType: \"custom\",\n  });\n\n  app.use(vite.middlewares);\n  app.use(\"*\", async (req, res, next) => {\n    const url = req.originalUrl;\n\n    try {\n      const clientTemplate = path.resolve(\n        import.meta.dirname,\n        \"..\",\n        \"client\",\n        \"index.html\",\n      );\n\n      // always reload the index.html file from disk incase it changes\n      let template = await fs.promises.readFile(clientTemplate, \"utf-8\");\n      template = template.replace(\n        `src=\"/src/main.tsx\"`,\n        `src=\"/src/main.tsx?v=${nanoid()}\"`,\n      );\n      const page = await vite.transformIndexHtml(url, template);\n      res.status(200).set({ \"Content-Type\": \"text/html\" }).end(page);\n    } catch (e) {\n      vite.ssrFixStacktrace(e as Error);\n      next(e);\n    }\n  });\n}\n\nexport function serveStatic(app: Express) {\n  const distPath = path.resolve(import.meta.dirname, \"public\");\n\n  if (!fs.existsSync(distPath)) {\n    throw new Error(\n      `Could not find the build directory: ${distPath}, make sure to build the client first`,\n    );\n  }\n\n  app.use(express.static(distPath));\n\n  // fall through to index.html if the file doesn't exist\n  app.use(\"*\", (_req, res) => {\n    res.sendFile(path.resolve(distPath, \"index.html\"));\n  });\n}\n", "size_bytes": 2263}, "shared/schema.ts": {"content": "import { sql } from \"drizzle-orm\";\nimport { pgTable, text, varchar, integer, jsonb, timestamp, boolean } from \"drizzle-orm/pg-core\";\nimport { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\nexport const users = pgTable(\"users\", {\n  id: varchar(\"id\").primaryKey().default(sql`gen_random_uuid()`),\n  username: text(\"username\").notNull().unique(),\n  password: text(\"password\").notNull(),\n  email: text(\"email\"),\n  stripeCustomerId: text(\"stripe_customer_id\"),\n  stripeSubscriptionId: text(\"stripe_subscription_id\"),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const assessmentSessions = pgTable(\"assessment_sessions\", {\n  id: varchar(\"id\").primaryKey().default(sql`gen_random_uuid()`),\n  userId: varchar(\"user_id\").references(() => users.id),\n  status: text(\"status\").notNull().default(\"active\"), // active, completed, cancelled\n  phase: text(\"phase\").notNull().default(\"welcome\"), // welcome, discovery, movement, photo, reveal, complete\n  intake: jsonb(\"intake\"),\n  signals: jsonb(\"signals\"),\n  scores: jsonb(\"scores\"),\n  plan: jsonb(\"plan\"),\n  gasScore: integer(\"gas_score\"),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n  completedAt: timestamp(\"completed_at\"),\n});\n\nexport const workoutPlans = pgTable(\"workout_plans\", {\n  id: varchar(\"id\").primaryKey().default(sql`gen_random_uuid()`),\n  userId: varchar(\"user_id\").references(() => users.id).notNull(),\n  sessionId: varchar(\"session_id\").references(() => assessmentSessions.id),\n  planData: jsonb(\"plan_data\").notNull(),\n  isCoached: boolean(\"is_coached\").default(false),\n  createdAt: timestamp(\"created_at\").defaultNow().notNull(),\n});\n\nexport const sessionEvents = pgTable(\"session_events\", {\n  id: varchar(\"id\").primaryKey().default(sql`gen_random_uuid()`),\n  sessionId: varchar(\"session_id\").references(() => assessmentSessions.id).notNull(),\n  eventType: text(\"event_type\").notNull(), // question_answered, movement_detected, phase_changed, etc.\n  eventData: jsonb(\"event_data\"),\n  timestamp: timestamp(\"timestamp\").defaultNow().notNull(),\n});\n\nexport const insertUserSchema = createInsertSchema(users).pick({\n  username: true,\n  password: true,\n  email: true,\n});\n\nexport const insertAssessmentSessionSchema = createInsertSchema(assessmentSessions).pick({\n  userId: true,\n  status: true,\n  phase: true,\n});\n\nexport const insertWorkoutPlanSchema = createInsertSchema(workoutPlans).pick({\n  userId: true,\n  sessionId: true,\n  planData: true,\n  isCoached: true,\n});\n\nexport type InsertUser = z.infer<typeof insertUserSchema>;\nexport type User = typeof users.$inferSelect;\nexport type InsertAssessmentSession = z.infer<typeof insertAssessmentSessionSchema>;\nexport type AssessmentSession = typeof assessmentSessions.$inferSelect;\nexport type InsertWorkoutPlan = z.infer<typeof insertWorkoutPlanSchema>;\nexport type WorkoutPlan = typeof workoutPlans.$inferSelect;\nexport type SessionEvent = typeof sessionEvents.$inferSelect;\n", "size_bytes": 2944}, "client/src/App.tsx": {"content": "import { Switch, Route } from \"wouter\";\nimport { queryClient } from \"./lib/queryClient\";\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\nimport NotFound from \"@/pages/not-found\";\nimport Assessment from \"@/pages/assessment\";\n\nfunction Router() {\n  return (\n    <Switch>\n      <Route path=\"/\" component={Assessment} />\n      <Route path=\"/assessment/:sessionId?\" component={Assessment} />\n      <Route component={NotFound} />\n    </Switch>\n  );\n}\n\nfunction App() {\n  return (\n    <QueryClientProvider client={queryClient}>\n      <TooltipProvider>\n        <Toaster />\n        <Router />\n      </TooltipProvider>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n", "size_bytes": 787}, "client/src/index.css": {"content": "@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Outfit:wght@400;500;600;700&display=swap');\n\n@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n:root {\n  --background: hsl(220, 15%, 6%);\n  --foreground: hsl(220, 20%, 98%);\n  --card: hsl(220, 15%, 8%);\n  --card-foreground: hsl(220, 15%, 95%);\n  --popover: hsl(220, 15%, 8%);\n  --popover-foreground: hsl(220, 15%, 95%);\n  --primary: hsl(213, 100%, 50%);\n  --primary-foreground: hsl(220, 20%, 98%);\n  --secondary: hsl(177, 70%, 35%);\n  --secondary-foreground: hsl(220, 20%, 98%);\n  --muted: hsl(220, 15%, 15%);\n  --muted-foreground: hsl(220, 15%, 65%);\n  --accent: hsl(167, 85%, 45%);\n  --accent-foreground: hsl(220, 15%, 10%);\n  --destructive: hsl(0, 70%, 50%);\n  --destructive-foreground: hsl(220, 20%, 98%);\n  --border: hsl(220, 15%, 20%);\n  --input: hsl(220, 15%, 15%);\n  --ring: hsl(213, 100%, 50%);\n  --radius: 12px;\n  --font-sans: Inter, system-ui, sans-serif;\n  --font-display: Outfit, Inter, system-ui, sans-serif;\n  --font-mono: Menlo, monospace;\n}\n\n.dark {\n  --background: hsl(220, 15%, 6%);\n  --foreground: hsl(220, 20%, 98%);\n  --card: hsl(220, 15%, 8%);\n  --card-foreground: hsl(220, 15%, 95%);\n  --popover: hsl(220, 15%, 8%);\n  --popover-foreground: hsl(220, 15%, 95%);\n  --primary: hsl(213, 100%, 50%);\n  --primary-foreground: hsl(220, 20%, 98%);\n  --secondary: hsl(177, 70%, 35%);\n  --secondary-foreground: hsl(220, 20%, 98%);\n  --muted: hsl(220, 15%, 15%);\n  --muted-foreground: hsl(220, 15%, 65%);\n  --accent: hsl(167, 85%, 45%);\n  --accent-foreground: hsl(220, 15%, 10%);\n  --destructive: hsl(0, 70%, 50%);\n  --destructive-foreground: hsl(220, 20%, 98%);\n  --border: hsl(220, 15%, 20%);\n  --input: hsl(220, 15%, 15%);\n  --ring: hsl(213, 100%, 50%);\n}\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n\n  body {\n    @apply font-sans antialiased bg-background text-foreground;\n  }\n}\n\n@keyframes pulse-glow {\n  0%, 100% { \n    transform: scale(1);\n  }\n  50% { \n    transform: scale(1.05);\n  }\n}\n\n@keyframes orbit {\n  0% { transform: rotate(0deg) translateX(80px) rotate(0deg); }\n  100% { transform: rotate(360deg) translateX(80px) rotate(-360deg); }\n}\n\n@keyframes float {\n  0%, 100% { transform: translateY(0px); }\n  50% { transform: translateY(-10px); }\n}\n\n@keyframes breathing {\n  0%, 100% { transform: scale(1); opacity: 0.8; }\n  50% { transform: scale(1.1); opacity: 1; }\n}\n\n.orb-container {\n  position: relative;\n  width: 200px;\n  height: 200px;\n  margin: 0 auto;\n}\n\n.ai-orb {\n  width: 200px;\n  height: 200px;\n  border-radius: 50%;\n  position: relative;\n  animation: pulse-glow 2s ease-in-out infinite;\n}\n\n.ai-orb::before {\n  content: '';\n  position: absolute;\n  top: -20px;\n  left: -20px;\n  right: -20px;\n  bottom: -20px;\n  border-radius: 50%;\n  background: radial-gradient(circle, rgba(0, 230, 184, 0.1), transparent 70%);\n  animation: breathing 4s ease-in-out infinite;\n}\n\n.orb-particles {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 4px;\n  height: 4px;\n  background: rgba(0, 230, 184, 0.8);\n  border-radius: 50%;\n  animation: orbit 8s linear infinite;\n}\n\n.particle-1 { animation-delay: 0s; }\n.particle-2 { animation-delay: 1s; }\n.particle-3 { animation-delay: 2s; }\n.particle-4 { animation-delay: 3s; }\n\n.holographic-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  border-radius: 50%;\n  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);\n  animation: float 3s ease-in-out infinite;\n}\n", "size_bytes": 3505}, "client/src/main.tsx": {"content": "import { createRoot } from \"react-dom/client\";\nimport App from \"./App\";\nimport \"./index.css\";\n\ncreateRoot(document.getElementById(\"root\")!).render(<App />);\n", "size_bytes": 157}, "server/services/assessment.ts": {"content": "import { AssessmentSession } from \"@shared/schema\";\nimport { analyzeIntakeData, generateWorkoutPlan, analyzeMovementQuality } from \"./openai\";\n\ninterface AssessmentScores {\n  TC: number; // Time Capacity\n  MQ: number; // Movement Quality\n  RR: number; // Recovery Readiness\n  EF: number; // Environmental Fit\n  AS: number; // Adherence Signals\n  GAS: number; // Goal Attainability Score\n}\n\nclass AssessmentService {\n  async calculateScores(session: AssessmentSession): Promise<AssessmentScores> {\n    const { intake, signals } = session;\n    \n    // Calculate Time Capacity (0-100)\n    const TC = this.calculateTimeCapacity(intake);\n    \n    // Calculate Movement Quality (0-100) - requires movement analysis  \n    const MQ = await this.calculateMovementQuality(signals && (signals as any).movement ? (signals as any).movement : null);\n    \n    // Calculate Recovery Readiness (0-100)\n    const RR = this.calculateRecoveryReadiness(intake);\n    \n    // Calculate Environmental Fit (0-100)\n    const EF = this.calculateEnvironmentalFit(intake);\n    \n    // Calculate Adherence Signals (0-100)\n    const AS = this.calculateAdherenceSignals(intake, signals);\n    \n    // Calculate Goal Attainability Score (weighted blend)\n    const GAS = Math.round(0.28 * TC + 0.22 * MQ + 0.20 * RR + 0.15 * EF + 0.15 * AS);\n    \n    return { TC, MQ, RR, EF, AS, GAS };\n  }\n\n  private calculateTimeCapacity(intake: any): number {\n    if (!intake?.time_days || !intake?.time_minutes) return 50;\n    \n    const weeklyMinutes = intake.time_days * intake.time_minutes;\n    \n    // Scale based on minimum effective volume\n    if (weeklyMinutes >= 150) return 100;\n    if (weeklyMinutes >= 120) return 85;\n    if (weeklyMinutes >= 90) return 70;\n    if (weeklyMinutes >= 60) return 55;\n    return 40;\n  }\n\n  private async calculateMovementQuality(movementData: any): Promise<number> {\n    if (!movementData) return 60; // Default if no movement data\n    \n    try {\n      const analysis = await analyzeMovementQuality(movementData);\n      \n      // Convert analysis scores to 0-100 scale\n      const scores = Object.values(analysis.scores) as number[];\n      const avgScore = scores.reduce((a: number, b: number) => a + b, 0) / scores.length;\n      return Math.max(0, Math.min(100, avgScore));\n    } catch (error) {\n      console.error('Movement quality analysis failed:', error);\n      return 60;\n    }\n  }\n\n  private calculateRecoveryReadiness(intake: any): number {\n    let score = 50;\n    \n    if (intake?.sleep_hours) {\n      if (intake.sleep_hours >= 8) score += 25;\n      else if (intake.sleep_hours >= 7) score += 15;\n      else if (intake.sleep_hours >= 6) score += 5;\n      else score -= 15;\n    }\n    \n    if (intake?.stress_1_5) {\n      const stressImpact = (5 - intake.stress_1_5) * 5; // Lower stress = higher score\n      score += stressImpact;\n    }\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  private calculateEnvironmentalFit(intake: any): number {\n    let score = 50;\n    \n    // Equipment access\n    if (intake?.equipment) {\n      if (intake.equipment.includes('full gym')) score += 30;\n      else if (intake.equipment.includes('DB')) score += 20;\n      else if (intake.equipment.includes('bands')) score += 10;\n    }\n    \n    // Schedule stability (inferred from consistency responses)\n    if (intake?.schedule_consistent) score += 20;\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  private calculateAdherenceSignals(intake: any, signals: any): number {\n    let score = 50;\n    \n    // Past adherence history\n    if (intake?.history && !intake.history.includes('start/stop')) score += 20;\n    \n    // Accountability preference\n    if (intake?.accountability_yes) score += 15;\n    \n    // Engagement signals from voice/face analysis\n    if (signals?.voice?.engagement > 0.7) score += 10;\n    if (signals?.face?.engagement > 0.7) score += 5;\n    \n    return Math.max(0, Math.min(100, score));\n  }\n\n  async generatePlan(session: AssessmentSession, scores: AssessmentScores): Promise<any> {\n    try {\n      const plan = await generateWorkoutPlan(session, scores);\n      \n      // Add success probability and improvement suggestions\n      plan.gasScore = scores.GAS;\n      plan.improvementSuggestions = this.generateImprovementSuggestions(scores);\n      \n      return plan;\n    } catch (error) {\n      console.error('Plan generation failed:', error);\n      return this.generateFallbackPlan(session, scores);\n    }\n  }\n\n  private generateImprovementSuggestions(scores: AssessmentScores): string[] {\n    const suggestions = [];\n    \n    if (scores.TC < 70) {\n      suggestions.push(\"Adding 1 more training day could increase success by 8-12%\");\n    }\n    \n    if (scores.MQ < 70) {\n      suggestions.push(\"Weekly form check-ins could improve movement quality by 15-20%\");\n    }\n    \n    if (scores.RR < 60) {\n      suggestions.push(\"Improving sleep to 7+ hours could boost recovery by 20%\");\n    }\n    \n    return suggestions;\n  }\n\n  private generateFallbackPlan(session: AssessmentSession, scores: AssessmentScores): any {\n    return {\n      gasScore: scores.GAS,\n      weeklySchedule: {\n        days: 3,\n        sessions: [\"Strength A\", \"Conditioning\", \"Strength B\"]\n      },\n      sampleWorkout: {\n        name: \"Strength A\",\n        duration: \"30 minutes\",\n        exercises: [\n          { name: \"Goblet Squats\", sets: \"3x8\", rpe: \"7\" },\n          { name: \"Push-ups\", sets: \"3xAMRAP\", rpe: \"8\" },\n          { name: \"Bent Over Rows\", sets: \"3x10\", rpe: \"7\" }\n        ]\n      },\n      nutritionGuidelines: {\n        pattern: \"3 meals + 1 snack\",\n        proteinTarget: \"0.8-1.0g per lb goal weight\",\n        calorieDeficit: \"300-500 calories\"\n      },\n      progressForecast: {\n        week4: \"-3 to -5 lbs\",\n        week8: \"-6 to -9 lbs\",\n        week12: \"-8 to -12 lbs\"\n      }\n    };\n  }\n}\n\nexport const assessmentService = new AssessmentService();\n", "size_bytes": 5867}, "server/services/openai.ts": {"content": "import OpenAI from \"openai\";\n\n// the newest OpenAI model is \"gpt-5\" which was released August 7, 2025. do not change this unless explicitly requested by the user\nconst openai = new OpenAI({ \n  apiKey: process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY_ENV_VAR || \"default_key\"\n});\n\nexport async function analyzeIntakeData(intake: any): Promise<{\n  constraints: any;\n  levers: any;\n  emotionalSignals: any;\n}> {\n  try {\n    const response = await openai.chat.completions.create({\n      model: \"gpt-5\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a fitness assessment expert. Analyze the user's intake data and extract constraints, levers, and emotional signals. Respond with JSON in this format: { 'constraints': {}, 'levers': {}, 'emotionalSignals': {} }\"\n        },\n        {\n          role: \"user\",\n          content: `Analyze this fitness assessment intake: ${JSON.stringify(intake)}`\n        }\n      ],\n      response_format: { type: \"json_object\" },\n    });\n\n    return JSON.parse(response.choices[0].message.content || \"{}\");\n  } catch (error: any) {\n    throw new Error(\"Failed to analyze intake data: \" + error.message);\n  }\n}\n\nexport async function generateWorkoutPlan(sessionData: any, scores: any): Promise<any> {\n  try {\n    const response = await openai.chat.completions.create({\n      model: \"gpt-5\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are an expert fitness coach. Generate a detailed, personalized workout plan based on the assessment data and scores. Include weekly schedule, exercise selections, progressions, and nutrition guidelines. Respond with structured JSON.\"\n        },\n        {\n          role: \"user\",\n          content: `Generate a workout plan for this assessment data: ${JSON.stringify({ sessionData, scores })}`\n        }\n      ],\n      response_format: { type: \"json_object\" },\n    });\n\n    return JSON.parse(response.choices[0].message.content || \"{}\");\n  } catch (error: any) {\n    throw new Error(\"Failed to generate workout plan: \" + error.message);\n  }\n}\n\nexport async function analyzeMovementQuality(movementData: any): Promise<{\n  scores: any;\n  recommendations: string[];\n}> {\n  try {\n    const response = await openai.chat.completions.create({\n      model: \"gpt-5\",\n      messages: [\n        {\n          role: \"system\",\n          content: \"You are a movement analysis expert. Analyze pose detection data and provide movement quality scores and recommendations. Respond with JSON: { 'scores': {}, 'recommendations': [] }\"\n        },\n        {\n          role: \"user\",\n          content: `Analyze this movement data: ${JSON.stringify(movementData)}`\n        }\n      ],\n      response_format: { type: \"json_object\" },\n    });\n\n    return JSON.parse(response.choices[0].message.content || \"{}\");\n  } catch (error: any) {\n    throw new Error(\"Failed to analyze movement quality: \" + error.message);\n  }\n}\n", "size_bytes": 2927}, "client/src/components/assessment-results.tsx": {"content": "import { useState } from \"react\";\nimport { useMutation } from \"@tanstack/react-query\";\nimport { apiRequest } from \"@/lib/queryClient\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface AssessmentResultsProps {\n  isOpen: boolean;\n  onClose: () => void;\n  session: any;\n}\n\nexport default function AssessmentResults({ isOpen, onClose, session }: AssessmentResultsProps) {\n  const [showStripeForm, setShowStripeForm] = useState(false);\n\n  const createPlanMutation = useMutation({\n    mutationFn: async (isCoached: boolean) => {\n      const response = await apiRequest(\"POST\", \"/api/plans\", {\n        userId: \"demo-user\",\n        sessionId: session?.id,\n        planData: session?.plan,\n        isCoached,\n      });\n      return response.json();\n    },\n    onSuccess: () => {\n      onClose();\n    },\n  });\n\n  const startSubscriptionMutation = useMutation({\n    mutationFn: async () => {\n      const response = await apiRequest(\"POST\", \"/api/create-subscription\", {\n        userId: \"demo-user\",\n        email: \"<EMAIL>\",\n        username: \"Demo User\",\n      });\n      return response.json();\n    },\n    onSuccess: (data) => {\n      // In a real app, redirect to Stripe Checkout\n      console.log(\"Subscription created:\", data);\n      setShowStripeForm(true);\n    },\n  });\n\n  if (!isOpen || !session) return null;\n\n  const scores = session.scores || {};\n  const plan = session.plan || {};\n\n  return (\n    <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n      <Card className=\"max-w-4xl w-full max-h-[90vh] overflow-y-auto\" data-testid=\"modal-assessment-results\">\n        <CardContent className=\"p-8\">\n          {/* Header */}\n          <div className=\"text-center mb-8\">\n            <h2 className=\"font-display text-3xl font-bold mb-2\">Your Personalized Plan</h2>\n            <p className=\"text-muted-foreground\">Based on your assessment, movement analysis, and goals</p>\n          </div>\n          \n          {/* Goal Attainability Score */}\n          <div className=\"bg-gradient-to-br from-primary/10 to-accent/10 rounded-2xl p-6 mb-8 border border-primary/20\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div>\n                <h3 className=\"font-display text-xl font-semibold mb-1\">Goal Attainability</h3>\n                <p className=\"text-sm text-muted-foreground\">Your likelihood of success with this plan</p>\n              </div>\n              <div className=\"text-right\">\n                <div className=\"text-4xl font-bold text-primary\" data-testid=\"text-gas-score\">\n                  {session.gasScore || scores.GAS || 67}%\n                </div>\n                <div className=\"text-sm text-muted-foreground\">Current odds</div>\n              </div>\n            </div>\n            \n            {/* Score Breakdown */}\n            <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4 mb-4\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-secondary\" data-testid=\"text-time-capacity\">\n                  {scores.TC || 72}\n                </div>\n                <div className=\"text-xs text-muted-foreground\">Time Capacity</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-accent\" data-testid=\"text-movement-quality\">\n                  {scores.MQ || 64}\n                </div>\n                <div className=\"text-xs text-muted-foreground\">Movement Quality</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-primary\" data-testid=\"text-recovery-readiness\">\n                  {scores.RR || 58}\n                </div>\n                <div className=\"text-xs text-muted-foreground\">Recovery</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-accent\" data-testid=\"text-environment-fit\">\n                  {scores.EF || 80}\n                </div>\n                <div className=\"text-xs text-muted-foreground\">Environment</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-secondary\" data-testid=\"text-adherence-signals\">\n                  {scores.AS || 70}\n                </div>\n                <div className=\"text-xs text-muted-foreground\">Adherence</div>\n              </div>\n            </div>\n            \n            {/* Improvement Suggestion */}\n            <div className=\"bg-accent/10 rounded-xl p-4 border border-accent/20\">\n              <div className=\"flex items-start space-x-3\">\n                <div className=\"w-8 h-8 rounded-full bg-accent flex items-center justify-center flex-shrink-0\">\n                  <svg className=\"w-4 h-4 text-accent-foreground\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M13,9H18.5L13,3.5V9M6,2H14L20,8V20A2,2 0 0,1 18,20H6C4.89,20 4,19.1 4,18V4C4,2.89 4.89,2 6,2M15,18V16H6V18H15M18,14V12H6V14H18Z\"/>\n                  </svg>\n                </div>\n                <div className=\"flex-1\">\n                  <div className=\"text-sm font-medium text-accent mb-1\">Boost to 78% success rate</div>\n                  <div className=\"text-sm text-foreground\">Weekly form check-ins and auto-adjustments can raise your probability by 11 points</div>\n                </div>\n              </div>\n            </div>\n          </div>\n          \n          {/* Plan Details */}\n          <div className=\"grid md:grid-cols-2 gap-8 mb-8\">\n            \n            {/* Training Plan */}\n            <div>\n              <h3 className=\"font-display text-xl font-semibold mb-4\">Training Schedule</h3>\n              <div className=\"space-y-4\">\n                \n                <div className=\"bg-muted/30 rounded-xl p-4\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <span className=\"font-medium\">4 Days per Week</span>\n                    <span className=\"text-sm text-muted-foreground\">28-32 min sessions</span>\n                  </div>\n                  <div className=\"text-sm text-muted-foreground\">2 Strength • 1 Conditioning • 1 Mobility</div>\n                </div>\n                \n                <Card className=\"p-4\">\n                  <div className=\"font-medium mb-3\">Sample Strength Session</div>\n                  <div className=\"space-y-2 text-sm\">\n                    {plan.sampleWorkout?.exercises?.map((exercise: any, index: number) => (\n                      <div key={index} className=\"flex justify-between\">\n                        <span>{exercise.name}</span>\n                        <span className=\"text-muted-foreground\">{exercise.sets}</span>\n                      </div>\n                    )) || (\n                      <>\n                        <div className=\"flex justify-between\">\n                          <span>Goblet Squats</span>\n                          <span className=\"text-muted-foreground\">4×8 @ RPE 7</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span>DB Romanian Deadlifts</span>\n                          <span className=\"text-muted-foreground\">3×10 @ RPE 7</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span>Push-ups</span>\n                          <span className=\"text-muted-foreground\">3×AMRAP</span>\n                        </div>\n                      </>\n                    )}\n                  </div>\n                </Card>\n              </div>\n            </div>\n            \n            {/* Nutrition & Progress */}\n            <div>\n              <h3 className=\"font-display text-xl font-semibold mb-4\">Nutrition & Targets</h3>\n              <div className=\"space-y-4\">\n                \n                <div className=\"bg-muted/30 rounded-xl p-4\">\n                  <div className=\"font-medium mb-2\">Daily Pattern</div>\n                  <div className=\"text-sm text-muted-foreground\">3 meals + 1 snack • ~500 cal deficit</div>\n                  <div className=\"text-sm text-muted-foreground mt-1\">Protein: 0.8-1.0g per lb goal weight</div>\n                </div>\n                \n                <Card className=\"p-4\">\n                  <div className=\"font-medium mb-3\">Expected Progress</div>\n                  <div className=\"space-y-3\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm\">4 weeks</span>\n                      <Badge variant=\"secondary\">-3 to -6 lbs</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm\">8 weeks</span>\n                      <Badge variant=\"secondary\">-6 to -10 lbs</Badge>\n                    </div>\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"text-sm\">12 weeks</span>\n                      <Badge variant=\"secondary\">-8 to -12 lbs</Badge>\n                    </div>\n                  </div>\n                </Card>\n              </div>\n            </div>\n          </div>\n          \n          {/* Action Buttons */}\n          <div className=\"flex flex-col md:flex-row gap-4\">\n            <Button \n              variant=\"outline\"\n              className=\"flex-1\"\n              onClick={() => createPlanMutation.mutate(false)}\n              disabled={createPlanMutation.isPending}\n              data-testid=\"button-save-basic-plan\"\n            >\n              <div className=\"text-center\">\n                <div>Save Basic Plan</div>\n                <div className=\"text-sm text-muted-foreground\">Get your plan and track progress</div>\n              </div>\n            </Button>\n            \n            <Button \n              className=\"flex-1 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90\"\n              onClick={() => startSubscriptionMutation.mutate()}\n              disabled={startSubscriptionMutation.isPending}\n              data-testid=\"button-start-coached-trial\"\n            >\n              <div className=\"text-center\">\n                <div>Start Coached Trial</div>\n                <div className=\"text-sm opacity-80\">7 days free • Weekly tune-ups</div>\n              </div>\n            </Button>\n          </div>\n\n          {showStripeForm && (\n            <div className=\"mt-6 p-4 bg-muted/50 rounded-xl border border-border\">\n              <div className=\"text-center\">\n                <p className=\"text-sm text-muted-foreground mb-2\">\n                  Subscription created successfully!\n                </p>\n                <p className=\"text-xs text-muted-foreground\">\n                  In a production app, this would redirect to Stripe Checkout.\n                </p>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n", "size_bytes": 11113}, "client/src/components/orb-avatar.tsx": {"content": "import { useEffect, useRef } from \"react\";\nimport { createOrb<PERSON>enderer } from \"@/lib/three-orb\";\n\ninterface OrbAvatarProps {\n  state: \"welcome\" | \"discovery\" | \"coaching\" | \"reveal\" | \"upsell\" | \"idle\";\n  audioData?: {\n    rms: number;\n    pitch: number;\n    viseme?: string;\n  };\n  engagement?: number;\n  intensity?: number;\n  className?: string;\n}\n\nexport default function OrbAvatar({ \n  state, \n  audioData, \n  engagement = 0.8, \n  intensity = 0.5,\n  className = \"\" \n}: OrbAvatarProps) {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const orbRef = useRef<any>(null);\n\n  useEffect(() => {\n    if (!containerRef.current || orbRef.current) return;\n\n    const orb = createOrbRenderer(containerRef.current);\n    orbRef.current = orb;\n\n    return () => {\n      if (orbRef.current) {\n        orbRef.current.dispose();\n        orbRef.current = null;\n      }\n    };\n  }, []);\n\n  useEffect(() => {\n    if (orbRef.current) {\n      orbRef.current.setState(state);\n    }\n  }, [state]);\n\n  useEffect(() => {\n    if (orbRef.current && audioData) {\n      orbRef.current.onTTSFrame(audioData);\n    }\n  }, [audioData]);\n\n  useEffect(() => {\n    if (orbRef.current) {\n      orbRef.current.setEngagement(engagement);\n      orbRef.current.setIntensity(intensity);\n    }\n  }, [engagement, intensity]);\n\n  return (\n    <div className={`orb-container ${className}`}>\n      <div \n        ref={containerRef} \n        className=\"w-full h-full\" \n        data-testid=\"orb-avatar-container\"\n      />\n      \n      {/* Fallback CSS orb for when WebGL is unavailable */}\n      <div className=\"ai-orb\" style={{ display: orbRef.current ? 'none' : 'block' }}>\n        <div className=\"holographic-overlay\"></div>\n        <div className=\"orb-particles particle-1\"></div>\n        <div className=\"orb-particles particle-2\"></div>\n        <div className=\"orb-particles particle-3\"></div>\n        <div className=\"orb-particles particle-4\"></div>\n      </div>\n    </div>\n  );\n}\n", "size_bytes": 1947}, "client/src/components/pose-overlay.tsx": {"content": "import { useEffect, useRef } from \"react\";\n\ninterface PoseOverlayProps {\n  poses: any[];\n  videoRef: React.RefObject<HTMLVideoElement>;\n  className?: string;\n}\n\nexport default function PoseOverlay({ poses, videoRef, className = \"\" }: PoseOverlayProps) {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n\n  useEffect(() => {\n    if (!canvasRef.current || !videoRef.current || poses.length === 0) return;\n\n    const canvas = canvasRef.current;\n    const video = videoRef.current;\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas size to match video\n    canvas.width = video.videoWidth || video.clientWidth;\n    canvas.height = video.videoHeight || video.clientHeight;\n\n    // Clear canvas\n    ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n    // Draw pose landmarks\n    poses.forEach(pose => {\n      if (pose.landmarks) {\n        drawPose(ctx, pose.landmarks, canvas.width, canvas.height);\n      }\n    });\n  }, [poses, videoRef]);\n\n  const drawPose = (ctx: CanvasRenderingContext2D, landmarks: any[], width: number, height: number) => {\n    // Set drawing style\n    ctx.strokeStyle = '#00E6B8'; // accent color\n    ctx.lineWidth = 3;\n    ctx.fillStyle = '#00E6B8';\n\n    // Draw pose connections\n    const connections = [\n      [11, 12], [11, 13], [13, 15], [12, 14], [14, 16], // Arms\n      [11, 23], [12, 24], [23, 24], // Torso\n      [23, 25], [25, 27], [24, 26], [26, 28], // Legs\n    ];\n\n    connections.forEach(([start, end]) => {\n      const startPoint = landmarks[start];\n      const endPoint = landmarks[end];\n      \n      if (startPoint && endPoint && startPoint.visibility > 0.5 && endPoint.visibility > 0.5) {\n        ctx.beginPath();\n        ctx.moveTo(startPoint.x * width, startPoint.y * height);\n        ctx.lineTo(endPoint.x * width, endPoint.y * height);\n        ctx.stroke();\n      }\n    });\n\n    // Draw key joints\n    const keyJoints = [11, 12, 13, 14, 15, 16, 23, 24, 25, 26, 27, 28];\n    keyJoints.forEach(jointIndex => {\n      const joint = landmarks[jointIndex];\n      if (joint && joint.visibility > 0.5) {\n        ctx.beginPath();\n        ctx.arc(joint.x * width, joint.y * height, 5, 0, 2 * Math.PI);\n        ctx.fill();\n      }\n    });\n  };\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className={`pointer-events-none ${className}`}\n      style={{ \n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n      }}\n      data-testid=\"canvas-pose-overlay\"\n    />\n  );\n}\n", "size_bytes": 2497}, "client/src/components/transcript-panel.tsx": {"content": "import { Button } from \"@/components/ui/button\";\nimport { Card } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\n\ninterface TranscriptPanelProps {\n  isOpen: boolean;\n  onClose: () => void;\n  transcript: string;\n  session: any;\n}\n\nexport default function TranscriptPanel({ isOpen, onClose, transcript, session }: TranscriptPanelProps) {\n  const getKeyFacts = () => {\n    if (!session?.intake) return [];\n    \n    const facts = [];\n    if (session.intake.time_days) facts.push(`${session.intake.time_days} days/week`);\n    if (session.intake.time_minutes) facts.push(`${session.intake.time_minutes}min sessions`);\n    if (session.intake.equipment) facts.push(session.intake.equipment.join(', '));\n    if (session.intake.goal_text) facts.push(session.intake.goal_text);\n    \n    return facts;\n  };\n\n  const transcriptLines = transcript.split('\\n').filter(line => line.trim());\n\n  return (\n    <div \n      className={`fixed right-0 top-20 bottom-6 w-80 bg-card border-l border-border transform transition-transform duration-300 z-40 ${\n        isOpen ? 'translate-x-0' : 'translate-x-full'\n      }`}\n      data-testid=\"panel-transcript\"\n    >\n      <div className=\"p-6 h-full flex flex-col\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h3 className=\"font-display font-semibold text-lg\">Live Session</h3>\n          <Button \n            variant=\"ghost\" \n            size=\"sm\"\n            onClick={onClose}\n            data-testid=\"button-close-transcript\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n            </svg>\n          </Button>\n        </div>\n        \n        {/* Key Facts Captured */}\n        <div className=\"mb-6\">\n          <h4 className=\"text-sm font-medium text-muted-foreground mb-3\">Key Facts</h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {getKeyFacts().map((fact, index) => (\n              <Badge \n                key={index}\n                variant=\"secondary\"\n                className=\"text-xs\"\n                data-testid={`badge-fact-${index}`}\n              >\n                {fact}\n              </Badge>\n            ))}\n          </div>\n        </div>\n        \n        {/* Live Transcript */}\n        <div className=\"flex-1 overflow-hidden\">\n          <h4 className=\"text-sm font-medium text-muted-foreground mb-3\">Transcript</h4>\n          <div className=\"space-y-3 overflow-y-auto h-full\" data-testid=\"transcript-content\">\n            {transcriptLines.length > 0 ? (\n              transcriptLines.map((line, index) => (\n                <div key={index} className=\"flex space-x-2\">\n                  <div className=\"w-6 h-6 rounded-full bg-accent flex-shrink-0 flex items-center justify-center\">\n                    <div className=\"w-2 h-2 bg-accent-foreground rounded-full\"></div>\n                  </div>\n                  <div className=\"flex-1\">\n                    <div className=\"text-xs text-muted-foreground\">You</div>\n                    <div className=\"text-sm\">{line}</div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"text-sm text-muted-foreground text-center\">\n                Start speaking to see transcript...\n              </div>\n            )}\n          </div>\n        </div>\n        \n        {/* Micro Offers */}\n        <div className=\"mt-4 pt-4 border-t border-border\">\n          <h4 className=\"text-sm font-medium text-muted-foreground mb-3\">Available Add-ons</h4>\n          <div className=\"space-y-2\">\n            <Button \n              variant=\"outline\" \n              className=\"w-full justify-start text-left h-auto p-3\"\n              data-testid=\"button-weekly-checkins\"\n            >\n              <div>\n                <div className=\"text-sm font-medium text-accent\">Weekly Check-ins</div>\n                <div className=\"text-xs text-muted-foreground\">Stay on track with progress reviews</div>\n              </div>\n            </Button>\n            <Button \n              variant=\"outline\" \n              className=\"w-full justify-start text-left h-auto p-3\"\n              data-testid=\"button-form-tuneups\"\n            >\n              <div>\n                <div className=\"text-sm font-medium text-primary\">Form Tune-ups</div>\n                <div className=\"text-xs text-muted-foreground\">Perfect your technique weekly</div>\n              </div>\n            </Button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n", "size_bytes": 4608}, "client/src/components/video-call.tsx": {"content": "import { useRef, useEffect, useState } from \"react\";\nimport OrbAvatar from \"./orb-avatar\";\nimport PoseOverlay from \"./pose-overlay\";\nimport { usePoseDetection } from \"@/hooks/use-pose-detection\";\nimport { Card } from \"@/components/ui/card\";\n\ninterface VideoCallProps {\n  session: any;\n  localStream: MediaStream | null;\n  remoteStream: MediaStream | null;\n  transcript: string;\n}\n\nexport default function VideoCall({ session, localStream, remoteStream, transcript }: VideoCallProps) {\n  const localVideoRef = useRef<HTMLVideoElement>(null);\n  const remoteVideoRef = useRef<HTMLVideoElement>(null);\n  const [currentPrompt, setCurrentPrompt] = useState(\"What's the one change you want to see in the next 8-12 weeks?\");\n  const [repCount, setRepCount] = useState(0);\n  const [currentExercise, setCurrentExercise] = useState(\"Air Squat x5\");\n  \n  const { poses, isDetecting, startDetection, stopDetection } = usePoseDetection(localVideoRef);\n\n  useEffect(() => {\n    if (localVideoRef.current && localStream) {\n      localVideoRef.current.srcObject = localStream;\n    }\n  }, [localStream]);\n\n  useEffect(() => {\n    if (remoteVideoRef.current && remoteStream) {\n      remoteVideoRef.current.srcObject = remoteStream;\n    }\n  }, [remoteStream]);\n\n  useEffect(() => {\n    if (session?.phase === \"movement\" && !isDetecting) {\n      startDetection();\n    } else if (session?.phase !== \"movement\" && isDetecting) {\n      stopDetection();\n    }\n  }, [session?.phase, isDetecting, startDetection, stopDetection]);\n\n  // Update prompts based on session phase\n  useEffect(() => {\n    if (session?.phase === \"discovery\") {\n      setCurrentPrompt(\"How many days per week can you realistically train?\");\n    } else if (session?.phase === \"movement\") {\n      setCurrentPrompt(\"Let's check your squat form. Do 5 air squats.\");\n      setCurrentExercise(\"Air Squat x5\");\n    } else if (session?.phase === \"photo\") {\n      setCurrentPrompt(\"Please take front and side photos for posture analysis.\");\n    }\n  }, [session?.phase]);\n\n  const getOrbState = () => {\n    switch (session?.phase) {\n      case \"welcome\": return \"welcome\";\n      case \"discovery\": return \"discovery\";\n      case \"movement\": return \"coaching\";\n      case \"photo\": return \"coaching\";\n      case \"reveal\": return \"reveal\";\n      default: return \"idle\";\n    }\n  };\n\n  return (\n    <div className=\"grid lg:grid-cols-2 gap-6 mb-6\">\n      {/* AI Trainer Pane */}\n      <Card className=\"bg-card rounded-2xl border border-border overflow-hidden aspect-video relative\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10\"></div>\n        \n        {/* Holographic Orb Avatar */}\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <OrbAvatar\n            state={getOrbState()}\n            engagement={0.8}\n            intensity={0.6}\n            className=\"scale-75 lg:scale-100\"\n            data-testid=\"ai-orb\"\n          />\n        </div>\n        \n        {/* AI State Indicator */}\n        <div className=\"absolute top-4 left-4 bg-card/80 backdrop-blur-sm rounded-full px-3 py-1 border border-border\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 bg-accent rounded-full animate-pulse\"></div>\n            <span className=\"text-xs font-medium text-accent capitalize\" data-testid=\"text-ai-state\">\n              {session?.phase || \"Loading\"} Mode\n            </span>\n          </div>\n        </div>\n        \n        {/* Current Cue Card */}\n        <div className=\"absolute bottom-6 left-6 right-6\">\n          <div className=\"bg-card/90 backdrop-blur-sm rounded-xl p-4 border border-border shadow-lg\">\n            <div className=\"text-sm text-muted-foreground mb-1\">AI Trainer is saying:</div>\n            <div className=\"text-foreground font-medium\" data-testid=\"text-current-prompt\">\n              {currentPrompt}\n            </div>\n          </div>\n        </div>\n      </Card>\n      \n      {/* User Camera Pane */}\n      <Card className=\"bg-card rounded-2xl border border-border overflow-hidden aspect-video relative\">\n        {/* User Video */}\n        <video\n          ref={localVideoRef}\n          autoPlay\n          muted\n          playsInline\n          className=\"absolute inset-0 w-full h-full object-cover\"\n          data-testid=\"video-local-stream\"\n        />\n        \n        {/* Pose Detection Overlay */}\n        {session?.phase === \"movement\" && (\n          <PoseOverlay \n            poses={poses}\n            videoRef={localVideoRef}\n            className=\"absolute inset-0\"\n          />\n        )}\n        \n        {/* Movement Analysis Feedback */}\n        <div className=\"absolute top-4 right-4\">\n          <div className=\"bg-card/80 backdrop-blur-sm rounded-xl p-3 border border-border\">\n            <div className=\"text-xs text-muted-foreground mb-1\">Form Analysis</div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex space-x-1\">\n                <div className=\"w-2 h-2 bg-accent rounded-full\"></div>\n                <div className=\"w-2 h-2 bg-accent rounded-full\"></div>\n                <div className=\"w-2 h-2 bg-yellow-500 rounded-full\"></div>\n                <div className=\"w-2 h-2 bg-muted rounded-full\"></div>\n              </div>\n              <span className=\"text-xs font-medium text-accent\" data-testid=\"text-form-feedback\">\n                {poses.length > 0 ? \"Good depth\" : \"Ready\"}\n              </span>\n            </div>\n          </div>\n        </div>\n        \n        {/* Rep Counter */}\n        {session?.phase === \"movement\" && (\n          <div className=\"absolute bottom-4 right-4\">\n            <div className=\"bg-primary/90 backdrop-blur-sm rounded-full w-16 h-16 flex items-center justify-center border-2 border-primary-foreground/20\">\n              <div className=\"text-center\">\n                <div className=\"text-xl font-bold text-primary-foreground\" data-testid=\"text-rep-count\">\n                  {repCount}\n                </div>\n                <div className=\"text-xs text-primary-foreground/80\">reps</div>\n              </div>\n            </div>\n          </div>\n        )}\n        \n        {/* Exercise Instruction */}\n        {session?.phase === \"movement\" && (\n          <div className=\"absolute bottom-4 left-4\">\n            <div className=\"bg-card/90 backdrop-blur-sm rounded-xl p-3 border border-border\">\n              <div className=\"text-xs text-muted-foreground mb-1\">Current Exercise</div>\n              <div className=\"text-sm font-medium text-foreground\" data-testid=\"text-current-exercise\">\n                {currentExercise}\n              </div>\n              <div className=\"text-xs text-accent\">\n                {5 - repCount} more to go\n              </div>\n            </div>\n          </div>\n        )}\n      </Card>\n    </div>\n  );\n}\n", "size_bytes": 6827}, "client/src/hooks/use-mobile.tsx": {"content": "import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n", "size_bytes": 565}, "client/src/hooks/use-pose-detection.ts": {"content": "import { useState, useEffect, useRef, useCallback } from \"react\";\n\n// This would normally import from MediaPipe, but we'll create a simplified version\n// In a real implementation, you'd import: import { Pose } from '@mediapipe/pose';\n\nexport function usePoseDetection(videoRef: React.RefObject<HTMLVideoElement>) {\n  const [poses, setPoses] = useState<any[]>([]);\n  const [isDetecting, setIsDetecting] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  \n  const detectionIntervalRef = useRef<NodeJS.Timeout | null>(null);\n  const wsRef = useRef<WebSocket | null>(null);\n\n  const connectWebSocket = useCallback(() => {\n    const protocol = window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\";\n    const wsUrl = `${protocol}//${window.location.host}/ws`;\n    \n    const ws = new WebSocket(wsUrl);\n    wsRef.current = ws;\n    \n    ws.onopen = () => {\n      console.log('Pose detection WebSocket connected');\n    };\n    \n    ws.onerror = (error) => {\n      console.error('Pose detection WebSocket error:', error);\n    };\n  }, []);\n\n  const detectPoses = useCallback(async () => {\n    if (!videoRef.current || !isDetecting) return;\n\n    try {\n      // Simulate pose detection - in real implementation, use MediaPipe\n      const mockPose = {\n        landmarks: [\n          // Mock landmarks for basic pose structure\n          ...Array(33).fill(null).map((_, i) => ({\n            x: Math.random(),\n            y: Math.random(),\n            z: Math.random(),\n            visibility: Math.random() > 0.3 ? 0.8 : 0.2\n          }))\n        ],\n        score: Math.random() * 0.5 + 0.5, // Random confidence between 0.5-1.0\n      };\n\n      setPoses([mockPose]);\n\n      // Send pose data to server via WebSocket\n      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n        wsRef.current.send(JSON.stringify({\n          type: 'pose_data',\n          poses: [mockPose],\n          timestamp: Date.now()\n        }));\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Pose detection failed');\n    }\n  }, [videoRef, isDetecting]);\n\n  const startDetection = useCallback(() => {\n    if (isDetecting) return;\n    \n    setIsDetecting(true);\n    setError(null);\n    connectWebSocket();\n    \n    // Start pose detection loop\n    detectionIntervalRef.current = setInterval(detectPoses, 100); // 10 FPS\n  }, [isDetecting, detectPoses, connectWebSocket]);\n\n  const stopDetection = useCallback(() => {\n    if (!isDetecting) return;\n    \n    setIsDetecting(false);\n    setPoses([]);\n    \n    if (detectionIntervalRef.current) {\n      clearInterval(detectionIntervalRef.current);\n      detectionIntervalRef.current = null;\n    }\n    \n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n  }, [isDetecting]);\n\n  useEffect(() => {\n    return () => {\n      stopDetection();\n    };\n  }, [stopDetection]);\n\n  return {\n    poses,\n    isDetecting,\n    error,\n    startDetection,\n    stopDetection,\n  };\n}\n", "size_bytes": 2984}, "client/src/hooks/use-speech.ts": {"content": "import { useState, useCallback, useRef } from \"react\";\n\n// Declare global speech recognition types\ndeclare global {\n  interface Window {\n    SpeechRecognition: any;\n    webkitSpeechRecognition: any;\n  }\n}\n\nexport function useSpeech() {\n  const [isListening, setIsListening] = useState(false);\n  const [transcript, setTranscript] = useState(\"\");\n  const [error, setError] = useState<string | null>(null);\n  \n  const recognitionRef = useRef<any>(null);\n  const wsRef = useRef<WebSocket | null>(null);\n\n  const connectWebSocket = useCallback(() => {\n    const protocol = window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\";\n    const wsUrl = `${protocol}//${window.location.host}/ws`;\n    \n    const ws = new WebSocket(wsUrl);\n    wsRef.current = ws;\n    \n    ws.onopen = () => {\n      console.log('Speech WebSocket connected');\n    };\n    \n    ws.onerror = (error) => {\n      console.error('Speech WebSocket error:', error);\n    };\n  }, []);\n\n  const startListening = useCallback(() => {\n    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {\n      setError('Speech recognition not supported');\n      return;\n    }\n\n    try {\n      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n      const recognition = new SpeechRecognition();\n      \n      recognition.continuous = true;\n      recognition.interimResults = true;\n      recognition.lang = 'en-US';\n\n      recognition.onstart = () => {\n        setIsListening(true);\n        setError(null);\n        connectWebSocket();\n      };\n\n      recognition.onresult = (event: any) => {\n        let finalTranscript = '';\n        let interimTranscript = '';\n\n        for (let i = event.resultIndex; i < event.results.length; i++) {\n          const result = event.results[i];\n          if (result.isFinal) {\n            finalTranscript += result[0].transcript;\n          } else {\n            interimTranscript += result[0].transcript;\n          }\n        }\n\n        if (finalTranscript) {\n          setTranscript(prev => prev + '\\n' + finalTranscript);\n          \n          // Send speech data to server\n          if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {\n            wsRef.current.send(JSON.stringify({\n              type: 'speech_data',\n              transcript: finalTranscript,\n              confidence: event.results[event.resultIndex]?.[0]?.confidence || 0.8,\n              timestamp: Date.now()\n            }));\n          }\n        }\n      };\n\n      recognition.onerror = (event: any) => {\n        setError(`Speech recognition error: ${event.error}`);\n        setIsListening(false);\n      };\n\n      recognition.onend = () => {\n        setIsListening(false);\n        if (wsRef.current) {\n          wsRef.current.close();\n        }\n      };\n\n      recognitionRef.current = recognition;\n      recognition.start();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to start speech recognition');\n    }\n  }, [connectWebSocket]);\n\n  const stopListening = useCallback(() => {\n    if (recognitionRef.current) {\n      recognitionRef.current.stop();\n    }\n    if (wsRef.current) {\n      wsRef.current.close();\n    }\n    setIsListening(false);\n  }, []);\n\n  return {\n    isListening,\n    transcript,\n    error,\n    startListening,\n    stopListening,\n  };\n}\n", "size_bytes": 3312}, "client/src/hooks/use-toast.ts": {"content": "import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n", "size_bytes": 3895}, "client/src/hooks/use-webrtc.ts": {"content": "import { useState, useEffect, useCallback, useRef } from \"react\";\n\nexport function useWebRTC() {\n  const [isConnected, setIsConnected] = useState(false);\n  const [localStream, setLocalStream] = useState<MediaStream | null>(null);\n  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);\n  const [error, setError] = useState<string | null>(null);\n  \n  const wsRef = useRef<WebSocket | null>(null);\n  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);\n\n  const initializeWebRTC = useCallback(async () => {\n    try {\n      // Get user media\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: true,\n        audio: true,\n      });\n      setLocalStream(stream);\n\n      // Create peer connection\n      const peerConnection = new RTCPeerConnection({\n        iceServers: [\n          { urls: 'stun:stun.l.google.com:19302' }\n        ]\n      });\n\n      // Add local stream to peer connection\n      stream.getTracks().forEach(track => {\n        peerConnection.addTrack(track, stream);\n      });\n\n      // Handle remote stream\n      peerConnection.ontrack = (event) => {\n        setRemoteStream(event.streams[0]);\n      };\n\n      peerConnectionRef.current = peerConnection;\n      // Don't set isConnected here - let WebSocket state drive it\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to initialize WebRTC');\n    }\n  }, []);\n\n  const connectToSession = useCallback((sessionId: string) => {\n    // Check if WebSocket is already open or connecting\n    if (wsRef.current && (wsRef.current.readyState === WebSocket.OPEN || wsRef.current.readyState === WebSocket.CONNECTING)) {\n      return;\n    }\n\n    const protocol = window.location.protocol === \"https:\" ? \"wss:\" : \"ws:\";\n    const wsUrl = `${protocol}//${window.location.host}/ws`;\n    \n    const ws = new WebSocket(wsUrl);\n    \n    ws.onopen = () => {\n      ws.send(JSON.stringify({ type: 'join_session', sessionId }));\n      wsRef.current = ws;\n      setIsConnected(true);\n    };\n\n    ws.onmessage = (event) => {\n      const data = JSON.parse(event.data);\n      // Handle WebRTC signaling messages here\n      console.log('WebSocket message:', data);\n    };\n\n    ws.onerror = (error) => {\n      setError('WebSocket connection failed');\n      setIsConnected(false);\n    };\n\n    ws.onclose = () => {\n      setIsConnected(false);\n    };\n  }, []);\n\n  const connect = useCallback(async (sessionId: string) => {\n    // Only initialize WebRTC if peer connection doesn't exist\n    if (!peerConnectionRef.current) {\n      await initializeWebRTC();\n    }\n    connectToSession(sessionId);\n  }, [initializeWebRTC, connectToSession]);\n\n  const disconnect = useCallback(() => {\n    if (localStream) {\n      localStream.getTracks().forEach(track => track.stop());\n      setLocalStream(null);\n    }\n    \n    if (peerConnectionRef.current) {\n      peerConnectionRef.current.close();\n      peerConnectionRef.current = null;\n    }\n    \n    if (wsRef.current) {\n      wsRef.current.close();\n      wsRef.current = null;\n    }\n    \n    setIsConnected(false);\n    setRemoteStream(null);\n  }, [localStream]);\n\n  useEffect(() => {\n    return () => {\n      disconnect();\n    };\n  }, [disconnect]);\n\n  return {\n    isConnected,\n    localStream,\n    remoteStream,\n    error,\n    connect,\n    disconnect,\n  };\n}\n", "size_bytes": 3322}, "client/src/lib/audio-analysis.ts": {"content": "export interface AudioAnalysisData {\n  rms: number;\n  pitch: number;\n  viseme?: string;\n}\n\nexport class AudioAnalyzer {\n  private audioContext: AudioContext | null = null;\n  private analyzer: AnalyserNode | null = null;\n  private dataArray: Uint8Array | null = null;\n  private source: MediaStreamAudioSourceNode | null = null;\n  private isAnalyzing = false;\n  private animationFrame: number | null = null;\n\n  constructor(private onFrame: (data: AudioAnalysisData) => void) {}\n\n  async start(stream: MediaStream): Promise<void> {\n    try {\n      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();\n      this.analyzer = this.audioContext.createAnalyser();\n      this.analyzer.fftSize = 2048;\n      \n      const bufferLength = this.analyzer.frequencyBinCount;\n      this.dataArray = new Uint8Array(bufferLength);\n      \n      this.source = this.audioContext.createMediaStreamSource(stream);\n      this.source.connect(this.analyzer);\n      \n      this.isAnalyzing = true;\n      this.analyze();\n    } catch (error) {\n      console.error('Failed to start audio analysis:', error);\n    }\n  }\n\n  stop(): void {\n    this.isAnalyzing = false;\n    \n    if (this.animationFrame) {\n      cancelAnimationFrame(this.animationFrame);\n      this.animationFrame = null;\n    }\n    \n    if (this.source) {\n      this.source.disconnect();\n      this.source = null;\n    }\n    \n    if (this.audioContext) {\n      this.audioContext.close();\n      this.audioContext = null;\n    }\n    \n    this.analyzer = null;\n    this.dataArray = null;\n  }\n\n  private analyze = (): void => {\n    if (!this.isAnalyzing || !this.analyzer || !this.dataArray) return;\n\n    this.analyzer.getByteFrequencyData(this.dataArray);\n    \n    // Calculate RMS (Root Mean Square) for amplitude\n    let sum = 0;\n    for (let i = 0; i < this.dataArray.length; i++) {\n      const normalized = this.dataArray[i] / 255;\n      sum += normalized * normalized;\n    }\n    const rms = Math.sqrt(sum / this.dataArray.length);\n    \n    // Estimate fundamental frequency (pitch)\n    const pitch = this.estimatePitch(this.dataArray);\n    \n    this.onFrame({\n      rms: Math.min(rms * 2, 1), // Amplify and clamp to [0,1]\n      pitch,\n    });\n    \n    this.animationFrame = requestAnimationFrame(this.analyze);\n  };\n\n  private estimatePitch(dataArray: Uint8Array): number {\n    // Simple pitch estimation using the strongest frequency bin\n    let maxAmplitude = 0;\n    let maxIndex = 0;\n    \n    // Focus on speech frequency range (80-1000 Hz)\n    const minIndex = Math.floor((80 * dataArray.length) / (this.audioContext!.sampleRate / 2));\n    const maxFreqIndex = Math.floor((1000 * dataArray.length) / (this.audioContext!.sampleRate / 2));\n    \n    for (let i = minIndex; i < Math.min(maxFreqIndex, dataArray.length); i++) {\n      if (dataArray[i] > maxAmplitude) {\n        maxAmplitude = dataArray[i];\n        maxIndex = i;\n      }\n    }\n    \n    // Convert bin index to frequency\n    const frequency = (maxIndex * this.audioContext!.sampleRate / 2) / dataArray.length;\n    \n    // Return frequency or default if no strong signal\n    return maxAmplitude > 10 ? frequency : 180; // Default to ~180Hz\n  }\n}\n", "size_bytes": 3175}, "client/src/lib/queryClient.ts": {"content": "import { QueryClient, QueryFunction } from \"@tanstack/react-query\";\n\nasync function throwIfResNotOk(res: Response) {\n  if (!res.ok) {\n    const text = (await res.text()) || res.statusText;\n    throw new Error(`${res.status}: ${text}`);\n  }\n}\n\nexport async function apiRequest(\n  method: string,\n  url: string,\n  data?: unknown | undefined,\n): Promise<Response> {\n  const res = await fetch(url, {\n    method,\n    headers: data ? { \"Content-Type\": \"application/json\" } : {},\n    body: data ? JSON.stringify(data) : undefined,\n    credentials: \"include\",\n  });\n\n  await throwIfResNotOk(res);\n  return res;\n}\n\ntype UnauthorizedBehavior = \"returnNull\" | \"throw\";\nexport const getQueryFn: <T>(options: {\n  on401: UnauthorizedBehavior;\n}) => QueryFunction<T> =\n  ({ on401: unauthorizedBehavior }) =>\n  async ({ queryKey }) => {\n    const res = await fetch(queryKey.join(\"/\") as string, {\n      credentials: \"include\",\n    });\n\n    if (unauthorizedBehavior === \"returnNull\" && res.status === 401) {\n      return null;\n    }\n\n    await throwIfResNotOk(res);\n    return await res.json();\n  };\n\nexport const queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      queryFn: getQueryFn({ on401: \"throw\" }),\n      refetchInterval: false,\n      refetchOnWindowFocus: false,\n      staleTime: Infinity,\n      retry: false,\n    },\n    mutations: {\n      retry: false,\n    },\n  },\n});\n", "size_bytes": 1383}, "client/src/lib/three-orb.ts": {"content": "import * as THREE from 'three';\n\ninterface OrbControl {\n  setState(state: \"welcome\" | \"discovery\" | \"coaching\" | \"reveal\" | \"upsell\" | \"idle\"): void;\n  onTTSFrame(data: { time: number; rms: number; pitch: number; viseme?: string }): void;\n  setEngagement(x: number): void;\n  setIntensity(x: number): void;\n  dispose(): void;\n}\n\nexport function createOrbRenderer(container: HTMLElement): OrbControl {\n  // Scene setup\n  const scene = new THREE.Scene();\n  const camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);\n  const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });\n  \n  renderer.setSize(container.clientWidth, container.clientHeight);\n  renderer.setClearColor(0x000000, 0);\n  container.appendChild(renderer.domElement);\n\n  // Orb geometry and material\n  const orbGeometry = new THREE.SphereGeometry(1, 64, 64);\n  \n  // Custom shader material for the orb\n  const orbMaterial = new THREE.ShaderMaterial({\n    transparent: true,\n    uniforms: {\n      uTime: { value: 0 },\n      uRMS: { value: 0 },\n      uPitch: { value: 0 },\n      uHueBase: { value: 0.55 }, // cyan/blue\n      uHueShift: { value: 0.0 },\n      uEngagement: { value: 0.8 },\n      uIntensity: { value: 0.5 },\n    },\n    vertexShader: `\n      varying vec3 vPos;\n      varying vec3 vNormal;\n      \n      void main() {\n        vPos = position;\n        vNormal = normal;\n        gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n      }\n    `,\n    fragmentShader: `\n      uniform float uTime;\n      uniform float uRMS;\n      uniform float uPitch;\n      uniform float uHueBase;\n      uniform float uHueShift;\n      uniform float uEngagement;\n      uniform float uIntensity;\n      \n      varying vec3 vPos;\n      varying vec3 vNormal;\n      \n      // Simple noise function\n      float hash(vec3 p) {\n        return fract(sin(dot(p, vec3(12.9898, 78.233, 37.719))) * 43758.5453);\n      }\n      \n      float noise(vec3 p) {\n        vec3 i = floor(p);\n        vec3 f = fract(p);\n        \n        float n = mix(\n          mix(mix(hash(i + vec3(0,0,0)), hash(i + vec3(1,0,0)), f.x),\n              mix(hash(i + vec3(0,1,0)), hash(i + vec3(1,1,0)), f.x), f.y),\n          mix(mix(hash(i + vec3(0,0,1)), hash(i + vec3(1,0,1)), f.x),\n              mix(hash(i + vec3(0,1,1)), hash(i + vec3(1,1,1)), f.x), f.y), f.z);\n        return n;\n      }\n      \n      vec3 hsv2rgb(vec3 c) {\n        vec4 K = vec4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);\n        vec3 p = abs(fract(c.xxx + K.xyz) * 6.0 - K.www);\n        return c.z * mix(K.xxx, clamp(p - K.xxx, 0.0, 1.0), c.y);\n      }\n      \n      void main() {\n        vec3 p = normalize(vPos);\n        \n        // Fresnel effect\n        float fresnel = pow(1.0 - abs(dot(vNormal, vec3(0, 0, 1))), 2.0);\n        \n        // Noise for surface distortion\n        float n = noise(p * 3.0 + vec3(uTime * 0.5)) * 0.15;\n        \n        // Base color with hue shifting\n        float hue = uHueBase + uHueShift + n * 0.1;\n        vec3 baseColor = hsv2rgb(vec3(hue, 0.8, 1.0));\n        \n        // Combine effects\n        float alpha = clamp(0.3 + fresnel * 0.7 + uRMS * 0.6 + n, 0.0, 1.0);\n        vec3 finalColor = baseColor * (0.6 + uRMS * 0.8) * uEngagement;\n        \n        gl_FragColor = vec4(finalColor, alpha * 0.8);\n      }\n    `\n  });\n\n  const orb = new THREE.Mesh(orbGeometry, orbMaterial);\n  scene.add(orb);\n\n  // Particle system for aura\n  const particleCount = 100;\n  const particles = new THREE.BufferGeometry();\n  const positions = new Float32Array(particleCount * 3);\n  const velocities = new Float32Array(particleCount * 3);\n\n  for (let i = 0; i < particleCount; i++) {\n    const i3 = i * 3;\n    // Random positions around sphere\n    const radius = 1.5 + Math.random() * 0.5;\n    const theta = Math.random() * Math.PI * 2;\n    const phi = Math.random() * Math.PI;\n    \n    positions[i3] = radius * Math.sin(phi) * Math.cos(theta);\n    positions[i3 + 1] = radius * Math.sin(phi) * Math.sin(theta);\n    positions[i3 + 2] = radius * Math.cos(phi);\n    \n    velocities[i3] = (Math.random() - 0.5) * 0.02;\n    velocities[i3 + 1] = (Math.random() - 0.5) * 0.02;\n    velocities[i3 + 2] = (Math.random() - 0.5) * 0.02;\n  }\n\n  particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));\n  particles.setAttribute('velocity', new THREE.BufferAttribute(velocities, 3));\n\n  const particleMaterial = new THREE.PointsMaterial({\n    color: 0x00E6B8,\n    size: 0.02,\n    transparent: true,\n    opacity: 0.6,\n    blending: THREE.AdditiveBlending,\n  });\n\n  const particleSystem = new THREE.Points(particles, particleMaterial);\n  scene.add(particleSystem);\n\n  camera.position.z = 3;\n\n  // Animation state\n  let currentState = \"idle\";\n  let animationId: number;\n\n  const animate = () => {\n    animationId = requestAnimationFrame(animate);\n    \n    const time = Date.now() * 0.001;\n    orbMaterial.uniforms.uTime.value = time;\n    \n    // Rotate orb slowly\n    orb.rotation.y += 0.005;\n    \n    // Animate particles\n    const positions = particleSystem.geometry.attributes.position.array as Float32Array;\n    const velocities = particleSystem.geometry.attributes.velocity.array as Float32Array;\n    \n    for (let i = 0; i < particleCount; i++) {\n      const i3 = i * 3;\n      positions[i3] += velocities[i3];\n      positions[i3 + 1] += velocities[i3 + 1];\n      positions[i3 + 2] += velocities[i3 + 2];\n      \n      // Keep particles in bounds\n      const radius = Math.sqrt(positions[i3]**2 + positions[i3 + 1]**2 + positions[i3 + 2]**2);\n      if (radius > 2.5 || radius < 1.2) {\n        velocities[i3] *= -1;\n        velocities[i3 + 1] *= -1;\n        velocities[i3 + 2] *= -1;\n      }\n    }\n    \n    particleSystem.geometry.attributes.position.needsUpdate = true;\n    \n    renderer.render(scene, camera);\n  };\n\n  animate();\n\n  // Resize handler\n  const handleResize = () => {\n    const width = container.clientWidth;\n    const height = container.clientHeight;\n    camera.aspect = width / height;\n    camera.updateProjectionMatrix();\n    renderer.setSize(width, height);\n  };\n\n  window.addEventListener('resize', handleResize);\n\n  // Control interface\n  const control: OrbControl = {\n    setState(state) {\n      currentState = state;\n      \n      // Update colors based on state\n      const stateColors = {\n        welcome: { hue: 0.55, intensity: 0.6 }, // Teal\n        discovery: { hue: 0.5, intensity: 0.7 }, // Cyan\n        coaching: { hue: 0.6, intensity: 0.9 }, // Blue\n        reveal: { hue: 0.45, intensity: 0.8 }, // Aqua\n        upsell: { hue: 0.65, intensity: 0.7 }, // Royal blue\n        idle: { hue: 0.55, intensity: 0.5 }\n      };\n      \n      const config = stateColors[state] || stateColors.idle;\n      orbMaterial.uniforms.uHueBase.value = config.hue;\n      orbMaterial.uniforms.uIntensity.value = config.intensity;\n    },\n\n    onTTSFrame(data) {\n      orbMaterial.uniforms.uRMS.value = data.rms;\n      orbMaterial.uniforms.uHueShift.value = (data.pitch - 180) / 600.0;\n      \n      // Scale orb based on audio\n      const scale = 1.0 + data.rms * 0.3;\n      orb.scale.setScalar(scale);\n      \n      // Update particle opacity\n      particleMaterial.opacity = 0.6 + data.rms * 0.4;\n    },\n\n    setEngagement(x) {\n      orbMaterial.uniforms.uEngagement.value = x;\n    },\n\n    setIntensity(x) {\n      orbMaterial.uniforms.uIntensity.value = x;\n    },\n\n    dispose() {\n      cancelAnimationFrame(animationId);\n      window.removeEventListener('resize', handleResize);\n      container.removeChild(renderer.domElement);\n      renderer.dispose();\n      orbGeometry.dispose();\n      orbMaterial.dispose();\n      particles.dispose();\n      particleMaterial.dispose();\n    }\n  };\n\n  return control;\n}\n", "size_bytes": 7690}, "client/src/lib/utils.ts": {"content": "import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n", "size_bytes": 166}, "client/src/pages/assessment.tsx": {"content": "import { useEffect, useState, useRef } from \"react\";\nimport { useParams } from \"wouter\";\nimport { useQuery, useMutation } from \"@tanstack/react-query\";\nimport { apiRequest, queryClient } from \"@/lib/queryClient\";\nimport type { AssessmentSession } from \"@shared/schema\";\nimport Video<PERSON>all from \"@/components/video-call\";\nimport TranscriptPanel from \"@/components/transcript-panel\";\nimport AssessmentResults from \"@/components/assessment-results\";\nimport { useWebRTC } from \"@/hooks/use-webrtc\";\nimport { useSpeech } from \"@/hooks/use-speech\";\nimport { Button } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\n\nexport default function Assessment() {\n  const { sessionId } = useParams();\n  const [currentSessionId, setCurrentSessionId] = useState<string | null>(sessionId || null);\n  const [showTranscript, setShowTranscript] = useState(false);\n  const [showResults, setShowResults] = useState(false);\n  const hasConnectedRef = useRef<string | null>(null);\n  \n  const { isConnected, localStream, remoteStream, connect, disconnect } = useWebRTC();\n  const { isListening, transcript, startListening, stopListening } = useSpeech();\n\n  // Fetch current session data\n  const { data: session, isLoading } = useQuery({\n    queryKey: ['/api/assessment', currentSessionId],\n    enabled: !!currentSessionId,\n  });\n\n  // Start new assessment session\n  const startAssessmentMutation = useMutation({\n    mutationFn: async () => {\n      const response = await apiRequest(\"POST\", \"/api/assessment/start\", {\n        userId: \"demo-user\", // In real app, get from auth\n      });\n      return response.json();\n    },\n    onSuccess: (data) => {\n      setCurrentSessionId(data.id);\n      queryClient.invalidateQueries({ queryKey: ['/api/assessment'] });\n    },\n  });\n\n  // Complete assessment\n  const completeAssessmentMutation = useMutation({\n    mutationFn: async () => {\n      const response = await apiRequest(\"POST\", `/api/assessment/${currentSessionId}/complete`, {});\n      return response.json();\n    },\n    onSuccess: () => {\n      setShowResults(true);\n      queryClient.invalidateQueries({ queryKey: ['/api/assessment', currentSessionId] });\n    },\n  });\n\n  const handleStartAssessment = () => {\n    startAssessmentMutation.mutate();\n  };\n\n  const handleEndCall = () => {\n    disconnect();\n    stopListening();\n    setCurrentSessionId(null);\n    hasConnectedRef.current = null;\n  };\n\n  const handleNextPhase = () => {\n    if (session?.phase === \"reveal\") {\n      completeAssessmentMutation.mutate();\n    }\n  };\n\n  useEffect(() => {\n    if (!currentSessionId) return;\n    if (hasConnectedRef.current === currentSessionId) return;\n    hasConnectedRef.current = currentSessionId;\n    connect(currentSessionId);\n  }, [currentSessionId, connect]);\n\n  if (!currentSessionId) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-card to-background flex items-center justify-center p-6\">\n        <Card className=\"w-full max-w-md\">\n          <CardContent className=\"pt-6 text-center\">\n            <div className=\"mb-6\">\n              <div className=\"w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center\">\n                <svg className=\"w-10 h-10 text-primary\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z\"/>\n                </svg>\n              </div>\n              <h1 className=\"text-2xl font-display font-bold mb-2\">AI Fitness Assessment</h1>\n              <p className=\"text-muted-foreground\">Get your personalized training plan with live movement analysis</p>\n            </div>\n            \n            <Button \n              onClick={handleStartAssessment}\n              disabled={startAssessmentMutation.isPending}\n              className=\"w-full\"\n              data-testid=\"button-start-assessment\"\n            >\n              {startAssessmentMutation.isPending ? \"Starting...\" : \"Start Assessment\"}\n            </Button>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-background via-card to-background flex items-center justify-center\">\n        <div className=\"animate-spin w-8 h-8 border-4 border-primary border-t-transparent rounded-full\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-background via-card to-background\">\n      {/* Top Control Bar */}\n      <div className=\"fixed top-0 left-0 right-0 z-50 bg-card/80 backdrop-blur-md border-b border-border\">\n        <div className=\"flex items-center justify-between px-6 py-4\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-accent rounded-full animate-pulse\"></div>\n              <span className=\"text-sm font-medium text-muted-foreground\">Live Assessment</span>\n              <span className=\"text-sm text-accent font-medium\" data-testid=\"text-session-time\">\n                {session?.createdAt ? new Date(session.createdAt).toLocaleTimeString() : \"00:00\"}\n              </span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center space-x-3\">\n            <Button \n              variant=\"ghost\" \n              size=\"sm\"\n              onClick={() => isListening ? stopListening() : startListening()}\n              data-testid=\"button-toggle-mic\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z\"/>\n                <path d=\"M17.3 11c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z\"/>\n              </svg>\n            </Button>\n            \n            <Button \n              variant=\"ghost\" \n              size=\"sm\"\n              onClick={() => setShowTranscript(!showTranscript)}\n              data-testid=\"button-toggle-transcript\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z\"/>\n              </svg>\n            </Button>\n            \n            <Button \n              variant=\"destructive\" \n              size=\"sm\"\n              onClick={handleEndCall}\n              data-testid=\"button-end-call\"\n            >\n              <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.71.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7 0-.28.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.***********.29.71 0 .28-.11.53-.29.7l-2.48 2.48c-.18.18-.43.29-.71.29-.28 0-.53-.11-.71-.28-.79-.74-1.69-1.36-2.67-1.85-.33-.16-.56-.5-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z\"/>\n              </svg>\n            </Button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"pt-20 pb-6 px-6 min-h-screen\">\n        <div className=\"max-w-7xl mx-auto\">\n          <VideoCall \n            session={session}\n            localStream={localStream}\n            remoteStream={remoteStream}\n            transcript={transcript}\n          />\n          \n          {/* Session Progress Footer */}\n          <div className=\"bg-card rounded-2xl border border-border p-6 mt-6\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex-1\">\n                <div className=\"flex items-center space-x-4 mb-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-8 h-8 rounded-full bg-accent flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-accent-foreground\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z\"/>\n                      </svg>\n                    </div>\n                    <span className=\"font-medium\">Phase Complete</span>\n                  </div>\n                  <div className=\"flex-1 h-2 bg-muted rounded-full\">\n                    <div \n                      className=\"h-2 bg-gradient-to-r from-accent to-primary rounded-full transition-all duration-1000\" \n                      style={{ width: session?.phase === \"complete\" ? \"100%\" : \"65%\" }}\n                    ></div>\n                  </div>\n                  <span className=\"text-sm text-muted-foreground\" data-testid=\"text-progress\">\n                    {session?.phase === \"complete\" ? \"100%\" : \"65%\"} Complete\n                  </span>\n                </div>\n                \n                <div className=\"text-sm text-muted-foreground\">\n                  Current Phase: <span className=\"text-foreground font-medium capitalize\" data-testid=\"text-current-phase\">\n                    {session?.phase || \"Loading...\"}\n                  </span>\n                </div>\n              </div>\n              \n              <Button \n                onClick={handleNextPhase}\n                disabled={completeAssessmentMutation.isPending || session?.phase !== \"reveal\"}\n                data-testid=\"button-next-phase\"\n              >\n                {completeAssessmentMutation.isPending ? \"Analyzing...\" : \"Complete Assessment\"}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Transcript Panel */}\n      <TranscriptPanel \n        isOpen={showTranscript}\n        onClose={() => setShowTranscript(false)}\n        transcript={transcript}\n        session={session}\n      />\n\n      {/* Assessment Results Modal */}\n      <AssessmentResults \n        isOpen={showResults}\n        onClose={() => setShowResults(false)}\n        session={session}\n      />\n    </div>\n  );\n}\n", "size_bytes": 10111}, "client/src/pages/not-found.tsx": {"content": "import { Card, CardContent } from \"@/components/ui/card\";\nimport { AlertCircle } from \"lucide-react\";\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen w-full flex items-center justify-center bg-gray-50\">\n      <Card className=\"w-full max-w-md mx-4\">\n        <CardContent className=\"pt-6\">\n          <div className=\"flex mb-4 gap-2\">\n            <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">404 Page Not Found</h1>\n          </div>\n\n          <p className=\"mt-4 text-sm text-gray-600\">\n            Did you forget to add the page to the router?\n          </p>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n", "size_bytes": 711}, "client/src/stores/assessment-store.ts": {"content": "import { create } from 'zustand';\n\ninterface AssessmentState {\n  sessionId: string | null;\n  phase: string;\n  transcript: string;\n  currentPrompt: string;\n  repCount: number;\n  currentExercise: string;\n  scores: any;\n  plan: any;\n  \n  setSessionId: (id: string) => void;\n  setPhase: (phase: string) => void;\n  appendTranscript: (text: string) => void;\n  setCurrentPrompt: (prompt: string) => void;\n  incrementRepCount: () => void;\n  resetRepCount: () => void;\n  setCurrentExercise: (exercise: string) => void;\n  setScores: (scores: any) => void;\n  setPlan: (plan: any) => void;\n  reset: () => void;\n}\n\nexport const useAssessmentStore = create<AssessmentState>((set) => ({\n  sessionId: null,\n  phase: 'welcome',\n  transcript: '',\n  currentPrompt: \"What's the one change you want to see in the next 8-12 weeks?\",\n  repCount: 0,\n  currentExercise: 'Air Squat x5',\n  scores: null,\n  plan: null,\n  \n  setSessionId: (id) => set({ sessionId: id }),\n  setPhase: (phase) => set({ phase }),\n  appendTranscript: (text) => set((state) => ({ \n    transcript: state.transcript + (state.transcript ? '\\n' : '') + text \n  })),\n  setCurrentPrompt: (prompt) => set({ currentPrompt: prompt }),\n  incrementRepCount: () => set((state) => ({ repCount: state.repCount + 1 })),\n  resetRepCount: () => set({ repCount: 0 }),\n  setCurrentExercise: (exercise) => set({ currentExercise: exercise }),\n  setScores: (scores) => set({ scores }),\n  setPlan: (plan) => set({ plan }),\n  reset: () => set({\n    sessionId: null,\n    phase: 'welcome',\n    transcript: '',\n    currentPrompt: \"What's the one change you want to see in the next 8-12 weeks?\",\n    repCount: 0,\n    currentExercise: 'Air Squat x5',\n    scores: null,\n    plan: null,\n  }),\n}));\n", "size_bytes": 1716}, "client/src/components/ui/accordion.tsx": {"content": "import * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n", "size_bytes": 1977}, "client/src/components/ui/alert-dialog.tsx": {"content": "import * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n", "size_bytes": 4420}, "client/src/components/ui/alert.tsx": {"content": "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n", "size_bytes": 1584}, "client/src/components/ui/aspect-ratio.tsx": {"content": "import * as AspectRatioPrimitive from \"@radix-ui/react-aspect-ratio\"\n\nconst AspectRatio = AspectRatioPrimitive.Root\n\nexport { AspectRatio }\n", "size_bytes": 140}, "client/src/components/ui/avatar.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n", "size_bytes": 1419}, "client/src/components/ui/badge.tsx": {"content": "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n", "size_bytes": 1128}, "client/src/components/ui/breadcrumb.tsx": {"content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ReactNode\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />)\nBreadcrumb.displayName = \"Breadcrumb\"\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n))\nBreadcrumbList.displayName = \"BreadcrumbList\"\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n))\nBreadcrumbItem.displayName = \"BreadcrumbItem\"\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean\n  }\n>(({ asChild, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  )\n})\nBreadcrumbLink.displayName = \"BreadcrumbLink\"\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n))\nBreadcrumbPage.displayName = \"BreadcrumbPage\"\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:w-3.5 [&>svg]:h-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n)\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\"\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"h-4 w-4\" />\n    <span className=\"sr-only\">More</span>\n  </span>\n)\nBreadcrumbEllipsis.displayName = \"BreadcrumbElipssis\"\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n}\n", "size_bytes": 2712}, "client/src/components/ui/button.tsx": {"content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "size_bytes": 1901}, "client/src/components/ui/calendar.tsx": {"content": "import * as React from \"react\"\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\nimport { DayPicker } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  ...props\n}: CalendarProps) {\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\"p-3\", className)}\n      classNames={{\n        months: \"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0\",\n        month: \"space-y-4\",\n        caption: \"flex justify-center pt-1 relative items-center\",\n        caption_label: \"text-sm font-medium\",\n        nav: \"space-x-1 flex items-center\",\n        nav_button: cn(\n          buttonVariants({ variant: \"outline\" }),\n          \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\n        ),\n        nav_button_previous: \"absolute left-1\",\n        nav_button_next: \"absolute right-1\",\n        table: \"w-full border-collapse space-y-1\",\n        head_row: \"flex\",\n        head_cell:\n          \"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]\",\n        row: \"flex w-full mt-2\",\n        cell: \"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n        day: cn(\n          buttonVariants({ variant: \"ghost\" }),\n          \"h-9 w-9 p-0 font-normal aria-selected:opacity-100\"\n        ),\n        day_range_end: \"day-range-end\",\n        day_selected:\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n        day_today: \"bg-accent text-accent-foreground\",\n        day_outside:\n          \"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground\",\n        day_disabled: \"text-muted-foreground opacity-50\",\n        day_range_middle:\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n        day_hidden: \"invisible\",\n        ...classNames,\n      }}\n      components={{\n        IconLeft: ({ className, ...props }) => (\n          <ChevronLeft className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n        IconRight: ({ className, ...props }) => (\n          <ChevronRight className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n      }}\n      {...props}\n    />\n  )\n}\nCalendar.displayName = \"Calendar\"\n\nexport { Calendar }\n", "size_bytes": 2695}, "client/src/components/ui/card.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n", "size_bytes": 1858}, "client/src/components/ui/carousel.tsx": {"content": "import * as React from \"react\"\nimport useEmblaCarousel, {\n  type UseEmblaCarouselType,\n} from \"embla-carousel-react\"\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\n\ntype CarouselApi = UseEmblaCarouselType[1]\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\ntype CarouselOptions = UseCarouselParameters[0]\ntype CarouselPlugin = UseCarouselParameters[1]\n\ntype CarouselProps = {\n  opts?: CarouselOptions\n  plugins?: CarouselPlugin\n  orientation?: \"horizontal\" | \"vertical\"\n  setApi?: (api: CarouselApi) => void\n}\n\ntype CarouselContextProps = {\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\n  api: ReturnType<typeof useEmblaCarousel>[1]\n  scrollPrev: () => void\n  scrollNext: () => void\n  canScrollPrev: boolean\n  canScrollNext: boolean\n} & CarouselProps\n\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\n\nfunction useCarousel() {\n  const context = React.useContext(CarouselContext)\n\n  if (!context) {\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\n  }\n\n  return context\n}\n\nconst Carousel = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & CarouselProps\n>(\n  (\n    {\n      orientation = \"horizontal\",\n      opts,\n      setApi,\n      plugins,\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const [carouselRef, api] = useEmblaCarousel(\n      {\n        ...opts,\n        axis: orientation === \"horizontal\" ? \"x\" : \"y\",\n      },\n      plugins\n    )\n    const [canScrollPrev, setCanScrollPrev] = React.useState(false)\n    const [canScrollNext, setCanScrollNext] = React.useState(false)\n\n    const onSelect = React.useCallback((api: CarouselApi) => {\n      if (!api) {\n        return\n      }\n\n      setCanScrollPrev(api.canScrollPrev())\n      setCanScrollNext(api.canScrollNext())\n    }, [])\n\n    const scrollPrev = React.useCallback(() => {\n      api?.scrollPrev()\n    }, [api])\n\n    const scrollNext = React.useCallback(() => {\n      api?.scrollNext()\n    }, [api])\n\n    const handleKeyDown = React.useCallback(\n      (event: React.KeyboardEvent<HTMLDivElement>) => {\n        if (event.key === \"ArrowLeft\") {\n          event.preventDefault()\n          scrollPrev()\n        } else if (event.key === \"ArrowRight\") {\n          event.preventDefault()\n          scrollNext()\n        }\n      },\n      [scrollPrev, scrollNext]\n    )\n\n    React.useEffect(() => {\n      if (!api || !setApi) {\n        return\n      }\n\n      setApi(api)\n    }, [api, setApi])\n\n    React.useEffect(() => {\n      if (!api) {\n        return\n      }\n\n      onSelect(api)\n      api.on(\"reInit\", onSelect)\n      api.on(\"select\", onSelect)\n\n      return () => {\n        api?.off(\"select\", onSelect)\n      }\n    }, [api, onSelect])\n\n    return (\n      <CarouselContext.Provider\n        value={{\n          carouselRef,\n          api: api,\n          opts,\n          orientation:\n            orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\n          scrollPrev,\n          scrollNext,\n          canScrollPrev,\n          canScrollNext,\n        }}\n      >\n        <div\n          ref={ref}\n          onKeyDownCapture={handleKeyDown}\n          className={cn(\"relative\", className)}\n          role=\"region\"\n          aria-roledescription=\"carousel\"\n          {...props}\n        >\n          {children}\n        </div>\n      </CarouselContext.Provider>\n    )\n  }\n)\nCarousel.displayName = \"Carousel\"\n\nconst CarouselContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const { carouselRef, orientation } = useCarousel()\n\n  return (\n    <div ref={carouselRef} className=\"overflow-hidden\">\n      <div\n        ref={ref}\n        className={cn(\n          \"flex\",\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n})\nCarouselContent.displayName = \"CarouselContent\"\n\nconst CarouselItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const { orientation } = useCarousel()\n\n  return (\n    <div\n      ref={ref}\n      role=\"group\"\n      aria-roledescription=\"slide\"\n      className={cn(\n        \"min-w-0 shrink-0 grow-0 basis-full\",\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nCarouselItem.displayName = \"CarouselItem\"\n\nconst CarouselPrevious = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<typeof Button>\n>(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\n\n  return (\n    <Button\n      ref={ref}\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute  h-8 w-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"-left-12 top-1/2 -translate-y-1/2\"\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollPrev}\n      onClick={scrollPrev}\n      {...props}\n    >\n      <ArrowLeft className=\"h-4 w-4\" />\n      <span className=\"sr-only\">Previous slide</span>\n    </Button>\n  )\n})\nCarouselPrevious.displayName = \"CarouselPrevious\"\n\nconst CarouselNext = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<typeof Button>\n>(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\n\n  return (\n    <Button\n      ref={ref}\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute h-8 w-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"-right-12 top-1/2 -translate-y-1/2\"\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollNext}\n      onClick={scrollNext}\n      {...props}\n    >\n      <ArrowRight className=\"h-4 w-4\" />\n      <span className=\"sr-only\">Next slide</span>\n    </Button>\n  )\n})\nCarouselNext.displayName = \"CarouselNext\"\n\nexport {\n  type CarouselApi,\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselPrevious,\n  CarouselNext,\n}\n", "size_bytes": 6210}, "client/src/components/ui/chart.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    config: ChartConfig\n    children: React.ComponentProps<\n      typeof RechartsPrimitive.ResponsiveContainer\n    >[\"children\"]\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = \"Chart\"\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<\"div\"> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: \"line\" | \"dot\" | \"dashed\"\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = \"dot\",\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === \"string\"\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return (\n          <div className={cn(\"font-medium\", labelClassName)}>\n            {labelFormatter(value, payload)}\n          </div>\n        )\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n    }, [\n      label,\n      labelFormatter,\n      payload,\n      hideLabel,\n      labelClassName,\n      config,\n      labelKey,\n    ])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\n          className\n        )}\n      >\n        {!nestLabel ? tooltipLabel : null}\n        <div className=\"grid gap-1.5\">\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\n                  indicator === \"dot\" && \"items-center\"\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn(\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\n                            {\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\n                              \"w-1\": indicator === \"line\",\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                                indicator === \"dashed\",\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\n                            }\n                          )}\n                          style={\n                            {\n                              \"--color-bg\": indicatorColor,\n                              \"--color-border\": indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        \"flex flex-1 justify-between leading-none\",\n                        nestLabel ? \"items-end\" : \"items-center\"\n                      )}\n                    >\n                      <div className=\"grid gap-1.5\">\n                        {nestLabel ? tooltipLabel : null}\n                        <span className=\"text-muted-foreground\">\n                          {itemConfig?.label || item.name}\n                        </span>\n                      </div>\n                      {item.value && (\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = \"ChartTooltip\"\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> &\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(\n  (\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    if (!payload?.length) {\n      return null\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"flex items-center justify-center gap-4\",\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n          className\n        )}\n      >\n        {payload.map((item) => {\n          const key = `${nameKey || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n          return (\n            <div\n              key={item.value}\n              className={cn(\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\n              )}\n            >\n              {itemConfig?.icon && !hideIcon ? (\n                <itemConfig.icon />\n              ) : (\n                <div\n                  className=\"h-2 w-2 shrink-0 rounded-[2px]\"\n                  style={{\n                    backgroundColor: item.color,\n                  }}\n                />\n              )}\n              {itemConfig?.label}\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nChartLegendContent.displayName = \"ChartLegend\"\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n", "size_bytes": 10481}, "client/src/components/ui/checkbox.tsx": {"content": "import * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n", "size_bytes": 1056}, "client/src/components/ui/collapsible.tsx": {"content": "\"use client\"\n\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\n\nconst Collapsible = CollapsiblePrimitive.Root\n\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger\n\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\n", "size_bytes": 329}, "client/src/components/ui/command.tsx": {"content": "import * as React from \"react\"\nimport { type DialogProps } from \"@radix-ui/react-dialog\"\nimport { Command as CommandPrimitive } from \"cmdk\"\nimport { Search } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Dialog, DialogContent } from \"@/components/ui/dialog\"\n\nconst Command = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCommand.displayName = CommandPrimitive.displayName\n\nconst CommandDialog = ({ children, ...props }: DialogProps) => {\n  return (\n    <Dialog {...props}>\n      <DialogContent className=\"overflow-hidden p-0 shadow-lg\">\n        <Command className=\"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\n          {children}\n        </Command>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\nconst CommandInput = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Input>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>\n>(({ className, ...props }, ref) => (\n  <div className=\"flex items-center border-b px-3\" cmdk-input-wrapper=\"\">\n    <Search className=\"mr-2 h-4 w-4 shrink-0 opacity-50\" />\n    <CommandPrimitive.Input\n      ref={ref}\n      className={cn(\n        \"flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  </div>\n))\n\nCommandInput.displayName = CommandPrimitive.Input.displayName\n\nconst CommandList = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.List\n    ref={ref}\n    className={cn(\"max-h-[300px] overflow-y-auto overflow-x-hidden\", className)}\n    {...props}\n  />\n))\n\nCommandList.displayName = CommandPrimitive.List.displayName\n\nconst CommandEmpty = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Empty>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>\n>((props, ref) => (\n  <CommandPrimitive.Empty\n    ref={ref}\n    className=\"py-6 text-center text-sm\"\n    {...props}\n  />\n))\n\nCommandEmpty.displayName = CommandPrimitive.Empty.displayName\n\nconst CommandGroup = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Group>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Group\n    ref={ref}\n    className={cn(\n      \"overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\n\nCommandGroup.displayName = CommandPrimitive.Group.displayName\n\nconst CommandSeparator = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 h-px bg-border\", className)}\n    {...props}\n  />\n))\nCommandSeparator.displayName = CommandPrimitive.Separator.displayName\n\nconst CommandItem = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      className\n    )}\n    {...props}\n  />\n))\n\nCommandItem.displayName = CommandPrimitive.Item.displayName\n\nconst CommandShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\nCommandShortcut.displayName = \"CommandShortcut\"\n\nexport {\n  Command,\n  CommandDialog,\n  CommandInput,\n  CommandList,\n  CommandEmpty,\n  CommandGroup,\n  CommandItem,\n  CommandShortcut,\n  CommandSeparator,\n}\n", "size_bytes": 4885}, "client/src/components/ui/context-menu.tsx": {"content": "import * as React from \"react\"\nimport * as ContextMenuPrimitive from \"@radix-ui/react-context-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ContextMenu = ContextMenuPrimitive.Root\n\nconst ContextMenuTrigger = ContextMenuPrimitive.Trigger\n\nconst ContextMenuGroup = ContextMenuPrimitive.Group\n\nconst ContextMenuPortal = ContextMenuPrimitive.Portal\n\nconst ContextMenuSub = ContextMenuPrimitive.Sub\n\nconst ContextMenuRadioGroup = ContextMenuPrimitive.RadioGroup\n\nconst ContextMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <ContextMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </ContextMenuPrimitive.SubTrigger>\n))\nContextMenuSubTrigger.displayName = ContextMenuPrimitive.SubTrigger.displayName\n\nconst ContextMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-context-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuSubContent.displayName = ContextMenuPrimitive.SubContent.displayName\n\nconst ContextMenuContent = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.Portal>\n    <ContextMenuPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"z-50 max-h-[--radix-context-menu-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in fade-in-80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-context-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </ContextMenuPrimitive.Portal>\n))\nContextMenuContent.displayName = ContextMenuPrimitive.Content.displayName\n\nconst ContextMenuItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <ContextMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuItem.displayName = ContextMenuPrimitive.Item.displayName\n\nconst ContextMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <ContextMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <ContextMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </ContextMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </ContextMenuPrimitive.CheckboxItem>\n))\nContextMenuCheckboxItem.displayName =\n  ContextMenuPrimitive.CheckboxItem.displayName\n\nconst ContextMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <ContextMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <ContextMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </ContextMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </ContextMenuPrimitive.RadioItem>\n))\nContextMenuRadioItem.displayName = ContextMenuPrimitive.RadioItem.displayName\n\nconst ContextMenuLabel = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <ContextMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold text-foreground\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuLabel.displayName = ContextMenuPrimitive.Label.displayName\n\nconst ContextMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-border\", className)}\n    {...props}\n  />\n))\nContextMenuSeparator.displayName = ContextMenuPrimitive.Separator.displayName\n\nconst ContextMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\nContextMenuShortcut.displayName = \"ContextMenuShortcut\"\n\nexport {\n  ContextMenu,\n  ContextMenuTrigger,\n  ContextMenuContent,\n  ContextMenuItem,\n  ContextMenuCheckboxItem,\n  ContextMenuRadioItem,\n  ContextMenuLabel,\n  ContextMenuSeparator,\n  ContextMenuShortcut,\n  ContextMenuGroup,\n  ContextMenuPortal,\n  ContextMenuSub,\n  ContextMenuSubContent,\n  ContextMenuSubTrigger,\n  ContextMenuRadioGroup,\n}\n", "size_bytes": 7428}, "client/src/components/ui/dialog.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n", "size_bytes": 3848}, "client/src/components/ui/drawer.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport { Drawer as DrawerPrimitive } from \"vaul\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Drawer = ({\n  shouldScaleBackground = true,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (\n  <DrawerPrimitive.Root\n    shouldScaleBackground={shouldScaleBackground}\n    {...props}\n  />\n)\nDrawer.displayName = \"Drawer\"\n\nconst DrawerTrigger = DrawerPrimitive.Trigger\n\nconst DrawerPortal = DrawerPrimitive.Portal\n\nconst DrawerClose = DrawerPrimitive.Close\n\nconst DrawerOverlay = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Overlay\n    ref={ref}\n    className={cn(\"fixed inset-0 z-50 bg-black/80\", className)}\n    {...props}\n  />\n))\nDrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName\n\nconst DrawerContent = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DrawerPortal>\n    <DrawerOverlay />\n    <DrawerPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted\" />\n      {children}\n    </DrawerPrimitive.Content>\n  </DrawerPortal>\n))\nDrawerContent.displayName = \"DrawerContent\"\n\nconst DrawerHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"grid gap-1.5 p-4 text-center sm:text-left\", className)}\n    {...props}\n  />\n)\nDrawerHeader.displayName = \"DrawerHeader\"\n\nconst DrawerFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n    {...props}\n  />\n)\nDrawerFooter.displayName = \"DrawerFooter\"\n\nconst DrawerTitle = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDrawerTitle.displayName = DrawerPrimitive.Title.displayName\n\nconst DrawerDescription = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDrawerDescription.displayName = DrawerPrimitive.Description.displayName\n\nexport {\n  Drawer,\n  DrawerPortal,\n  DrawerOverlay,\n  DrawerTrigger,\n  DrawerClose,\n  DrawerContent,\n  DrawerHeader,\n  DrawerFooter,\n  DrawerTitle,\n  DrawerDescription,\n}\n", "size_bytes": 3021}, "client/src/components/ui/dropdown-menu.tsx": {"content": "import * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n", "size_bytes": 7609}, "client/src/components/ui/form.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState, formState } = useFormContext()\n\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nconst FormItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\n    </FormItemContext.Provider>\n  )\n})\nFormItem.displayName = \"FormItem\"\n\nconst FormLabel = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      ref={ref}\n      className={cn(error && \"text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n})\nFormLabel.displayName = \"FormLabel\"\n\nconst FormControl = React.forwardRef<\n  React.ElementRef<typeof Slot>,\n  React.ComponentPropsWithoutRef<typeof Slot>\n>(({ ...props }, ref) => {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      ref={ref}\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n})\nFormControl.displayName = \"FormControl\"\n\nconst FormDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      ref={ref}\n      id={formDescriptionId}\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    />\n  )\n})\nFormDescription.displayName = \"FormDescription\"\n\nconst FormMessage = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      ref={ref}\n      id={formMessageId}\n      className={cn(\"text-sm font-medium text-destructive\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n})\nFormMessage.displayName = \"FormMessage\"\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n", "size_bytes": 4120}, "client/src/components/ui/hover-card.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as HoverCardPrimitive from \"@radix-ui/react-hover-card\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst HoverCard = HoverCardPrimitive.Root\n\nconst HoverCardTrigger = HoverCardPrimitive.Trigger\n\nconst HoverCardContent = React.forwardRef<\n  React.ElementRef<typeof HoverCardPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <HoverCardPrimitive.Content\n    ref={ref}\n    align={align}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-hover-card-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nHoverCardContent.displayName = HoverCardPrimitive.Content.displayName\n\nexport { HoverCard, HoverCardTrigger, HoverCardContent }\n", "size_bytes": 1251}, "client/src/components/ui/input-otp.tsx": {"content": "import * as React from \"react\"\nimport { OTPInput, OTPInputContext } from \"input-otp\"\nimport { Dot } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst InputOTP = React.forwardRef<\n  React.ElementRef<typeof OTPInput>,\n  React.ComponentPropsWithoutRef<typeof OTPInput>\n>(({ className, containerClassName, ...props }, ref) => (\n  <OTPInput\n    ref={ref}\n    containerClassName={cn(\n      \"flex items-center gap-2 has-[:disabled]:opacity-50\",\n      containerClassName\n    )}\n    className={cn(\"disabled:cursor-not-allowed\", className)}\n    {...props}\n  />\n))\nInputOTP.displayName = \"InputOTP\"\n\nconst InputOTPGroup = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\">\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center\", className)} {...props} />\n))\nInputOTPGroup.displayName = \"InputOTPGroup\"\n\nconst InputOTPSlot = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\"> & { index: number }\n>(({ index, className, ...props }, ref) => {\n  const inputOTPContext = React.useContext(OTPInputContext)\n  const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index]\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md\",\n        isActive && \"z-10 ring-2 ring-ring ring-offset-background\",\n        className\n      )}\n      {...props}\n    >\n      {char}\n      {hasFakeCaret && (\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\n          <div className=\"h-4 w-px animate-caret-blink bg-foreground duration-1000\" />\n        </div>\n      )}\n    </div>\n  )\n})\nInputOTPSlot.displayName = \"InputOTPSlot\"\n\nconst InputOTPSeparator = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\">\n>(({ ...props }, ref) => (\n  <div ref={ref} role=\"separator\" {...props}>\n    <Dot />\n  </div>\n))\nInputOTPSeparator.displayName = \"InputOTPSeparator\"\n\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator }\n", "size_bytes": 2154}, "client/src/components/ui/input.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n", "size_bytes": 791}, "client/src/components/ui/label.tsx": {"content": "import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n", "size_bytes": 710}, "client/src/components/ui/menubar.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as MenubarPrimitive from \"@radix-ui/react-menubar\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction MenubarMenu({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Menu>) {\n  return <MenubarPrimitive.Menu {...props} />\n}\n\nfunction MenubarGroup({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Group>) {\n  return <MenubarPrimitive.Group {...props} />\n}\n\nfunction MenubarPortal({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Portal>) {\n  return <MenubarPrimitive.Portal {...props} />\n}\n\nfunction MenubarRadioGroup({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.RadioGroup>) {\n  return <MenubarPrimitive.RadioGroup {...props} />\n}\n\nfunction MenubarSub({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Sub>) {\n  return <MenubarPrimitive.Sub data-slot=\"menubar-sub\" {...props} />\n}\n\nconst Menubar = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"flex h-10 items-center space-x-1 rounded-md border bg-background p-1\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubar.displayName = MenubarPrimitive.Root.displayName\n\nconst MenubarTrigger = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-3 py-1.5 text-sm font-medium outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarTrigger.displayName = MenubarPrimitive.Trigger.displayName\n\nconst MenubarSubTrigger = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <MenubarPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </MenubarPrimitive.SubTrigger>\n))\nMenubarSubTrigger.displayName = MenubarPrimitive.SubTrigger.displayName\n\nconst MenubarSubContent = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-menubar-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarSubContent.displayName = MenubarPrimitive.SubContent.displayName\n\nconst MenubarContent = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Content>\n>(\n  (\n    { className, align = \"start\", alignOffset = -4, sideOffset = 8, ...props },\n    ref\n  ) => (\n    <MenubarPrimitive.Portal>\n      <MenubarPrimitive.Content\n        ref={ref}\n        align={align}\n        alignOffset={alignOffset}\n        sideOffset={sideOffset}\n        className={cn(\n          \"z-50 min-w-[12rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-menubar-content-transform-origin]\",\n          className\n        )}\n        {...props}\n      />\n    </MenubarPrimitive.Portal>\n  )\n)\nMenubarContent.displayName = MenubarPrimitive.Content.displayName\n\nconst MenubarItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <MenubarPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarItem.displayName = MenubarPrimitive.Item.displayName\n\nconst MenubarCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <MenubarPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <MenubarPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </MenubarPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </MenubarPrimitive.CheckboxItem>\n))\nMenubarCheckboxItem.displayName = MenubarPrimitive.CheckboxItem.displayName\n\nconst MenubarRadioItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <MenubarPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <MenubarPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </MenubarPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </MenubarPrimitive.RadioItem>\n))\nMenubarRadioItem.displayName = MenubarPrimitive.RadioItem.displayName\n\nconst MenubarLabel = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <MenubarPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarLabel.displayName = MenubarPrimitive.Label.displayName\n\nconst MenubarSeparator = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nMenubarSeparator.displayName = MenubarPrimitive.Separator.displayName\n\nconst MenubarShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\nMenubarShortcut.displayname = \"MenubarShortcut\"\n\nexport {\n  Menubar,\n  MenubarMenu,\n  MenubarTrigger,\n  MenubarContent,\n  MenubarItem,\n  MenubarSeparator,\n  MenubarLabel,\n  MenubarCheckboxItem,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarPortal,\n  MenubarSubContent,\n  MenubarSubTrigger,\n  MenubarGroup,\n  MenubarSub,\n  MenubarShortcut,\n}\n", "size_bytes": 8605}, "client/src/components/ui/navigation-menu.tsx": {"content": "import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst NavigationMenu = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <NavigationMenuPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative z-10 flex max-w-max flex-1 items-center justify-center\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <NavigationMenuViewport />\n  </NavigationMenuPrimitive.Root>\n))\nNavigationMenu.displayName = NavigationMenuPrimitive.Root.displayName\n\nconst NavigationMenuList = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.List\n    ref={ref}\n    className={cn(\n      \"group flex flex-1 list-none items-center justify-center space-x-1\",\n      className\n    )}\n    {...props}\n  />\n))\nNavigationMenuList.displayName = NavigationMenuPrimitive.List.displayName\n\nconst NavigationMenuItem = NavigationMenuPrimitive.Item\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[state=open]:text-accent-foreground data-[state=open]:bg-accent/50 data-[state=open]:hover:bg-accent data-[state=open]:focus:bg-accent\"\n)\n\nconst NavigationMenuTrigger = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <NavigationMenuPrimitive.Trigger\n    ref={ref}\n    className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n    {...props}\n  >\n    {children}{\" \"}\n    <ChevronDown\n      className=\"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180\"\n      aria-hidden=\"true\"\n    />\n  </NavigationMenuPrimitive.Trigger>\n))\nNavigationMenuTrigger.displayName = NavigationMenuPrimitive.Trigger.displayName\n\nconst NavigationMenuContent = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto \",\n      className\n    )}\n    {...props}\n  />\n))\nNavigationMenuContent.displayName = NavigationMenuPrimitive.Content.displayName\n\nconst NavigationMenuLink = NavigationMenuPrimitive.Link\n\nconst NavigationMenuViewport = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Viewport>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Viewport>\n>(({ className, ...props }, ref) => (\n  <div className={cn(\"absolute left-0 top-full flex justify-center\")}>\n    <NavigationMenuPrimitive.Viewport\n      className={cn(\n        \"origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  </div>\n))\nNavigationMenuViewport.displayName =\n  NavigationMenuPrimitive.Viewport.displayName\n\nconst NavigationMenuIndicator = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Indicator>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Indicator>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.Indicator\n    ref={ref}\n    className={cn(\n      \"top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in\",\n      className\n    )}\n    {...props}\n  >\n    <div className=\"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md\" />\n  </NavigationMenuPrimitive.Indicator>\n))\nNavigationMenuIndicator.displayName =\n  NavigationMenuPrimitive.Indicator.displayName\n\nexport {\n  navigationMenuTriggerStyle,\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n}\n", "size_bytes": 5128}, "client/src/components/ui/pagination.tsx": {"content": "import * as React from \"react\"\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { ButtonProps, buttonVariants } from \"@/components/ui/button\"\n\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\n  <nav\n    role=\"navigation\"\n    aria-label=\"pagination\"\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\n    {...props}\n  />\n)\nPagination.displayName = \"Pagination\"\n\nconst PaginationContent = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    className={cn(\"flex flex-row items-center gap-1\", className)}\n    {...props}\n  />\n))\nPaginationContent.displayName = \"PaginationContent\"\n\nconst PaginationItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li ref={ref} className={cn(\"\", className)} {...props} />\n))\nPaginationItem.displayName = \"PaginationItem\"\n\ntype PaginationLinkProps = {\n  isActive?: boolean\n} & Pick<ButtonProps, \"size\"> &\n  React.ComponentProps<\"a\">\n\nconst PaginationLink = ({\n  className,\n  isActive,\n  size = \"icon\",\n  ...props\n}: PaginationLinkProps) => (\n  <a\n    aria-current={isActive ? \"page\" : undefined}\n    className={cn(\n      buttonVariants({\n        variant: isActive ? \"outline\" : \"ghost\",\n        size,\n      }),\n      className\n    )}\n    {...props}\n  />\n)\nPaginationLink.displayName = \"PaginationLink\"\n\nconst PaginationPrevious = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label=\"Go to previous page\"\n    size=\"default\"\n    className={cn(\"gap-1 pl-2.5\", className)}\n    {...props}\n  >\n    <ChevronLeft className=\"h-4 w-4\" />\n    <span>Previous</span>\n  </PaginationLink>\n)\nPaginationPrevious.displayName = \"PaginationPrevious\"\n\nconst PaginationNext = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label=\"Go to next page\"\n    size=\"default\"\n    className={cn(\"gap-1 pr-2.5\", className)}\n    {...props}\n  >\n    <span>Next</span>\n    <ChevronRight className=\"h-4 w-4\" />\n  </PaginationLink>\n)\nPaginationNext.displayName = \"PaginationNext\"\n\nconst PaginationEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    aria-hidden\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"h-4 w-4\" />\n    <span className=\"sr-only\">More pages</span>\n  </span>\n)\nPaginationEllipsis.displayName = \"PaginationEllipsis\"\n\nexport {\n  Pagination,\n  PaginationContent,\n  PaginationEllipsis,\n  PaginationItem,\n  PaginationLink,\n  PaginationNext,\n  PaginationPrevious,\n}\n", "size_bytes": 2751}, "client/src/components/ui/popover.tsx": {"content": "import * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n", "size_bytes": 1280}, "client/src/components/ui/progress.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n", "size_bytes": 791}, "client/src/components/ui/radio-group.tsx": {"content": "import * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Root\n      className={cn(\"grid gap-2\", className)}\n      {...props}\n      ref={ref}\n    />\n  )\n})\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n})\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\n\nexport { RadioGroup, RadioGroupItem }\n", "size_bytes": 1467}, "client/src/components/ui/resizable.tsx": {"content": "\"use client\"\n\nimport { GripVertical } from \"lucide-react\"\nimport * as ResizablePrimitive from \"react-resizable-panels\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ResizablePanelGroup = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof ResizablePrimitive.PanelGroup>) => (\n  <ResizablePrimitive.PanelGroup\n    className={cn(\n      \"flex h-full w-full data-[panel-group-direction=vertical]:flex-col\",\n      className\n    )}\n    {...props}\n  />\n)\n\nconst ResizablePanel = ResizablePrimitive.Panel\n\nconst ResizableHandle = ({\n  withHandle,\n  className,\n  ...props\n}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle> & {\n  withHandle?: boolean\n}) => (\n  <ResizablePrimitive.PanelResizeHandle\n    className={cn(\n      \"relative flex w-px items-center justify-center bg-border after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90\",\n      className\n    )}\n    {...props}\n  >\n    {withHandle && (\n      <div className=\"z-10 flex h-4 w-3 items-center justify-center rounded-sm border bg-border\">\n        <GripVertical className=\"h-2.5 w-2.5\" />\n      </div>\n    )}\n  </ResizablePrimitive.PanelResizeHandle>\n)\n\nexport { ResizablePanelGroup, ResizablePanel, ResizableHandle }\n", "size_bytes": 1723}, "client/src/components/ui/scroll-area.tsx": {"content": "import * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n", "size_bytes": 1642}, "client/src/components/ui/select.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n", "size_bytes": 5742}, "client/src/components/ui/separator.tsx": {"content": "import * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n", "size_bytes": 756}, "client/src/components/ui/sheet.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n", "size_bytes": 4281}, "client/src/components/ui/sidebar.tsx": {"content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { VariantProps, cva } from \"class-variance-authority\"\nimport { PanelLeft } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport {\n  Sheet,\n  She<PERSON><PERSON>ontent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  <PERSON>lt<PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nconst SidebarProvider = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    defaultOpen?: boolean\n    open?: boolean\n    onOpenChange?: (open: boolean) => void\n  }\n>(\n  (\n    {\n      defaultOpen = true,\n      open: openProp,\n      onOpenChange: setOpenProp,\n      className,\n      style,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const isMobile = useIsMobile()\n    const [openMobile, setOpenMobile] = React.useState(false)\n\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = React.useState(defaultOpen)\n    const open = openProp ?? _open\n    const setOpen = React.useCallback(\n      (value: boolean | ((value: boolean) => boolean)) => {\n        const openState = typeof value === \"function\" ? value(open) : value\n        if (setOpenProp) {\n          setOpenProp(openState)\n        } else {\n          _setOpen(openState)\n        }\n\n        // This sets the cookie to keep the sidebar state.\n        document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n      },\n      [setOpenProp, open]\n    )\n\n    // Helper to toggle the sidebar.\n    const toggleSidebar = React.useCallback(() => {\n      return isMobile\n        ? setOpenMobile((open) => !open)\n        : setOpen((open) => !open)\n    }, [isMobile, setOpen, setOpenMobile])\n\n    // Adds a keyboard shortcut to toggle the sidebar.\n    React.useEffect(() => {\n      const handleKeyDown = (event: KeyboardEvent) => {\n        if (\n          event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n          (event.metaKey || event.ctrlKey)\n        ) {\n          event.preventDefault()\n          toggleSidebar()\n        }\n      }\n\n      window.addEventListener(\"keydown\", handleKeyDown)\n      return () => window.removeEventListener(\"keydown\", handleKeyDown)\n    }, [toggleSidebar])\n\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\"\n\n    const contextValue = React.useMemo<SidebarContextProps>(\n      () => ({\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar,\n      }),\n      [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n    )\n\n    return (\n      <SidebarContext.Provider value={contextValue}>\n        <TooltipProvider delayDuration={0}>\n          <div\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH,\n                \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                ...style,\n              } as React.CSSProperties\n            }\n            className={cn(\n              \"group/sidebar-wrapper flex min-h-svh w-full has-[[data-variant=inset]]:bg-sidebar\",\n              className\n            )}\n            ref={ref}\n            {...props}\n          >\n            {children}\n          </div>\n        </TooltipProvider>\n      </SidebarContext.Provider>\n    )\n  }\n)\nSidebarProvider.displayName = \"SidebarProvider\"\n\nconst Sidebar = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    side?: \"left\" | \"right\"\n    variant?: \"sidebar\" | \"floating\" | \"inset\"\n    collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n  }\n>(\n  (\n    {\n      side = \"left\",\n      variant = \"sidebar\",\n      collapsible = \"offcanvas\",\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n    if (collapsible === \"none\") {\n      return (\n        <div\n          className={cn(\n            \"flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground\",\n            className\n          )}\n          ref={ref}\n          {...props}\n        >\n          {children}\n        </div>\n      )\n    }\n\n    if (isMobile) {\n      return (\n        <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n          <SheetContent\n            data-sidebar=\"sidebar\"\n            data-mobile=\"true\"\n            className=\"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden\"\n            style={\n              {\n                \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n              } as React.CSSProperties\n            }\n            side={side}\n          >\n            <SheetHeader className=\"sr-only\">\n              <SheetTitle>Sidebar</SheetTitle>\n              <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n            </SheetHeader>\n            <div className=\"flex h-full w-full flex-col\">{children}</div>\n          </SheetContent>\n        </Sheet>\n      )\n    }\n\n    return (\n      <div\n        ref={ref}\n        className=\"group peer hidden text-sidebar-foreground md:block\"\n        data-state={state}\n        data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n        data-variant={variant}\n        data-side={side}\n      >\n        {/* This is what handles the sidebar gap on desktop */}\n        <div\n          className={cn(\n            \"relative w-[--sidebar-width] bg-transparent transition-[width] duration-200 ease-linear\",\n            \"group-data-[collapsible=offcanvas]:w-0\",\n            \"group-data-[side=right]:rotate-180\",\n            variant === \"floating\" || variant === \"inset\"\n              ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon]\"\n          )}\n        />\n        <div\n          className={cn(\n            \"fixed inset-y-0 z-10 hidden h-svh w-[--sidebar-width] transition-[left,right,width] duration-200 ease-linear md:flex\",\n            side === \"left\"\n              ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n              : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n            // Adjust the padding for floating and inset variants.\n            variant === \"floating\" || variant === \"inset\"\n              ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]\"\n              : \"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n            className\n          )}\n          {...props}\n        >\n          <div\n            data-sidebar=\"sidebar\"\n            className=\"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow\"\n          >\n            {children}\n          </div>\n        </div>\n      </div>\n    )\n  }\n)\nSidebar.displayName = \"Sidebar\"\n\nconst SidebarTrigger = React.forwardRef<\n  React.ElementRef<typeof Button>,\n  React.ComponentProps<typeof Button>\n>(({ className, onClick, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      ref={ref}\n      data-sidebar=\"trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeft />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n})\nSidebarTrigger.displayName = \"SidebarTrigger\"\n\nconst SidebarRail = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\">\n>(({ className, ...props }, ref) => {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <button\n      ref={ref}\n      data-sidebar=\"rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex\",\n        \"[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarRail.displayName = \"SidebarRail\"\n\nconst SidebarInset = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"main\">\n>(({ className, ...props }, ref) => {\n  return (\n    <main\n      ref={ref}\n      className={cn(\n        \"relative flex w-full flex-1 flex-col bg-background\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInset.displayName = \"SidebarInset\"\n\nconst SidebarInput = React.forwardRef<\n  React.ElementRef<typeof Input>,\n  React.ComponentProps<typeof Input>\n>(({ className, ...props }, ref) => {\n  return (\n    <Input\n      ref={ref}\n      data-sidebar=\"input\"\n      className={cn(\n        \"h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarInput.displayName = \"SidebarInput\"\n\nconst SidebarHeader = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarHeader.displayName = \"SidebarHeader\"\n\nconst SidebarFooter = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarFooter.displayName = \"SidebarFooter\"\n\nconst SidebarSeparator = React.forwardRef<\n  React.ElementRef<typeof Separator>,\n  React.ComponentProps<typeof Separator>\n>(({ className, ...props }, ref) => {\n  return (\n    <Separator\n      ref={ref}\n      data-sidebar=\"separator\"\n      className={cn(\"mx-2 w-auto bg-sidebar-border\", className)}\n      {...props}\n    />\n  )\n})\nSidebarSeparator.displayName = \"SidebarSeparator\"\n\nconst SidebarContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarContent.displayName = \"SidebarContent\"\n\nconst SidebarGroup = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => {\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n})\nSidebarGroup.displayName = \"SidebarGroup\"\n\nconst SidebarGroupLabel = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupLabel.displayName = \"SidebarGroupLabel\"\n\nconst SidebarGroupAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & { asChild?: boolean }\n>(({ className, asChild = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarGroupAction.displayName = \"SidebarGroupAction\"\n\nconst SidebarGroupContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"group-content\"\n    className={cn(\"w-full text-sm\", className)}\n    {...props}\n  />\n))\nSidebarGroupContent.displayName = \"SidebarGroupContent\"\n\nconst SidebarMenu = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu\"\n    className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n    {...props}\n  />\n))\nSidebarMenu.displayName = \"SidebarMenu\"\n\nconst SidebarMenuItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    data-sidebar=\"menu-item\"\n    className={cn(\"group/menu-item relative\", className)}\n    {...props}\n  />\n))\nSidebarMenuItem.displayName = \"SidebarMenuItem\"\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:!p-0\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst SidebarMenuButton = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    isActive?: boolean\n    tooltip?: string | React.ComponentProps<typeof TooltipContent>\n  } & VariantProps<typeof sidebarMenuButtonVariants>\n>(\n  (\n    {\n      asChild = false,\n      isActive = false,\n      variant = \"default\",\n      size = \"default\",\n      tooltip,\n      className,\n      ...props\n    },\n    ref\n  ) => {\n    const Comp = asChild ? Slot : \"button\"\n    const { isMobile, state } = useSidebar()\n\n    const button = (\n      <Comp\n        ref={ref}\n        data-sidebar=\"menu-button\"\n        data-size={size}\n        data-active={isActive}\n        className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n        {...props}\n      />\n    )\n\n    if (!tooltip) {\n      return button\n    }\n\n    if (typeof tooltip === \"string\") {\n      tooltip = {\n        children: tooltip,\n      }\n    }\n\n    return (\n      <Tooltip>\n        <TooltipTrigger asChild>{button}</TooltipTrigger>\n        <TooltipContent\n          side=\"right\"\n          align=\"center\"\n          hidden={state !== \"collapsed\" || isMobile}\n          {...tooltip}\n        />\n      </Tooltip>\n    )\n  }\n)\nSidebarMenuButton.displayName = \"SidebarMenuButton\"\n\nconst SidebarMenuAction = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<\"button\"> & {\n    asChild?: boolean\n    showOnHover?: boolean\n  }\n>(({ className, asChild = false, showOnHover = false, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 after:md:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuAction.displayName = \"SidebarMenuAction\"\n\nconst SidebarMenuBadge = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\">\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    data-sidebar=\"menu-badge\"\n    className={cn(\n      \"pointer-events-none absolute right-1 flex h-5 min-w-5 select-none items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground\",\n      \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n      \"peer-data-[size=sm]/menu-button:top-1\",\n      \"peer-data-[size=default]/menu-button:top-1.5\",\n      \"peer-data-[size=lg]/menu-button:top-2.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuBadge.displayName = \"SidebarMenuBadge\"\n\nconst SidebarMenuSkeleton = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    showIcon?: boolean\n  }\n>(({ className, showIcon = false, ...props }, ref) => {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      ref={ref}\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-[--skeleton-width] flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n})\nSidebarMenuSkeleton.displayName = \"SidebarMenuSkeleton\"\n\nconst SidebarMenuSub = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    data-sidebar=\"menu-sub\"\n    className={cn(\n      \"mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5\",\n      \"group-data-[collapsible=icon]:hidden\",\n      className\n    )}\n    {...props}\n  />\n))\nSidebarMenuSub.displayName = \"SidebarMenuSub\"\n\nconst SidebarMenuSubItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ ...props }, ref) => <li ref={ref} {...props} />)\nSidebarMenuSubItem.displayName = \"SidebarMenuSubItem\"\n\nconst SidebarMenuSubButton = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentProps<\"a\"> & {\n    asChild?: boolean\n    size?: \"sm\" | \"md\"\n    isActive?: boolean\n  }\n>(({ asChild = false, size = \"md\", isActive, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nSidebarMenuSubButton.displayName = \"SidebarMenuSubButton\"\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n", "size_bytes": 23567}, "client/src/components/ui/skeleton.tsx": {"content": "import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "size_bytes": 261}, "client/src/components/ui/slider.tsx": {"content": "import * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n", "size_bytes": 1077}, "client/src/components/ui/switch.tsx": {"content": "import * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n", "size_bytes": 1139}, "client/src/components/ui/table.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "size_bytes": 2765}, "client/src/components/ui/tabs.tsx": {"content": "import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n", "size_bytes": 1883}, "client/src/components/ui/textarea.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<\"textarea\">\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n", "size_bytes": 689}, "client/src/components/ui/toast.tsx": {"content": "import * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n", "size_bytes": 4845}, "client/src/components/ui/toaster.tsx": {"content": "import { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n", "size_bytes": 772}, "client/src/components/ui/toggle-group.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as ToggleGroupPrimitive from \"@radix-ui/react-toggle-group\"\nimport { type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\nimport { toggleVariants } from \"@/components/ui/toggle\"\n\nconst ToggleGroupContext = React.createContext<\n  VariantProps<typeof toggleVariants>\n>({\n  size: \"default\",\n  variant: \"default\",\n})\n\nconst ToggleGroup = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Root> &\n    VariantProps<typeof toggleVariants>\n>(({ className, variant, size, children, ...props }, ref) => (\n  <ToggleGroupPrimitive.Root\n    ref={ref}\n    className={cn(\"flex items-center justify-center gap-1\", className)}\n    {...props}\n  >\n    <ToggleGroupContext.Provider value={{ variant, size }}>\n      {children}\n    </ToggleGroupContext.Provider>\n  </ToggleGroupPrimitive.Root>\n))\n\nToggleGroup.displayName = ToggleGroupPrimitive.Root.displayName\n\nconst ToggleGroupItem = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item> &\n    VariantProps<typeof toggleVariants>\n>(({ className, children, variant, size, ...props }, ref) => {\n  const context = React.useContext(ToggleGroupContext)\n\n  return (\n    <ToggleGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        toggleVariants({\n          variant: context.variant || variant,\n          size: context.size || size,\n        }),\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </ToggleGroupPrimitive.Item>\n  )\n})\n\nToggleGroupItem.displayName = ToggleGroupPrimitive.Item.displayName\n\nexport { ToggleGroup, ToggleGroupItem }\n", "size_bytes": 1753}, "client/src/components/ui/toggle.tsx": {"content": "import * as React from \"react\"\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst toggleVariants = cva(\n  \"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 gap-2\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-transparent\",\n        outline:\n          \"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n      },\n      size: {\n        default: \"h-10 px-3 min-w-10\",\n        sm: \"h-9 px-2.5 min-w-9\",\n        lg: \"h-11 px-5 min-w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst Toggle = React.forwardRef<\n  React.ElementRef<typeof TogglePrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &\n    VariantProps<typeof toggleVariants>\n>(({ className, variant, size, ...props }, ref) => (\n  <TogglePrimitive.Root\n    ref={ref}\n    className={cn(toggleVariants({ variant, size, className }))}\n    {...props}\n  />\n))\n\nToggle.displayName = TogglePrimitive.Root.displayName\n\nexport { Toggle, toggleVariants }\n", "size_bytes": 1527}, "client/src/components/ui/tooltip.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n", "size_bytes": 1209}, "replit.md": {"content": "# AI Fitness Assessment Platform\n\n## Overview\n\nThis is a FaceTime-style AI fitness assessment platform that conducts personalized initial fitness evaluations through real-time video calls. The system combines voice analysis, pose detection, and conversational AI to assess user fitness levels and generate tailored workout plans. Users interact with a holographic AI trainer avatar that guides them through discovery questions, movement assessments, and provides personalized recommendations with confidence scores.\n\n## User Preferences\n\nPreferred communication style: Simple, everyday language.\n\n## System Architecture\n\n### Frontend Architecture\n- **Framework**: React with TypeScript using Vite for build tooling\n- **UI Library**: shadcn/ui components built on Radix UI primitives\n- **Styling**: Tailwind CSS with custom design tokens for dark theme\n- **State Management**: Zustand for assessment state, React Query for server state\n- **Routing**: Wouter for lightweight client-side routing\n- **3D Graphics**: Three.js for the holographic AI trainer orb avatar with WebGL shaders\n\n### Backend Architecture\n- **Runtime**: Node.js with Express.js server\n- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations\n- **WebSocket**: Real-time communication for video call functionality and live data streaming\n- **API Design**: RESTful endpoints with structured error handling and request logging\n\n### Real-time Communication\n- **Video/Audio**: WebRTC for peer-to-peer video calling with STUN servers\n- **Speech Processing**: Web Speech API for real-time speech-to-text transcription\n- **Pose Detection**: MediaPipe integration for movement analysis and form assessment\n- **Audio Analysis**: Web Audio API for voice prosody and RMS analysis to drive avatar animations\n\n### AI Integration\n- **LLM**: OpenAI GPT-5 for intelligent conversation flow and workout plan generation\n- **Assessment Logic**: Custom scoring algorithm calculating Time Capacity, Movement Quality, Recovery Readiness, Environmental Fit, and Adherence Signals to produce Goal Attainability Score (GAS)\n- **Avatar Animation**: Audio-driven holographic orb with state-based color palettes and particle effects\n\n### Data Model\n- **Users**: Authentication and subscription management with Stripe integration\n- **Assessment Sessions**: Multi-phase evaluation tracking (welcome → discovery → movement → photo → reveal)\n- **Session Events**: Granular event logging for speech, movement, and phase transitions\n- **Workout Plans**: Generated fitness programs with coaching tier options\n\n### Database Schema\n- Utilizes PostgreSQL with UUID primary keys and JSONB for flexible data storage\n- Tracks user progression through assessment phases with intake data and biometric signals\n- Stores workout plans with coaching options and Stripe subscription details\n\n### Payment Integration\n- Stripe integration for subscription management and payment processing\n- Support for coached vs. self-guided workout plan tiers\n- Customer and subscription ID tracking linked to user accounts\n\n## External Dependencies\n\n### Core Services\n- **Neon Database**: PostgreSQL hosting with serverless connection pooling\n- **OpenAI API**: GPT-5 for conversational AI and workout plan generation\n- **Stripe**: Payment processing and subscription management\n\n### Media Processing\n- **MediaPipe Pose**: Real-time pose estimation and movement analysis\n- **Web Speech API**: Browser-native speech recognition\n- **WebRTC**: Peer-to-peer video communication\n\n### Development Tools\n- **Replit Platform**: Development environment with cartographer and dev banner plugins\n- **Drizzle Kit**: Database migrations and schema management\n- **ESBuild**: Production bundling for server-side code\n\n### UI/UX Libraries\n- **Radix UI**: Accessible component primitives\n- **Three.js**: 3D graphics rendering for avatar\n- **Lucide React**: Icon library\n- **React Hook Form**: Form validation with Zod schemas", "size_bytes": 3942}}, "version": 1}