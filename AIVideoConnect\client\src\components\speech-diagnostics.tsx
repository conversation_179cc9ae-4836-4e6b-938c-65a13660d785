import { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface DiagnosticResult {
  test: string;
  status: 'pass' | 'fail' | 'warning';
  message: string;
  details?: any;
}

export default function SpeechDiagnostics() {
  const [results, setResults] = useState<DiagnosticResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const addResult = useCallback((result: DiagnosticResult) => {
    setResults(prev => [...prev, result]);
  }, []);

  const runDiagnostics = useCallback(async () => {
    setIsRunning(true);
    setResults([]);

    // Test 1: Browser Support
    addResult({
      test: 'Browser Speech API Support',
      status: ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) ? 'pass' : 'fail',
      message: ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) 
        ? 'Browser supports Speech Recognition API' 
        : 'Browser does not support Speech Recognition API',
      details: {
        userAgent: navigator.userAgent,
        webkitSpeechRecognition: 'webkitSpeechRecognition' in window,
        SpeechRecognition: 'SpeechRecognition' in window
      }
    });

    // Test 2: MediaDevices Support
    addResult({
      test: 'MediaDevices Support',
      status: (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) ? 'pass' : 'fail',
      message: (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) 
        ? 'MediaDevices API is available' 
        : 'MediaDevices API is not available',
      details: {
        mediaDevices: !!navigator.mediaDevices,
        getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
      }
    });

    // Test 3: Microphone Permission
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      addResult({
        test: 'Microphone Permission',
        status: 'pass',
        message: 'Microphone permission granted',
        details: {
          audioTracks: stream.getAudioTracks().length,
          tracks: stream.getAudioTracks().map(track => ({
            label: track.label,
            enabled: track.enabled,
            muted: track.muted,
            readyState: track.readyState,
            settings: track.getSettings()
          }))
        }
      });

      // Test 4: Audio Level Detection
      try {
        const audioContext = new AudioContext();
        const analyser = audioContext.createAnalyser();
        const source = audioContext.createMediaStreamSource(stream);
        source.connect(analyser);

        analyser.fftSize = 256;
        const dataArray = new Uint8Array(analyser.frequencyBinCount);

        // Monitor audio levels for 3 seconds
        let maxLevel = 0;
        let samples = 0;
        const checkAudio = () => {
          analyser.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
          maxLevel = Math.max(maxLevel, average);
          samples++;
          setAudioLevel(average);

          if (samples < 30) { // 3 seconds at ~10fps
            setTimeout(checkAudio, 100);
          } else {
            addResult({
              test: 'Audio Input Level',
              status: maxLevel > 10 ? 'pass' : maxLevel > 0 ? 'warning' : 'fail',
              message: maxLevel > 10 
                ? `Good audio levels detected (max: ${Math.round(maxLevel)})` 
                : maxLevel > 0 
                ? `Low audio levels detected (max: ${Math.round(maxLevel)}) - try speaking louder`
                : 'No audio input detected - check microphone',
              details: {
                maxLevel: Math.round(maxLevel),
                samples: samples
              }
            });

            // Cleanup
            source.disconnect();
            audioContext.close();
            stream.getTracks().forEach(track => track.stop());
            setAudioLevel(0);
          }
        };
        checkAudio();

      } catch (audioError) {
        addResult({
          test: 'Audio Level Detection',
          status: 'warning',
          message: 'Could not test audio levels: ' + audioError.message,
          details: { error: audioError.message }
        });
        stream.getTracks().forEach(track => track.stop());
      }

    } catch (permissionError: any) {
      addResult({
        test: 'Microphone Permission',
        status: 'fail',
        message: 'Microphone permission denied: ' + permissionError.message,
        details: {
          error: permissionError.name,
          message: permissionError.message
        }
      });
    }

    // Test 5: MediaRecorder Support
    const supportedTypes = [
      'audio/wav',
      'audio/mp4',
      'audio/webm',
      'audio/ogg',
      'audio/webm;codecs=opus'
    ];

    const supportedFormats = supportedTypes.filter(type => MediaRecorder.isTypeSupported(type));
    
    addResult({
      test: 'MediaRecorder Support',
      status: supportedFormats.length > 0 ? 'pass' : 'fail',
      message: supportedFormats.length > 0 
        ? `MediaRecorder supports ${supportedFormats.length} audio formats`
        : 'MediaRecorder does not support any audio formats',
      details: {
        supportedFormats,
        allFormats: supportedTypes.map(type => ({
          type,
          supported: MediaRecorder.isTypeSupported(type)
        }))
      }
    });

    // Test 6: Network Connectivity
    try {
      const response = await fetch('/api/health', { method: 'GET' });
      addResult({
        test: 'Server Connectivity',
        status: response.ok ? 'pass' : 'warning',
        message: response.ok 
          ? 'Server is reachable'
          : `Server responded with status ${response.status}`,
        details: {
          status: response.status,
          statusText: response.statusText
        }
      });
    } catch (networkError: any) {
      addResult({
        test: 'Server Connectivity',
        status: 'fail',
        message: 'Cannot reach server: ' + networkError.message,
        details: { error: networkError.message }
      });
    }

    setIsRunning(false);
  }, [addResult]);

  const getStatusColor = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass': return 'bg-green-100 text-green-800 border-green-200';
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'fail': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>🔍 Speech Recognition Diagnostics</span>
          <Button onClick={runDiagnostics} disabled={isRunning}>
            {isRunning ? '🔄 Running...' : '▶️ Run Diagnostics'}
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {audioLevel > 0 && (
          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="text-sm font-medium text-blue-800 mb-2">
              🎤 Audio Level Monitor (speak now!)
            </div>
            <div className="w-full bg-blue-200 rounded-full h-4">
              <div 
                className="bg-blue-600 h-4 rounded-full transition-all duration-100"
                style={{ width: `${Math.min(audioLevel * 2, 100)}%` }}
              />
            </div>
            <div className="text-xs text-blue-600 mt-1">
              Level: {Math.round(audioLevel)} (speak louder if below 20)
            </div>
          </div>
        )}

        {results.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-medium">Diagnostic Results:</h3>
            {results.map((result, index) => (
              <div key={index} className={`p-4 rounded-lg border ${getStatusColor(result.status)}`}>
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{result.test}</span>
                  <Badge variant={result.status === 'pass' ? 'default' : result.status === 'warning' ? 'secondary' : 'destructive'}>
                    {result.status.toUpperCase()}
                  </Badge>
                </div>
                <div className="text-sm mb-2">{result.message}</div>
                {result.details && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-gray-600">Technical Details</summary>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}

        {results.length === 0 && !isRunning && (
          <div className="text-center text-gray-500 py-8">
            Click "Run Diagnostics" to test your speech recognition setup
          </div>
        )}
      </CardContent>
    </Card>
  );
}
