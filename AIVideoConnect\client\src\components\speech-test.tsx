import { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';

export default function SpeechTest() {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [attempts, setAttempts] = useState(0);
  const [manualInput, setManualInput] = useState('');

  const testBasicSpeechRecognition = useCallback(async () => {
    console.log('🧪 Testing basic speech recognition...');
    setAttempts(prev => prev + 1);
    
    // Check browser support
    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      setError('Speech recognition not supported in this browser');
      return;
    }

    try {
      // Request microphone permission
      console.log('🎤 Requesting microphone permission...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log('✅ Microphone permission granted');
      stream.getTracks().forEach(track => track.stop());

      // Create speech recognition
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      // Try different configurations based on attempt number
      if (attempts <= 2) {
        recognition.continuous = false;
        recognition.interimResults = true;
      } else if (attempts <= 4) {
        recognition.continuous = true;
        recognition.interimResults = false;
      } else {
        recognition.continuous = true;
        recognition.interimResults = true;
      }

      recognition.lang = 'en-US';
      recognition.maxAlternatives = 3; // Try more alternatives
      
      console.log('🎤 Starting speech recognition...');
      setIsListening(true);
      setError(null);
      setTranscript('');

      recognition.onstart = () => {
        console.log('✅ Speech recognition started');
      };

      recognition.onresult = (event: any) => {
        console.log('🎵 Speech result received:', event.results.length);
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = 0; i < event.results.length; i++) {
          const result = event.results[i];
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
            console.log('✅ Final transcript:', result[0].transcript);
          } else {
            interimTranscript += result[0].transcript;
            console.log('⏳ Interim transcript:', result[0].transcript);
          }
        }

        setTranscript(finalTranscript || interimTranscript);
      };

      recognition.onerror = (event: any) => {
        console.error('❌ Speech recognition error:', event.error);

        if (event.error === 'no-speech') {
          console.log(`⚠️ No speech detected (attempt ${attempts}), auto-restarting in 1 second...`);
          setError(`No speech detected (attempt ${attempts}). SPEAK LOUDER! Auto-restarting...`);

          // Auto-restart with shorter delay and different config
          setTimeout(() => {
            if (!isListening) {
              console.log('🔄 Auto-restarting speech recognition with new config...');
              testBasicSpeechRecognition();
            }
          }, 1000);
        } else {
          setError(`Speech recognition error: ${event.error}`);
          setIsListening(false);
        }
      };

      recognition.onend = () => {
        console.log('🛑 Speech recognition ended');
        setIsListening(false);
      };

      recognition.start();

    } catch (err) {
      console.error('❌ Error:', err);
      setError(`Error: ${err.message}`);
      setIsListening(false);
    }
  }, []);

  const stopListening = useCallback(() => {
    console.log('🛑 Stopping speech recognition...');
    setIsListening(false);
  }, []);

  const testMicrophone = useCallback(async () => {
    try {
      console.log('🎤 Testing microphone access...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // Test audio levels
      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);
      microphone.connect(analyser);

      const dataArray = new Uint8Array(analyser.frequencyBinCount);

      const checkLevel = () => {
        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
        console.log('🎵 Microphone level:', Math.round(average));

        if (average > 10) {
          console.log('✅ Microphone is picking up sound!');
        } else {
          console.log('⚠️ Very low microphone level - try speaking louder');
        }
      };

      // Check levels for 3 seconds
      const interval = setInterval(checkLevel, 500);
      setTimeout(() => {
        clearInterval(interval);
        stream.getTracks().forEach(track => track.stop());
        audioContext.close();
        console.log('🛑 Microphone test completed');
      }, 3000);

    } catch (err) {
      console.error('❌ Microphone test failed:', err);
    }
  }, []);

  const sendManualText = useCallback(() => {
    if (manualInput.trim()) {
      console.log('📝 Manual text input:', manualInput);
      setTranscript(manualInput);
      setManualInput('');
      setError(null);
    }
  }, [manualInput]);

  const resetTest = useCallback(() => {
    setAttempts(0);
    setTranscript('');
    setError(null);
    setIsListening(false);
    console.log('🔄 Test reset');
  }, []);

  return (
    <div className="p-6 max-w-md mx-auto bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-bold mb-2">
        Speech Recognition Test
        {attempts > 0 && <span className="text-sm text-gray-500 ml-2">(Attempt #{attempts})</span>}
      </h2>

      <div className="text-xs text-gray-500 mb-4 space-y-1">
        <div>🌐 Browser: {navigator.userAgent.includes('Chrome') ? 'Chrome' : navigator.userAgent.includes('Firefox') ? 'Firefox' : 'Other'}</div>
        <div>🎤 Speech API: {('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) ? 'Available' : 'Not Available'}</div>
        <div>📱 Platform: {navigator.platform}</div>
      </div>
      
      <div className="space-y-4">
        <div className="space-y-2">
          <Button
            onClick={isListening ? stopListening : testBasicSpeechRecognition}
            variant={isListening ? "destructive" : "default"}
            className="w-full"
          >
            {isListening ? "🔴 Stop Listening" : "🎤 Start Listening"}
          </Button>

          <div className="grid grid-cols-2 gap-2">
            <Button
              onClick={testMicrophone}
              variant="outline"
              className="text-sm"
              disabled={isListening}
            >
              🔊 Test Mic
            </Button>
            <Button
              onClick={resetTest}
              variant="outline"
              className="text-sm"
            >
              🔄 Reset
            </Button>
          </div>
        </div>

        {/* Manual Text Input Fallback */}
        <div className="border-t pt-4">
          <div className="text-sm font-medium mb-2">🔧 Manual Text Input (Fallback)</div>
          <div className="flex space-x-2">
            <input
              type="text"
              value={manualInput}
              onChange={(e) => setManualInput(e.target.value)}
              placeholder="Type your message here..."
              className="flex-1 px-3 py-2 border rounded text-sm"
              onKeyPress={(e) => e.key === 'Enter' && sendManualText()}
            />
            <Button
              onClick={sendManualText}
              variant="outline"
              className="text-sm"
              disabled={!manualInput.trim()}
            >
              Send
            </Button>
          </div>
          <div className="text-xs text-gray-500 mt-1">
            Use this if speech recognition isn't working
          </div>
        </div>

        {isListening && (
          <div className="space-y-2">
            <div className="text-green-600 flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span>Listening... Speak now!</span>
            </div>
            <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded space-y-1">
              <div>💡 <strong>Speak clearly and loudly:</strong></div>
              <div>• "Hello, can you hear me?"</div>
              <div>• "Testing one two three"</div>
              <div>• "This is a speech test"</div>
              <div className="text-xs text-orange-600 mt-1">
                ⚠️ If you see "no-speech" errors, speak louder or closer to your microphone
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="text-red-600 text-sm p-2 bg-red-50 rounded">
            {error}
          </div>
        )}

        {transcript && (
          <div className="p-3 bg-blue-50 rounded">
            <div className="text-sm text-gray-600 mb-1">You said:</div>
            <div className="font-medium">{transcript}</div>
          </div>
        )}
      </div>
    </div>
  );
}
