import { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useSpeech } from '@/hooks/use-speech';
import { useWebSocket } from '@/hooks/use-websocket';
import SpeechDiagnostics from './speech-diagnostics';

// Declare global speech recognition types
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

interface TestResult {
  method: string;
  transcript: string;
  confidence: number;
  duration: number;
  error?: string;
}

export default function SpeechTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isTestingBrowser, setIsTestingBrowser] = useState(false);
  const [browserTranscript, setBrowserTranscript] = useState('');
  const [browserError, setBrowserError] = useState<string | null>(null);
  const [attempts, setAttempts] = useState(0);
  const [showDiagnostics, setShowDiagnostics] = useState(false);
  const startTimeRef = useRef<number>(0);

  const websocket = useWebSocket();
  const {
    isListening: whisperListening,
    transcript: whisperTranscript,
    error: whisperError,
    confidence: whisperConfidence,
    isProcessing: whisperProcessing,
    startListening: startWhisperListening,
    stopListening: stopWhisperListening
  } = useSpeech(websocket, { useWhisper: true, recordingDuration: 3000 });

  const testBrowserSpeechRecognition = useCallback(async () => {
    if (isTestingBrowser) {
      setIsTestingBrowser(false);
      return;
    }

    console.log('🧪 Testing browser speech recognition...');
    setAttempts(prev => prev + 1);

    // Check browser support
    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      setBrowserError('Speech recognition not supported in this browser');
      return;
    }

    try {
      setBrowserError(null);
      setBrowserTranscript('');
      startTimeRef.current = Date.now();

      // Request microphone permission
      console.log('🎤 Requesting microphone permission...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      console.log('✅ Microphone permission granted');
      stream.getTracks().forEach(track => track.stop());

      // Create speech recognition
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();

      recognition.continuous = false;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 3;

      setIsTestingBrowser(true);

      recognition.onstart = () => {
        console.log('✅ Browser speech recognition started');
      };

      recognition.onresult = (event: any) => {
        console.log('🎵 Browser speech result received:', event.results.length);
        let finalTranscript = '';
        let interimTranscript = '';
        let confidence = 0;

        for (let i = 0; i < event.results.length; i++) {
          const result = event.results[i];
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
            confidence = result[0].confidence || 0.8;
            console.log('✅ Final transcript:', result[0].transcript, 'confidence:', confidence);
          } else {
            interimTranscript += result[0].transcript;
            console.log('⏳ Interim transcript:', result[0].transcript);
          }
        }

        setBrowserTranscript(finalTranscript || interimTranscript);

        if (finalTranscript) {
          const duration = Date.now() - startTimeRef.current;
          setTestResults(prev => [...prev, {
            method: 'Browser API',
            transcript: finalTranscript,
            confidence,
            duration,
          }]);
          setIsTestingBrowser(false);
        }
      };

      recognition.onerror = (event: any) => {
        console.error('❌ Browser speech recognition error:', event.error);
        const duration = Date.now() - startTimeRef.current;

        setTestResults(prev => [...prev, {
          method: 'Browser API',
          transcript: '',
          confidence: 0,
          duration,
          error: event.error
        }]);

        setBrowserError(`Speech recognition error: ${event.error}`);
        setIsTestingBrowser(false);
      };

      recognition.onend = () => {
        console.log('🛑 Browser speech recognition ended');
        setIsTestingBrowser(false);
      };

      recognition.start();

    } catch (err: any) {
      console.error('❌ Browser speech recognition error:', err);
      setBrowserError(`Error: ${err.message}`);
      setIsTestingBrowser(false);
    }
  }, [attempts]);

  const testWhisperRecognition = useCallback(async () => {
    if (whisperListening) {
      stopWhisperListening();
      return;
    }

    console.log('🧪 Testing Whisper speech recognition...');
    startTimeRef.current = Date.now();

    // Connect WebSocket if not connected
    if (!websocket.isConnected) {
      websocket.connect('test-session');
      // Wait a bit for connection
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    startWhisperListening();
  }, [whisperListening, startWhisperListening, stopWhisperListening, websocket]);

  const clearResults = useCallback(() => {
    setTestResults([]);
    setBrowserTranscript('');
    setBrowserError(null);
  }, []);
  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <span>🎤 Speech Recognition Test</span>
          <Badge variant="outline">
            {attempts > 0 ? `Attempt #${attempts}` : 'Ready'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* System Info */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div>🌐 Browser: {navigator.userAgent.includes('Chrome') ? 'Chrome' : navigator.userAgent.includes('Firefox') ? 'Firefox' : 'Other'}</div>
            <div>🎤 Browser API: {('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) ? '✅ Available' : '❌ Not Available'}</div>
          </div>
          <div className="space-y-1">
            <div>🔌 WebSocket: {websocket.isConnected ? '✅ Connected' : '❌ Disconnected'}</div>
            <div>📱 Platform: {navigator.platform}</div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <h3 className="font-medium">Browser Speech API</h3>
            <Button
              onClick={testBrowserSpeechRecognition}
              variant={isTestingBrowser ? "destructive" : "default"}
              className="w-full"
              disabled={!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)}
            >
              {isTestingBrowser ? "🔴 Stop Browser Test" : "🎤 Test Browser API"}
            </Button>
            {browserError && (
              <div className="text-red-600 text-xs p-2 bg-red-50 rounded">
                {browserError}
              </div>
            )}
            {browserTranscript && (
              <div className="text-green-600 text-xs p-2 bg-green-50 rounded">
                "{browserTranscript}"
              </div>
            )}
          </div>

          <div className="space-y-2">
            <h3 className="font-medium">Whisper API</h3>
            <Button
              onClick={testWhisperRecognition}
              variant={whisperListening ? "destructive" : "default"}
              className="w-full"
              disabled={!websocket.isConnected}
            >
              {whisperListening ? "🔴 Stop Whisper Test" : "🎙️ Test Whisper API"}
            </Button>
            {whisperProcessing && (
              <div className="text-blue-600 text-xs p-2 bg-blue-50 rounded">
                Processing audio...
              </div>
            )}
            {whisperError && (
              <div className="text-red-600 text-xs p-2 bg-red-50 rounded">
                {whisperError}
              </div>
            )}
            {whisperTranscript && (
              <div className="text-green-600 text-xs p-2 bg-green-50 rounded">
                "{whisperTranscript}" (confidence: {Math.round(whisperConfidence * 100)}%)
              </div>
            )}
          </div>
        </div>

        {/* Test Instructions */}
        {(isTestingBrowser || whisperListening) && (
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="font-medium text-blue-900 mb-2">🎯 Test Instructions</div>
            <div className="text-sm text-blue-800 space-y-1">
              <div>• Speak clearly and loudly</div>
              <div>• Try: "Hello, can you hear me?"</div>
              <div>• Try: "Testing one two three"</div>
              <div>• Try: "This is a speech recognition test"</div>
            </div>
          </div>
        )}

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Test Results</h3>
              <Button onClick={clearResults} variant="outline" size="sm">
                Clear Results
              </Button>
            </div>
            <div className="space-y-2 max-h-40 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between mb-1">
                    <Badge variant={result.error ? "destructive" : "default"}>
                      {result.method}
                    </Badge>
                    <span className="text-xs text-gray-500">
                      {result.duration}ms
                    </span>
                  </div>
                  {result.error ? (
                    <div className="text-red-600 text-sm">❌ {result.error}</div>
                  ) : (
                    <div className="space-y-1">
                      <div className="text-sm">"{result.transcript}"</div>
                      <div className="text-xs text-gray-500">
                        Confidence: {Math.round(result.confidence * 100)}%
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Utility Controls */}
        <div className="flex space-x-2">
          <Button onClick={clearResults} variant="outline" size="sm">
            🔄 Clear Results
          </Button>
          <Button
            onClick={() => websocket.isConnected ? websocket.disconnect() : websocket.connect('test-session')}
            variant="outline"
            size="sm"
          >
            {websocket.isConnected ? '🔌 Disconnect WS' : '🔌 Connect WS'}
          </Button>
          <Button
            onClick={() => setShowDiagnostics(!showDiagnostics)}
            variant="outline"
            size="sm"
          >
            {showDiagnostics ? '🔍 Hide Diagnostics' : '🔍 Run Diagnostics'}
          </Button>
        </div>

        {/* Diagnostics Panel */}
        {showDiagnostics && (
          <div className="mt-6">
            <SpeechDiagnostics />
          </div>
        )}
      </CardContent>
    </Card>
  );
}
