imports:
  root: ../../../__package__.yml
service:
  auth: false
  base-path: ''
  endpoints:
    get:
      path: /v1/voices/pvc/{voice_id}/samples/{sample_id}/waveform
      method: GET
      auth: false
      docs: Retrieve the visual waveform of a voice sample.
      source:
        openapi: openapi.json
      path-parameters:
        voice_id:
          type: string
          docs: >-
            Voice ID to be used, you can use https://api.elevenlabs.io/v1/voices
            to list all the available voices.
        sample_id:
          type: string
          docs: Sample ID to be used
      display-name: Retrieve Voice Sample Visual Waveform
      response:
        docs: Successful Response
        type: root.VoiceSampleVisualWaveformResponseModel
        status-code: 200
      errors:
        - root.UnprocessableEntityError
      examples:
        - path-parameters:
            voice_id: 21m00Tcm4TlvDq8ikWAM
            sample_id: VW7YKqPnjY4h39yTbx2L
          response:
            body:
              sample_id: DCwhRBWXzGAHq8TQ4Fs18
              visual_waveform:
                - 0.1
                - 0.2
                - 0.3
                - 0.4
                - 0.5
  source:
    openapi: openapi.json
