/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type BodyInviteUserV1WorkspaceInvitesAddPostWorkspacePermission = "external" | "admin" | "workspace_admin" | "workspace_member" | "support_l1" | "support_l2" | "moderator" | "sales" | "voice_mixer" | "voice_admin" | "convai_admin" | "enterprise_viewer" | "quality_check_admin" | "workspace_migration_admin" | "human_reviewer" | "productions_admin";
export declare const BodyInviteUserV1WorkspaceInvitesAddPostWorkspacePermission: {
    readonly External: "external";
    readonly Admin: "admin";
    readonly WorkspaceAdmin: "workspace_admin";
    readonly WorkspaceMember: "workspace_member";
    readonly SupportL1: "support_l1";
    readonly SupportL2: "support_l2";
    readonly Moderator: "moderator";
    readonly Sales: "sales";
    readonly VoiceMixer: "voice_mixer";
    readonly VoiceAdmin: "voice_admin";
    readonly ConvaiAdmin: "convai_admin";
    readonly EnterpriseViewer: "enterprise_viewer";
    readonly QualityCheckAdmin: "quality_check_admin";
    readonly WorkspaceMigrationAdmin: "workspace_migration_admin";
    readonly HumanReviewer: "human_reviewer";
    readonly ProductionsAdmin: "productions_admin";
};
