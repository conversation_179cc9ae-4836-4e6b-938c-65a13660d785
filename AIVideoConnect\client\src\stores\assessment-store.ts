import { create } from 'zustand';

interface AssessmentState {
  sessionId: string | null;
  phase: string;
  transcript: string;
  currentPrompt: string;
  repCount: number;
  currentExercise: string;
  scores: any;
  plan: any;
  
  setSessionId: (id: string) => void;
  setPhase: (phase: string) => void;
  appendTranscript: (text: string) => void;
  setCurrentPrompt: (prompt: string) => void;
  incrementRepCount: () => void;
  resetRepCount: () => void;
  setCurrentExercise: (exercise: string) => void;
  setScores: (scores: any) => void;
  setPlan: (plan: any) => void;
  reset: () => void;
}

export const useAssessmentStore = create<AssessmentState>((set) => ({
  sessionId: null,
  phase: 'welcome',
  transcript: '',
  currentPrompt: "What's the one change you want to see in the next 8-12 weeks?",
  repCount: 0,
  currentExercise: 'Air Squat x5',
  scores: null,
  plan: null,
  
  setSessionId: (id) => set({ sessionId: id }),
  setPhase: (phase) => set({ phase }),
  appendTranscript: (text) => set((state) => ({ 
    transcript: state.transcript + (state.transcript ? '\n' : '') + text 
  })),
  setCurrentPrompt: (prompt) => set({ currentPrompt: prompt }),
  incrementRepCount: () => set((state) => ({ repCount: state.repCount + 1 })),
  resetRepCount: () => set({ repCount: 0 }),
  setCurrentExercise: (exercise) => set({ currentExercise: exercise }),
  setScores: (scores) => set({ scores }),
  setPlan: (plan) => set({ plan }),
  reset: () => set({
    sessionId: null,
    phase: 'welcome',
    transcript: '',
    currentPrompt: "What's the one change you want to see in the next 8-12 weeks?",
    repCount: 0,
    currentExercise: 'Air Squat x5',
    scores: null,
    plan: null,
  }),
}));
