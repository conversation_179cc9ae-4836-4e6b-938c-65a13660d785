import { users, assessmentSessions, workoutPlans, sessionEvents, type User, type InsertUser, type AssessmentSession, type InsertAssessmentSession, type WorkoutPlan, type InsertWorkoutPlan, type SessionEvent } from "@shared/schema";
import { db } from "./db";
import { eq, desc } from "drizzle-orm";

export interface IStorage {
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  updateUserStripeInfo(userId: string, stripeCustomerId: string, stripeSubscriptionId: string): Promise<User>;
  
  createAssessmentSession(session: InsertAssessmentSession): Promise<AssessmentSession>;
  getAssessmentSession(id: string): Promise<AssessmentSession | undefined>;
  updateAssessmentSession(id: string, updates: Partial<AssessmentSession>): Promise<AssessmentSession>;
  
  createWorkoutPlan(plan: InsertWorkoutPlan): Promise<WorkoutPlan>;
  getUserWorkoutPlans(userId: string): Promise<WorkoutPlan[]>;
  
  createSessionEvent(event: { sessionId: string; eventType: string; eventData?: any }): Promise<SessionEvent>;
  getSessionEvents(sessionId: string): Promise<SessionEvent[]>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async updateUserStripeInfo(userId: string, stripeCustomerId: string, stripeSubscriptionId: string): Promise<User> {
    const [user] = await db
      .update(users)
      .set({ stripeCustomerId, stripeSubscriptionId })
      .where(eq(users.id, userId))
      .returning();
    return user;
  }

  async createAssessmentSession(session: InsertAssessmentSession): Promise<AssessmentSession> {
    const [newSession] = await db
      .insert(assessmentSessions)
      .values({
        ...session,
        intake: session.intake ? JSON.stringify(session.intake) : null,
        signals: session.signals ? JSON.stringify(session.signals) : null,
        scores: session.scores ? JSON.stringify(session.scores) : null,
        plan: session.plan ? JSON.stringify(session.plan) : null,
      })
      .returning();

    // Parse JSON fields back to objects
    return {
      ...newSession,
      intake: newSession.intake ? JSON.parse(newSession.intake) : null,
      signals: newSession.signals ? JSON.parse(newSession.signals) : null,
      scores: newSession.scores ? JSON.parse(newSession.scores) : null,
      plan: newSession.plan ? JSON.parse(newSession.plan) : null,
    };
  }

  async getAssessmentSession(id: string): Promise<AssessmentSession | undefined> {
    const [session] = await db.select().from(assessmentSessions).where(eq(assessmentSessions.id, id));
    if (!session) return undefined;

    // Parse JSON fields back to objects
    return {
      ...session,
      intake: session.intake ? JSON.parse(session.intake) : null,
      signals: session.signals ? JSON.parse(session.signals) : null,
      scores: session.scores ? JSON.parse(session.scores) : null,
      plan: session.plan ? JSON.parse(session.plan) : null,
    };
  }

  async updateAssessmentSession(id: string, updates: Partial<AssessmentSession>): Promise<AssessmentSession> {
    const [session] = await db
      .update(assessmentSessions)
      .set({
        ...updates,
        intake: updates.intake ? JSON.stringify(updates.intake) : undefined,
        signals: updates.signals ? JSON.stringify(updates.signals) : undefined,
        scores: updates.scores ? JSON.stringify(updates.scores) : undefined,
        plan: updates.plan ? JSON.stringify(updates.plan) : undefined,
      })
      .where(eq(assessmentSessions.id, id))
      .returning();

    // Parse JSON fields back to objects
    return {
      ...session,
      intake: session.intake ? JSON.parse(session.intake) : null,
      signals: session.signals ? JSON.parse(session.signals) : null,
      scores: session.scores ? JSON.parse(session.scores) : null,
      plan: session.plan ? JSON.parse(session.plan) : null,
    };
  }

  async createWorkoutPlan(plan: InsertWorkoutPlan): Promise<WorkoutPlan> {
    const [newPlan] = await db
      .insert(workoutPlans)
      .values(plan)
      .returning();
    return newPlan;
  }

  async getUserWorkoutPlans(userId: string): Promise<WorkoutPlan[]> {
    return await db.select().from(workoutPlans).where(eq(workoutPlans.userId, userId)).orderBy(desc(workoutPlans.createdAt));
  }

  async createSessionEvent(event: { sessionId: string; eventType: string; eventData?: any }): Promise<SessionEvent> {
    const [newEvent] = await db
      .insert(sessionEvents)
      .values({
        ...event,
        eventData: event.eventData ? JSON.stringify(event.eventData) : null,
      })
      .returning();

    // Parse JSON field back to object
    return {
      ...newEvent,
      eventData: newEvent.eventData ? JSON.parse(newEvent.eventData) : null,
    };
  }

  async getSessionEvents(sessionId: string): Promise<SessionEvent[]> {
    return await db.select().from(sessionEvents).where(eq(sessionEvents.sessionId, sessionId)).orderBy(desc(sessionEvents.timestamp));
  }
}

export const storage = new DatabaseStorage();
