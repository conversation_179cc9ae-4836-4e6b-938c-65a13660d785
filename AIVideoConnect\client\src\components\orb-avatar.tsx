import { useEffect, useRef } from "react";
import { createOrb<PERSON>enderer } from "@/lib/three-orb";

interface OrbAvatarProps {
  state: "welcome" | "discovery" | "coaching" | "reveal" | "upsell" | "idle";
  audioData?: {
    rms: number;
    pitch: number;
    viseme?: string;
  };
  engagement?: number;
  intensity?: number;
  className?: string;
}

export default function OrbAvatar({ 
  state, 
  audioData, 
  engagement = 0.8, 
  intensity = 0.5,
  className = "" 
}: OrbAvatarProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const orbRef = useRef<any>(null);

  useEffect(() => {
    if (!containerRef.current || orbRef.current) return;

    const orb = createOrbRenderer(containerRef.current);
    orbRef.current = orb;

    return () => {
      if (orbRef.current) {
        orbRef.current.dispose();
        orbRef.current = null;
      }
    };
  }, []);

  useEffect(() => {
    if (orbRef.current) {
      orbRef.current.setState(state);
    }
  }, [state]);

  useEffect(() => {
    if (orbRef.current && audioData) {
      orbRef.current.onTTSFrame(audioData);
    }
  }, [audioData]);

  useEffect(() => {
    if (orbRef.current) {
      orbRef.current.setEngagement(engagement);
      orbRef.current.setIntensity(intensity);
    }
  }, [engagement, intensity]);

  return (
    <div className={`orb-container ${className}`}>
      <div 
        ref={containerRef} 
        className="w-full h-full" 
        data-testid="orb-avatar-container"
      />
      
      {/* Fallback CSS orb for when WebGL is unavailable */}
      <div className="ai-orb" style={{ display: orbRef.current ? 'none' : 'block' }}>
        <div className="holographic-overlay"></div>
        <div className="orb-particles particle-1"></div>
        <div className="orb-particles particle-2"></div>
        <div className="orb-particles particle-3"></div>
        <div className="orb-particles particle-4"></div>
      </div>
    </div>
  );
}
