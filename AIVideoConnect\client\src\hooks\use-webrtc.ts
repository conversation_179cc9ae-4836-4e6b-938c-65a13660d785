import { useState, useEffect, useCallback, useRef } from "react";

interface WebSocketHook {
  isConnected: boolean;
  sendMessage: (message: any) => void;
  subscribe: (messageType: string, handler: (data: any) => void) => () => void;
}

export function useWebRTC(websocket?: WebSocketHook) {
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isConnected, setIsConnected] = useState<boolean>(false);

  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const initializeWebRTC = useCallback(async () => {
    try {
      setError(null);
      console.log('Requesting camera and microphone permissions...');
      
      // Get user media with explicit constraints
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { 
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: "user"
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true
        }
      });
      
      console.log('Camera stream obtained:', stream);
      console.log('Video tracks:', stream.getVideoTracks());
      console.log('Audio tracks:', stream.getAudioTracks());
      
      setLocalStream(stream);

      // Create peer connection with enhanced configuration
      const peerConnection = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' },
          { urls: 'stun:stun2.l.google.com:19302' }
        ],
        iceCandidatePoolSize: 10
      });

      // Add local stream to peer connection
      stream.getTracks().forEach(track => {
        console.log('Adding track to peer connection:', track.kind, track.label);
        peerConnection.addTrack(track, stream);
      });

      // Handle remote stream
      peerConnection.ontrack = (event) => {
        console.log('Received remote stream:', event.streams[0]);
        setRemoteStream(event.streams[0]);
      };

      // Handle ICE candidates
      peerConnection.onicecandidate = (event) => {
        if (event.candidate && websocket?.isConnected) {
          websocket.sendMessage({
            type: 'ice-candidate',
            candidate: event.candidate
          });
        }
      };

      // Handle connection state changes
      peerConnection.onconnectionstatechange = () => {
        console.log('Peer connection state:', peerConnection.connectionState);
        if (peerConnection.connectionState === 'failed') {
          setError('Peer connection failed');
        }
      };

      peerConnectionRef.current = peerConnection;
      console.log('WebRTC initialized successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize WebRTC';
      console.error('WebRTC initialization failed:', err);
      setError(errorMessage);
      throw err; // Re-throw so caller can handle
    }
  }, []);

  const connectToSession = useCallback((sessionId: string) => {
    // Check if we're in a browser environment
    if (typeof window === 'undefined') {
      console.warn('WebSocket connection attempted in non-browser environment');
      return;
    }

    // WebSocket connection is now handled by the centralized hook
    if (!websocket?.isConnected) {
      console.warn('WebSocket not connected, cannot establish WebRTC session');
      return;
    }

    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const host = window.location.host || 'localhost:3000';
    const wsUrl = `${protocol}//${host}/ws`;

    console.log('Connecting to WebSocket:', wsUrl);
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('WebSocket connected for session:', sessionId);
      ws.send(JSON.stringify({ type: 'join_session', sessionId }));
      wsRef.current = ws;
      setIsConnected(true);
    };

    ws.onmessage = async (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('WebSocket message received:', data.type);

        // Handle WebRTC signaling messages
        if (data.type === 'offer' && peerConnectionRef.current) {
          await peerConnectionRef.current.setRemoteDescription(new RTCSessionDescription(data.offer));
          const answer = await peerConnectionRef.current.createAnswer();
          await peerConnectionRef.current.setLocalDescription(answer);
          ws.send(JSON.stringify({ type: 'answer', answer, sessionId }));
        } else if (data.type === 'answer' && peerConnectionRef.current) {
          await peerConnectionRef.current.setRemoteDescription(new RTCSessionDescription(data.answer));
        } else if (data.type === 'ice-candidate' && peerConnectionRef.current) {
          await peerConnectionRef.current.addIceCandidate(new RTCIceCandidate(data.candidate));
        }
      } catch (err) {
        console.error('Error handling WebSocket message:', err);
        setError('Failed to process signaling message');
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setError('WebSocket connection failed');
      setIsConnected(false);
    };

    ws.onclose = (event) => {
      console.log('WebSocket closed:', event.code, event.reason);
      setIsConnected(false);

      // Attempt reconnection if not a clean close
      if (event.code !== 1000 && event.code !== 1001) {
        setTimeout(() => {
          console.log('Attempting WebSocket reconnection...');
          connectToSession(sessionId);
        }, 3000);
      }
    };
  }, []);

  const connect = useCallback(async (sessionId: string) => {
    try {
      // Always initialize WebRTC to get fresh camera access
      await initializeWebRTC();
      connectToSession(sessionId);
    } catch (err) {
      console.error('Failed to connect:', err);
      setError(err instanceof Error ? err.message : 'Failed to connect');
    }
  }, [initializeWebRTC, connectToSession]);

  const disconnect = useCallback(() => {
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
      setLocalStream(null);
    }
    
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }
    
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    
    setIsConnected(false);
    setRemoteStream(null);
  }, [localStream]);

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  return {
    isConnected,
    localStream,
    remoteStream,
    error,
    connect,
    disconnect,
  };
}
