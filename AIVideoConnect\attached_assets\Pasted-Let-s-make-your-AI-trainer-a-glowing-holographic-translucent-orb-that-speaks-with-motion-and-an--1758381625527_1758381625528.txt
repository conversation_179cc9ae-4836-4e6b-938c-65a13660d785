Let’s make your AI trainer a glowing, holographic, translucent orb that “speaks” with motion and an aura that reacts to intensity. Below is a build-ready spec with render tech, audio drive, color psychology, and API hooks—so your devs can drop it into the call UI today.

Orb Avatar: System Design
Visual stack (pick one per platform)

Web (React/Next): Three.js + WebGL2, postprocessing (Bloom, FXAA).

React Native (mobile):

Expo + expo-three (GLView) for real 3D, or

<PERSON><PERSON> (fallback) for battery-friendly 2D glow.

Desktop (Electron/Web): same as Web.

Look & feel

Core: translucent sphere (additive blend), inner noise field (GLSL), subtle caustics.

Aura: GPU particle halo (spherical shell + curl noise), soft bloom.

Hologram: scanline/fresnel + slight chromatic aberration.

Depth: inner “heartbeat” pulse synced to speech envelope.

Data inputs that drive the orb

TTS/Voice (when AI speaks)

Amplitude envelope (RMS/peak) → orb scale + aura radius.

Phoneme/viseme timestamps (from ElevenLabs/Apple TTS) → micro-deforms (“mouthless lip-sync”).

Prosody (pitch/energy) → hue/brightness shifts.

Conversation state

Assessment phase → base palette + animation preset (calm intake vs energetic coaching vs soft sell).

Confidence/clarity scores → sparkle density/bloom.

User sentiment (optional)

If you detect stress/uncertainty → soften spectrum, reduce particle velocity.

Color & motion language (simple, memorable)
State	Hue	Motion	Use
Warm welcome	Teal→Azure	Slow, breathing	Start rapport
Discovery Q&A	Cyan	Gentle orbit	Listening mode
Form coaching	Electric Blue w/ white accents	Responsive pulses to cues	“Do this now” clarity
Plan reveal	Aqua→Mint	Expanding bloom	Positive framing
Subtle upsell	Royal Blue w/ gold micro-sparks	Slower, confident	Value, trust, not hype
Error/low network	Dimmed desat blue	Minimal motion	Don’t distract
Control API (drop-in)
type OrbState =
  | "welcome" | "discovery" | "coaching" | "reveal" | "upsell" | "idle" | "degraded";

type OrbControl = {
  setState(state: OrbState): void;             // switches palette + motion preset
  onTTSFrame(d: { time: number; rms: number; pitch: number; viseme?: string }): void;
  setEngagement(x: number): void;              // 0..1 → sparkle density & bloom
  setIntensity(x: number): void;               // 0..1 → aura radius/velocity
  setPrivacy(enabled: boolean): void;          // disables sentiment-driven changes
};


Where to call

onTTSFrame on every audio callback (e.g., 60–120 fps); map rms to scale and aura, pitch to hue shift, viseme to noise warp.

Three.js scene sketch (web/electron)
// init
renderer.toneMapping = THREE.ACESFilmicToneMapping;
composer.addPass(new RenderPass(scene, camera));
composer.addPass(new UnrealBloomPass(new THREE.Vector2(w,h), 0.9, 0.4, 0.85));

const orbGeo = new THREE.SphereGeometry(1, 128, 128);
const orbMat = new THREE.ShaderMaterial({
  transparent: true, blending: THREE.AdditiveBlending, depthWrite: false,
  uniforms: {
    uTime: { value: 0 },
    uRMS: { value: 0 },
    uPitch: { value: 0 },
    uHueBase: { value: 0.55 }, // cyan/blue
    uHueShift: { value: 0.0 },
    uFresnelPow: { value: 3.0 },
    uNoiseAmp: { value: 0.15 }
  },
  vertexShader: `varying vec3 vPos; void main(){ vPos=position; gl_Position=projectionMatrix*modelViewMatrix*vec4(position,1.0);} `,
  fragmentShader: `
    uniform float uTime,uRMS,uPitch,uHueBase,uHueShift,uFresnelPow,uNoiseAmp;
    varying vec3 vPos;
    // simple hash/noise
    float hash(vec3 p){ return fract(sin(dot(p,vec3(12.9898,78.233,37.719)))*43758.5453); }
    float noise(vec3 p){ vec3 i=floor(p), f=fract(p);
      float n= mix(mix(mix(hash(i+vec3(0,0,0)), hash(i+vec3(1,0,0)), f.x),
                       mix(hash(i+vec3(0,1,0)), hash(i+vec3(1,1,0)), f.x), f.y),
                   mix(mix(hash(i+vec3(0,0,1)), hash(i+vec3(1,0,1)), f.x),
                       mix(hash(i+vec3(0,1,1)), hash(i+vec3(1,1,1)), f.x), f.y), f.z);
      return n;
    }
    vec3 h2rgb(float h){
      vec3 c=clamp(abs(mod(h*6.0+vec3(0,4,2),6.0)-3.0)-1.0,0.0,1.0);
      return c*c*(3.0-2.0*c);
    }
    void main(){
      vec3 p=normalize(vPos);
      float fres = pow(1.0-abs(p.z), uFresnelPow);
      float n = noise(p*3.0 + vec3(uTime*0.5)) * uNoiseAmp;
      float a = clamp(0.25 + fres*0.7 + uRMS*0.6 + n, 0.0, 1.0);
      float hue = uHueBase + uHueShift;
      vec3 col = h2rgb(fract(hue)) * (0.6 + uRMS*0.8);
      gl_FragColor = vec4(col, a);
    }
  `
});
const orb = new THREE.Mesh(orbGeo, orbMat);
scene.add(orb);

// aura particles (GPU)
const aura = makeAuraParticles({ count: 20000, additive: true });
scene.add(aura);

function onTTSFrame({ rms, pitch }) {
  orbMat.uniforms.uRMS.value = rms;                     // 0..1
  orbMat.uniforms.uHueShift.value = (pitch-180)/600.0;  // small ± shift
  const s = 1.0 + rms * 0.25;
  orb.scale.set(s, s, s);
  aura.material.uniforms.uIntensity.value = rms;        // radius/velocity
}

function animate(t){
  orbMat.uniforms.uTime.value = t*0.001;
  composer.render();
  requestAnimationFrame(animate);
}


(This is minimal—your devs can swap in better noise, add scanlines and CA post.)

React Native (Expo) options

High-fidelity: expo-three + expo-gl + three + postprocessing.

Battery-friendly: Lottie sprite sheets with amplitude-driven frame scrubbing:

Map rms → frame offset, pitch → tint filter.

Reanimated for hue/scale/tint if you keep it 2D.

Audio pipeline (real-time, low-latency)

Use your TTS stream (AI voice) as the drive signal (no mic loopback needed).

Compute RMS + pitch via AudioWorklet (Web) / AVAudioEngine (iOS) / AAudio (Android).

Emit animation frames at 60–90 Hz; keep end-to-end < 60–100 ms.

// pseudo: feed analytics into orb controller
tts.on('frame', (buf) => {
  const { rms, pitch } = analyzeAudio(buf);
  orbControl.onTTSFrame({ time: audioClock(), rms, pitch });
});

Performance targets

Web/Mac: 60 fps @ 1080p, bloom threshold 0.9, ~20–40k particles.

Mid phones: cap at 30 fps, reduce particles to 5–8k, disable CA.

Thermals: drop to Lottie after 10 min continuous call on budget devices.

Accessibility & privacy

Motion sensitivity toggle (reduce pulses, no scale bounce).

Color-blind safe alt (brightness modulation > hue).

Explicit consent for sentiment-driven changes; single switch disables it without degrading coaching.

Where it plugs into your call flow

AI pane in your dual-video layout becomes the OrbCanvas.

On state change (welcome → discovery → coaching → reveal → upsell), call setState().

While TTS plays, continuously call onTTSFrame() to animate.

When upsell line triggers, slow orbit + slight gold sparkles for confidence.

QA checklist (ship-day ready)

Latency from TTS frame → orb motion < 80 ms

Frame pacing stable under WebRTC + Whisper load

Battery/thermal test on mid-tier Android

Graceful degrade (no shader = static SVG orb + CSS glow)

Toggle audit (privacy, motion, low power)