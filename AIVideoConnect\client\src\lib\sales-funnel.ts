// Subtle Sales Funnel System - Natural conversion without pushy tactics
import { GoalAttainmentScore } from './goal-attainment-scoring';
import { WorkoutPlan } from './plan-generator';

export interface SalesFunnelState {
  stage: 'assessment' | 'results' | 'plan_reveal' | 'value_demonstration' | 'soft_offer' | 'decision';
  userEngagement: number; // 0-100
  readinessToUpgrade: number; // 0-100
  touchpoints: TouchPoint[];
  objections: string[];
  interests: string[];
}

export interface TouchPoint {
  timestamp: Date;
  type: 'question' | 'response' | 'hesitation' | 'excitement' | 'concern';
  content: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  weight: number; // importance 0-1
}

export interface UpgradeOffer {
  tier: 'pro' | 'elite';
  name: string;
  description: string;
  price: number;
  features: string[];
  value_props: string[];
  urgency?: string;
  social_proof?: string;
  guarantee?: string;
}

export interface ConversionTrigger {
  condition: string;
  message: string;
  timing: 'immediate' | 'delayed' | 'contextual';
  priority: number;
}

export class SalesFunnelManager {
  private state: SalesFunnelState;
  private gasScore?: GoalAttainmentScore;
  private plan?: WorkoutPlan;
  
  constructor() {
    this.state = {
      stage: 'assessment',
      userEngagement: 70, // Start optimistic
      readinessToUpgrade: 30, // Start conservative
      touchpoints: [],
      objections: [],
      interests: []
    };
  }
  
  // Analyze user responses for conversion signals
  analyzeUserResponse(response: string, context: string): void {
    const sentiment = this.analyzeSentiment(response);
    const touchpoint: TouchPoint = {
      timestamp: new Date(),
      type: this.classifyResponseType(response),
      content: response,
      sentiment,
      weight: this.calculateWeight(context, sentiment)
    };
    
    this.state.touchpoints.push(touchpoint);
    this.updateEngagementScore(touchpoint);
    this.detectConversionSignals(response);
    this.identifyObjections(response);
  }
  
  private analyzeSentiment(text: string): 'positive' | 'neutral' | 'negative' {
    const positiveWords = ['excited', 'love', 'great', 'amazing', 'perfect', 'yes', 'definitely', 'absolutely'];
    const negativeWords = ['worried', 'concerned', 'expensive', 'not sure', 'maybe', 'difficult', 'hard'];
    
    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }
  
  private classifyResponseType(response: string): TouchPoint['type'] {
    const lowerResponse = response.toLowerCase();
    
    if (lowerResponse.includes('?')) return 'question';
    if (lowerResponse.includes('not sure') || lowerResponse.includes('maybe')) return 'hesitation';
    if (lowerResponse.includes('excited') || lowerResponse.includes('love')) return 'excitement';
    if (lowerResponse.includes('worried') || lowerResponse.includes('concerned')) return 'concern';
    
    return 'response';
  }
  
  private calculateWeight(context: string, sentiment: 'positive' | 'neutral' | 'negative'): number {
    let weight = 0.5; // baseline
    
    // Context importance
    if (context.includes('goal') || context.includes('motivation')) weight += 0.3;
    if (context.includes('time') || context.includes('commitment')) weight += 0.2;
    if (context.includes('price') || context.includes('cost')) weight += 0.4;
    
    // Sentiment modifier
    if (sentiment === 'positive') weight += 0.2;
    if (sentiment === 'negative') weight -= 0.1;
    
    return Math.min(Math.max(weight, 0), 1);
  }
  
  private updateEngagementScore(touchpoint: TouchPoint): void {
    const impact = touchpoint.weight * 10;
    
    if (touchpoint.sentiment === 'positive') {
      this.state.userEngagement = Math.min(this.state.userEngagement + impact, 100);
    } else if (touchpoint.sentiment === 'negative') {
      this.state.userEngagement = Math.max(this.state.userEngagement - impact, 0);
    }
  }
  
  private detectConversionSignals(response: string): void {
    const signals = [
      'need accountability',
      'want support',
      'done this before',
      'failed before',
      'need help',
      'guidance',
      'coach',
      'personal trainer'
    ];
    
    const lowerResponse = response.toLowerCase();
    signals.forEach(signal => {
      if (lowerResponse.includes(signal)) {
        this.state.readinessToUpgrade = Math.min(this.state.readinessToUpgrade + 15, 100);
        this.state.interests.push(signal);
      }
    });
  }
  
  private identifyObjections(response: string): void {
    const objections = [
      { pattern: 'expensive|cost|money|afford', objection: 'price_concern' },
      { pattern: 'time|busy|schedule', objection: 'time_constraint' },
      { pattern: 'not sure|maybe|think about', objection: 'uncertainty' },
      { pattern: 'tried before|failed|didn\'t work', objection: 'past_failure' }
    ];
    
    const lowerResponse = response.toLowerCase();
    objections.forEach(({ pattern, objection }) => {
      if (new RegExp(pattern).test(lowerResponse)) {
        if (!this.state.objections.includes(objection)) {
          this.state.objections.push(objection);
        }
      }
    });
  }
  
  // Generate contextual upgrade offers
  generateUpgradeOffer(): UpgradeOffer | null {
    if (this.state.readinessToUpgrade < 40) return null;
    
    const tier = this.state.readinessToUpgrade > 70 ? 'elite' : 'pro';
    
    const offers = {
      pro: {
        tier: 'pro' as const,
        name: 'AI-Coached Program',
        description: 'Your personalized plan with weekly AI check-ins and automatic adjustments',
        price: 29,
        features: [
          'Weekly progress check-ins',
          'Automatic plan adjustments',
          'Form feedback via video analysis',
          'Nutrition tracking integration',
          '24/7 AI coaching support'
        ],
        value_props: [
          'Stay accountable with weekly check-ins',
          'Never plateau with automatic adjustments',
          'Perfect your form with AI feedback'
        ]
      },
      elite: {
        tier: 'elite' as const,
        name: 'Hybrid AI + Human Coaching',
        description: 'AI coaching enhanced with monthly human trainer reviews',
        price: 79,
        features: [
          'Everything in AI-Coached Program',
          'Monthly video calls with certified trainers',
          'Custom exercise modifications',
          'Injury prevention screening',
          'Priority support'
        ],
        value_props: [
          'Get the best of AI efficiency and human expertise',
          'Prevent injuries with professional oversight',
          'Accelerate results with personalized attention'
        ]
      }
    };
    
    const offer = offers[tier];
    
    // Add contextual elements based on user profile
    if (this.state.interests.includes('accountability')) {
      offer.value_props.unshift('Never miss a workout with built-in accountability');
    }
    
    if (this.state.objections.includes('past_failure')) {
      offer.guarantee = '30-day money-back guarantee - if you don\'t see progress, get a full refund';
    }
    
    if (this.gasScore && this.gasScore.GAS < 60) {
      offer.urgency = 'Limited time: Get your first month for just $19 to boost your success rate';
    }
    
    return offer;
  }
  
  // Generate natural conversation prompts that subtly introduce value
  generateConversationPrompt(stage: string): string {
    const prompts = {
      assessment: [
        "I'm already seeing some patterns that will help me create something amazing for you.",
        "Your answers are giving me great insights into what will work best for your lifestyle.",
        "I can tell you're serious about this - that's exactly the mindset that leads to success."
      ],
      
      results: [
        "Based on your assessment, I can see exactly why previous attempts might not have worked.",
        "Your movement quality shows real potential - with the right guidance, you could see incredible progress.",
        "The good news is that your biggest challenges are exactly what I'm designed to help with."
      ],
      
      plan_reveal: [
        "This plan is specifically designed for someone with your goals and schedule.",
        "I've built in progressions that will keep you challenged but not overwhelmed.",
        "The key to your success will be consistency and making small adjustments along the way."
      ],
      
      value_demonstration: [
        "Many people see great results with just the basic plan, but I've noticed you mentioned [specific challenge].",
        "The difference between good results and amazing results often comes down to having the right support system.",
        "I can already predict a few areas where you might need extra guidance as you progress."
      ]
    };
    
    const stagePrompts = prompts[stage as keyof typeof prompts] || prompts.assessment;
    return stagePrompts[Math.floor(Math.random() * stagePrompts.length)];
  }
  
  // Determine if it's the right time to present an offer
  shouldPresentOffer(): boolean {
    const conditions = [
      this.state.userEngagement > 60,
      this.state.readinessToUpgrade > 50,
      this.state.stage === 'plan_reveal' || this.state.stage === 'value_demonstration',
      this.state.touchpoints.length > 5 // Sufficient interaction
    ];
    
    return conditions.filter(Boolean).length >= 3;
  }
  
  // Handle objections with empathy and value reinforcement
  handleObjection(objection: string): string {
    const responses = {
      price_concern: "I totally understand - investing in yourself is a big decision. That's exactly why I want to make sure you get results. The AI coaching actually costs less than a single session with a personal trainer, but gives you support every single day.",
      
      time_constraint: "I hear you on time being tight - that's actually why this program works so well. Instead of spending hours figuring out what to do, you get a clear plan that fits your schedule. Most people save time because they're not wasting effort on things that don't work.",
      
      uncertainty: "It's smart to be thoughtful about this. What I can tell you is that your assessment shows you have all the ingredients for success. The question isn't whether you can do it - it's whether you want to do it with support or try to figure it out alone again.",
      
      past_failure: "I'm glad you mentioned that - it tells me you're being realistic, which is actually a strength. The difference here is that this isn't a generic program. It's built specifically for your movement patterns, schedule, and goals. Plus, you'll have ongoing support to make adjustments when life happens."
    };
    
    return responses[objection as keyof typeof responses] || 
           "I appreciate you sharing that concern. Let me address it directly...";
  }
  
  // Update funnel state
  updateState(updates: Partial<SalesFunnelState>): void {
    this.state = { ...this.state, ...updates };
  }
  
  // Set assessment results for context
  setAssessmentResults(gasScore: GoalAttainmentScore, plan: WorkoutPlan): void {
    this.gasScore = gasScore;
    this.plan = plan;
    
    // Adjust readiness based on GAS score
    if (gasScore.GAS < 50) {
      this.state.readinessToUpgrade += 20; // Lower success rate = higher need for support
    }
  }
  
  // Get current state
  getState(): SalesFunnelState {
    return { ...this.state };
  }
  
  // Get conversion probability
  getConversionProbability(): number {
    const factors = [
      this.state.userEngagement / 100,
      this.state.readinessToUpgrade / 100,
      Math.min(this.state.touchpoints.length / 10, 1), // Interaction depth
      this.state.objections.length === 0 ? 1 : 0.7, // Fewer objections = higher probability
      this.state.interests.length / 5 // Interest signals
    ];
    
    const average = factors.reduce((sum, factor) => sum + factor, 0) / factors.length;
    return Math.round(average * 100);
  }
}
