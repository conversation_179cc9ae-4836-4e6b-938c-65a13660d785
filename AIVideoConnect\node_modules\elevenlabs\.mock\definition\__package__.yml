errors:
  UnprocessableEntityError:
    status-code: 422
    type: HTTPValidationError
    docs: Validation Error
    examples:
      - value: {}
  BadRequestError:
    status-code: 400
    type: unknown
    docs: Invalid request
    examples:
      - value:
          error: invalid_output_format
          message: output_format must be wav or none
  ForbiddenError:
    status-code: 403
    type: unknown
    docs: Permission denied
    examples:
      - value:
          error: permission_denied
          message: User does not have required permissions
      - value:
          error: anonymous_not_allowed
          message: Anonymous users cannot use this function
  NotFoundError:
    status-code: 404
    type: unknown
    docs: Dubbing not found
    examples:
      - value:
          error: dubbing_not_found
          message: There is no dubbing for language {language_code}.
      - value:
          error: transcript_not_found
          message: No transcript was found for the dub.
  TooEarlyError:
    status-code: 425
    type: unknown
    docs: Dubbing not ready
    examples:
      - value:
          error: dubbing_not_dubbed
          message: Dubbing has not finished yet.
types:
  AsrConversationalConfig:
    properties:
      quality:
        type: optional<AsrQuality>
        docs: The quality of the transcription
      provider:
        type: optional<AsrProvider>
        docs: The provider of the transcription service
      user_input_audio_format:
        type: optional<AsrInputFormat>
        docs: The format of the audio to be transcribed
      keywords:
        type: optional<list<string>>
        docs: Keywords to boost prediction probability for
    source:
      openapi: openapi.json
  AsrInputFormat:
    enum:
      - pcm_8000
      - pcm_16000
      - pcm_22050
      - pcm_24000
      - pcm_44100
      - pcm_48000
      - ulaw_8000
    source:
      openapi: openapi.json
  AsrProvider:
    type: literal<"elevenlabs">
  AsrQuality:
    type: literal<"high">
  AddChapterResponseModel:
    properties:
      chapter:
        type: ChapterWithContentResponseModel
    source:
      openapi: openapi.json
  AddKnowledgeBaseResponseModel:
    properties:
      id: string
      name: string
      prompt_injectable: boolean
    source:
      openapi: openapi.json
  AddProjectResponseModel:
    properties:
      project:
        type: ProjectResponse
    source:
      openapi: openapi.json
  AddPronunciationDictionaryResponseModelPermissionOnResource:
    enum:
      - admin
      - editor
      - viewer
    inline: true
    source:
      openapi: openapi.json
  AddPronunciationDictionaryResponseModel:
    properties:
      id:
        type: string
        docs: The ID of the created pronunciation dictionary.
      name:
        type: string
        docs: The name of the created pronunciation dictionary.
      created_by:
        type: string
        docs: The user ID of the creator of the pronunciation dictionary.
      creation_time_unix:
        type: integer
        docs: The creation time of the pronunciation dictionary in Unix timestamp.
      version_id:
        type: string
        docs: The ID of the created pronunciation dictionary version.
      version_rules_num:
        type: integer
        docs: The number of rules in the version of the pronunciation dictionary.
      description:
        type: optional<string>
        docs: The description of the pronunciation dictionary.
      permission_on_resource:
        type: optional<AddPronunciationDictionaryResponseModelPermissionOnResource>
        docs: The permission on the resource of the pronunciation dictionary.
    source:
      openapi: openapi.json
  AddVoiceIvcResponseModel:
    properties:
      voice_id:
        type: string
        docs: The ID of the newly created voice.
      requires_verification:
        type: boolean
        docs: Whether the voice requires verification
    source:
      openapi: openapi.json
  AddVoiceResponseModel:
    properties:
      voice_id:
        type: string
        docs: The ID of the voice.
    source:
      openapi: openapi.json
  AddWorkspaceGroupMemberResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the workspace group member addition request. If the
          request was successful, the status will be 'ok'. Otherwise an error
          message with status 500 will be returned.
    source:
      openapi: openapi.json
  AddWorkspaceInviteResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the workspace invite request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  AdditionalFormatResponseModel:
    properties:
      requested_format:
        type: string
        docs: The requested format.
      file_extension:
        type: string
        docs: The file extension of the additional format.
      content_type:
        type: string
        docs: The content type of the additional format.
      is_base64_encoded:
        type: boolean
        docs: Whether the content is base64 encoded.
      content:
        type: string
        docs: The content of the additional format.
    source:
      openapi: openapi.json
  AdditionalFormats:
    type: list<ExportOptions>
  AgentBan:
    properties:
      at_unix: integer
      reason:
        type: optional<string>
      reason_type:
        type: BanReasonType
    source:
      openapi: openapi.json
  AgentCallLimits:
    properties:
      agent_concurrency_limit:
        type: optional<integer>
        docs: >-
          The maximum number of concurrent conversations. -1 indicates that
          there is no maximum
        default: -1
      daily_limit:
        type: optional<integer>
        docs: The maximum number of conversations per day
        default: 100000
    source:
      openapi: openapi.json
  AgentConfig:
    properties:
      first_message:
        type: optional<string>
        docs: >-
          If non-empty, the first message the agent will say. If empty, the
          agent waits for the user to start the discussion.
        default: ''
      language:
        type: optional<string>
        docs: Language of the agent - used for ASR and TTS
        default: en
      dynamic_variables:
        type: optional<DynamicVariablesConfig>
        docs: Configuration for dynamic variables
      prompt:
        type: optional<PromptAgent>
        docs: The prompt for the agent
    source:
      openapi: openapi.json
  AgentConfigDbModel:
    properties:
      first_message:
        type: optional<string>
        docs: >-
          If non-empty, the first message the agent will say. If empty, the
          agent waits for the user to start the discussion.
        default: ''
      language:
        type: optional<string>
        docs: Language of the agent - used for ASR and TTS
        default: en
      dynamic_variables:
        type: optional<DynamicVariablesConfig>
        docs: Configuration for dynamic variables
      prompt:
        type: optional<PromptAgentDbModel>
    source:
      openapi: openapi.json
  AgentConfigOverride:
    properties:
      prompt:
        type: optional<PromptAgentOverride>
        docs: The overrides for the prompt configuration
      first_message:
        type: optional<string>
        docs: >-
          If non-empty, the first message the agent will say. If empty, the
          agent waits for the user to start the discussion
      language:
        type: optional<string>
        docs: The language of the agent, used for ASR and TTS
    source:
      openapi: openapi.json
  AgentConfigOverrideConfig:
    properties:
      prompt:
        type: optional<PromptAgentOverrideConfig>
        docs: Overrides for the prompt configuration
      first_message:
        type: optional<boolean>
        docs: Whether to allow overriding the first message
        default: false
      language:
        type: optional<boolean>
        docs: Whether to allow overriding the language
        default: false
    source:
      openapi: openapi.json
  AgentMetadataResponseModel:
    properties:
      created_at_unix_secs:
        type: integer
        docs: The creation time of the agent in unix seconds
    source:
      openapi: openapi.json
  AgentPlatformSettingsRequestModel:
    properties:
      auth:
        type: optional<AuthSettings>
        docs: Settings for authentication
      evaluation:
        type: optional<EvaluationSettings>
        docs: Settings for evaluation
      widget:
        type: optional<WidgetConfig>
        docs: Configuration for the widget
      data_collection:
        type: optional<map<string, LiteralJsonSchemaProperty>>
        docs: Data collection settings
      overrides:
        type: optional<ConversationInitiationClientDataConfigInput>
        docs: Additional overrides for the agent during conversation initiation
      call_limits:
        type: optional<AgentCallLimits>
        docs: Call limits for the agent
      privacy:
        type: optional<PrivacyConfig>
        docs: Privacy settings for the agent
      workspace_overrides:
        type: optional<AgentWorkspaceOverridesInput>
        docs: Workspace overrides for the agent
    source:
      openapi: openapi.json
  AgentPlatformSettingsResponseModel:
    properties:
      auth:
        type: optional<AuthSettings>
        docs: Settings for authentication
      evaluation:
        type: optional<EvaluationSettings>
        docs: Settings for evaluation
      widget:
        type: optional<WidgetConfig>
        docs: Configuration for the widget
      data_collection:
        type: optional<map<string, LiteralJsonSchemaProperty>>
        docs: Data collection settings
      overrides:
        type: optional<ConversationInitiationClientDataConfigOutput>
        docs: Additional overrides for the agent during conversation initiation
      call_limits:
        type: optional<AgentCallLimits>
        docs: Call limits for the agent
      privacy:
        type: optional<PrivacyConfig>
        docs: Privacy settings for the agent
      workspace_overrides:
        type: optional<AgentWorkspaceOverridesOutput>
        docs: Workspace overrides for the agent
      safety:
        type: optional<SafetyResponseModel>
    source:
      openapi: openapi.json
  AgentSimulatedChatTestResponseModel:
    properties:
      simulated_conversation:
        type: list<ConversationHistoryTranscriptCommonModelOutput>
      analysis:
        type: ConversationHistoryAnalysisCommonModel
    source:
      openapi: openapi.json
  AgentSummaryResponseModel:
    properties:
      agent_id:
        type: string
        docs: The ID of the agent
      name:
        type: string
        docs: The name of the agent
      tags:
        docs: Agent tags used to categorize the agent
        type: list<string>
      created_at_unix_secs:
        type: integer
        docs: The creation time of the agent in unix seconds
      access_info:
        type: ResourceAccessInfo
        docs: The access information of the agent
    source:
      openapi: openapi.json
  AgentTransfer:
    properties:
      agent_id: string
      condition: string
    source:
      openapi: openapi.json
  AgentWorkspaceOverridesInput:
    properties:
      conversation_initiation_client_data_webhook:
        type: optional<ConversationInitiationClientDataWebhook>
        docs: The webhook to send conversation initiation client data to
      webhooks:
        type: optional<ConvAiWebhooks>
    source:
      openapi: openapi.json
  AgentWorkspaceOverridesOutput:
    properties:
      conversation_initiation_client_data_webhook:
        type: optional<ConversationInitiationClientDataWebhook>
        docs: The webhook to send conversation initiation client data to
      webhooks:
        type: optional<ConvAiWebhooks>
    source:
      openapi: openapi.json
  AllowlistItem:
    properties:
      hostname:
        type: string
        docs: The hostname of the allowed origin
    source:
      openapi: openapi.json
  ArrayJsonSchemaPropertyInputItems:
    discriminated: false
    union:
      - type: LiteralJsonSchemaProperty
      - type: ObjectJsonSchemaPropertyInput
      - type: ArrayJsonSchemaPropertyInput
    source:
      openapi: openapi.json
    inline: true
  ArrayJsonSchemaPropertyInput:
    properties:
      type:
        type: optional<literal<"array">>
      description:
        type: optional<string>
        default: ''
      items:
        display-name: Items
        type: ArrayJsonSchemaPropertyInputItems
    source:
      openapi: openapi.json
  ArrayJsonSchemaPropertyOutputItems:
    discriminated: false
    union:
      - type: LiteralJsonSchemaProperty
      - type: ObjectJsonSchemaPropertyOutput
      - type: ArrayJsonSchemaPropertyOutput
    source:
      openapi: openapi.json
    inline: true
  ArrayJsonSchemaPropertyOutput:
    properties:
      type:
        type: optional<literal<"array">>
      description:
        type: optional<string>
        default: ''
      items:
        display-name: Items
        type: ArrayJsonSchemaPropertyOutputItems
    source:
      openapi: openapi.json
  AudioNativeCreateProjectResponseModel:
    properties:
      project_id:
        type: string
        docs: The ID of the created Audio Native project.
      converting:
        type: boolean
        docs: Whether the project is currently being converted.
      html_snippet:
        type: string
        docs: The HTML snippet to embed the Audio Native player.
    source:
      openapi: openapi.json
  AudioNativeEditContentResponseModel:
    properties:
      project_id:
        type: string
        docs: The ID of the project.
      converting:
        type: boolean
        docs: Whether the project is currently being converted.
      publishing:
        type: boolean
        docs: Whether the project is currently being published.
      html_snippet:
        type: string
        docs: The HTML snippet to embed the Audio Native player.
    source:
      openapi: openapi.json
  AudioNativeProjectSettingsResponseModelStatus:
    enum:
      - processing
      - ready
    docs: Current state of the project
    default: ready
    inline: true
    source:
      openapi: openapi.json
  AudioNativeProjectSettingsResponseModel:
    properties:
      title:
        type: string
        docs: The title of the project.
      image:
        type: string
        docs: The image of the project.
      author:
        type: string
        docs: The author of the project.
      small:
        type: boolean
        docs: Whether the project is small.
      text_color:
        type: string
        docs: The text color of the project.
      background_color:
        type: string
        docs: The background color of the project.
      sessionization:
        type: integer
        docs: >-
          The sessionization of the project. Specifies for how many minutes to
          persist the session across page reloads.
      audio_path:
        type: optional<string>
        docs: The path of the audio file.
      audio_url:
        type: optional<string>
        docs: The URL of the audio file.
      status:
        type: optional<AudioNativeProjectSettingsResponseModelStatus>
        docs: Current state of the project
        default: ready
    source:
      openapi: openapi.json
  AudioWithTimestampsResponse:
    properties:
      audio_base64:
        type: string
        docs: Base64 encoded audio data
      alignment:
        type: optional<CharacterAlignmentResponseModel>
        docs: Timestamp information for each character in the original text
      normalized_alignment:
        type: optional<CharacterAlignmentResponseModel>
        docs: Timestamp information for each character in the normalized text
    source:
      openapi: openapi.json
  AuthSettings:
    properties:
      enable_auth:
        type: optional<boolean>
        docs: >-
          If set to true, starting a conversation with an agent will require a
          signed token
        default: false
      allowlist:
        type: optional<list<AllowlistItem>>
        docs: A list of hosts that are allowed to start conversations with the agent
      shareable_token:
        type: optional<string>
        docs: >-
          A shareable token that can be used to start a conversation with the
          agent
    source:
      openapi: openapi.json
  AuthorizationMethod:
    enum:
      - invalid
      - public
      - authorization_header
      - signed_url
      - shareable_link
      - livekit_token
      - livekit_token_website
    source:
      openapi: openapi.json
  BanReasonType:
    enum:
      - safety
      - manual
    source:
      openapi: openapi.json
  BatchCallDetailedResponse:
    docs: Detailed response model for a batch call including all recipients.
    properties:
      id: string
      phone_number_id: string
      name: string
      agent_id: string
      created_at_unix: integer
      scheduled_time_unix: integer
      total_calls_dispatched: integer
      total_calls_scheduled: integer
      last_updated_at_unix: integer
      status:
        type: BatchCallStatus
      agent_name: string
      recipients:
        type: list<OutboundCallRecipientResponseModel>
    source:
      openapi: openapi.json
  BatchCallRecipientStatus:
    enum:
      - pending
      - in_progress
      - completed
      - failed
      - cancelled
    source:
      openapi: openapi.json
  BatchCallStatus:
    enum:
      - pending
      - in_progress
      - completed
      - failed
      - cancelled
    source:
      openapi: openapi.json
  BodyAddChapterToAProjectV1ProjectsProjectIdChaptersAddPost:
    properties:
      name:
        type: string
        docs: The name of the chapter, used for identification only.
      from_url:
        type: optional<string>
        docs: >-
          An optional URL from which we will extract content to initialize the
          Studio project. If this is set, 'from_url' must be null. If neither
          'from_url' or 'from_document' are provided we will initialize the
          Studio project as blank.
    source:
      openapi: openapi.json
  BodyAddProjectV1ProjectsAddPostTargetAudience:
    enum:
      - children
      - value: young adult
        name: YoungAdult
      - adult
      - value: all ages
        name: AllAges
    inline: true
    source:
      openapi: openapi.json
  BodyAddProjectV1ProjectsAddPostFiction:
    enum:
      - fiction
      - value: non-fiction
        name: NonFiction
    inline: true
    source:
      openapi: openapi.json
  BodyAddProjectV1ProjectsAddPostApplyTextNormalization:
    enum:
      - auto
      - 'on'
      - 'off'
      - apply_english
    inline: true
    source:
      openapi: openapi.json
  BodyAddProjectV1ProjectsAddPostSourceType:
    enum:
      - blank
      - book
      - article
      - genfm
    inline: true
    source:
      openapi: openapi.json
  AddProjectRequest:
    properties:
      name:
        type: string
        docs: The name of the Studio project, used for identification only.
      default_title_voice_id:
        type: string
        docs: >-
          The voice_id that corresponds to the default voice used for new
          titles.
      default_paragraph_voice_id:
        type: string
        docs: >-
          The voice_id that corresponds to the default voice used for new
          paragraphs.
      default_model_id:
        type: string
        docs: >-
          The ID of the model to be used for this Studio project, you can query
          GET /v1/models to list all available models.
      from_url:
        type: optional<string>
        docs: >-
          An optional URL from which we will extract content to initialize the
          Studio project. If this is set, 'from_url' must be null. If neither
          'from_url' or 'from_document' are provided we will initialize the
          Studio project as blank.
      from_document:
        type: optional<string>
        docs: >-
          An optional .epub, .pdf, .txt or similar file can be provided. If
          provided, we will initialize the Studio project with its content. If
          this is set, 'from_url' must be null.  If neither 'from_url' or
          'from_document' are provided we will initialize the Studio project as
          blank.
        validation:
          format: binary
      quality_preset:
        type: optional<string>
        docs: >
          Output quality of the generated audio. Must be one of:

          standard - standard output format, 128kbps with 44.1kHz sample rate.

          high - high quality output format, 192kbps with 44.1kHz sample rate
          and major improvements on our side. Using this setting increases the
          credit cost by 20%.

          ultra - ultra quality output format, 192kbps with 44.1kHz sample rate
          and highest improvements on our side. Using this setting increases the
          credit cost by 50%.

          ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz
          sample rate and highest improvements on our side in a fully lossless
          format. Using this setting increases the credit cost by 100%.
        default: standard
      title:
        type: optional<string>
        docs: >-
          An optional name of the author of the Studio project, this will be
          added as metadata to the mp3 file on Studio project or chapter
          download.
      author:
        type: optional<string>
        docs: >-
          An optional name of the author of the Studio project, this will be
          added as metadata to the mp3 file on Studio project or chapter
          download.
      description:
        type: optional<string>
        docs: An optional description of the Studio project.
      genres:
        type: optional<list<string>>
        docs: An optional list of genres associated with the Studio project.
      target_audience:
        type: optional<BodyAddProjectV1ProjectsAddPostTargetAudience>
        docs: An optional target audience of the Studio project.
      language:
        type: optional<string>
        docs: >-
          An optional language of the Studio project. Two-letter language code
          (ISO 639-1).
        validation:
          minLength: 2
          maxLength: 2
      content_type:
        type: optional<string>
        docs: An optional content type of the Studio project.
      original_publication_date:
        type: optional<string>
        docs: >-
          An optional original publication date of the Studio project, in the
          format YYYY-MM-DD or YYYY.
        validation:
          pattern: ^\d{4}-\d{2}-\d{2}$|^\d{4}$
      mature_content:
        type: optional<boolean>
        docs: >-
          An optional specification of whether this Studio project contains
          mature content.
      isbn_number:
        type: optional<string>
        docs: >-
          An optional ISBN number of the Studio project you want to create, this
          will be added as metadata to the mp3 file on Studio project or chapter
          download.
      acx_volume_normalization:
        type: optional<boolean>
        docs: >-
          [Deprecated] When the Studio project is downloaded, should the
          returned audio have postprocessing in order to make it compliant with
          audiobook normalized volume requirements
        default: false
      volume_normalization:
        type: optional<boolean>
        docs: >-
          When the Studio project is downloaded, should the returned audio have
          postprocessing in order to make it compliant with audiobook normalized
          volume requirements
        default: false
      pronunciation_dictionary_locators:
        type: optional<list<string>>
        docs: >-
          A list of pronunciation dictionary locators
          (pronunciation_dictionary_id, version_id) encoded as a list of JSON
          strings for pronunciation dictionaries to be applied to the text. A
          list of json encoded strings is required as adding projects may occur
          through formData as opposed to jsonBody. To specify multiple
          dictionaries use multiple --form lines in your curl, such as --form
          'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"'
          --form
          'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'.
          Note that multiple dictionaries are not currently supported by our UI
          which will only show the first.
      callback_url:
        type: optional<string>
        docs: >-
          A url that will be called by our service when the Studio project is
          converted. Request will contain a json blob containing the status of
          the conversion
      fiction:
        type: optional<BodyAddProjectV1ProjectsAddPostFiction>
        docs: >-
          An optional specification of whether the content of this Studio
          project is fiction.
      apply_text_normalization:
        type: optional<BodyAddProjectV1ProjectsAddPostApplyTextNormalization>
        docs: |2-

              This parameter controls text normalization with four modes: 'auto', 'on', 'apply_english' and 'off'.
              When set to 'auto', the system will automatically decide whether to apply text normalization
              (e.g., spelling out numbers). With 'on', text normalization will always be applied, while
              with 'off', it will be skipped. 'apply_english' is the same as 'on' but will assume that text is in English.
              
      auto_convert:
        type: optional<boolean>
        docs: Whether to auto convert the Studio project to audio or not.
        default: false
      auto_assign_voices:
        type: optional<boolean>
        docs: >-
          [Alpha Feature] Whether automatically assign voices to phrases in the
          create Project.
      source_type:
        type: optional<BodyAddProjectV1ProjectsAddPostSourceType>
        docs: The type of Studio project to create.
    source:
      openapi: openapi.json
  BodyAddToKnowledgeBaseV1ConvaiAddToKnowledgeBasePost:
    properties:
      name:
        type: optional<string>
        docs: A custom, human-readable name for the document.
        validation:
          minLength: 1
      url:
        type: optional<string>
        docs: >-
          URL to a page of documentation that the agent will have access to in
          order to interact with users.
      file:
        type: optional<string>
        docs: >-
          Documentation that the agent will have access to in order to interact
          with users.
        validation:
          format: binary
    source:
      openapi: openapi.json
  BodyAddToKnowledgeBaseV1ConvaiAgentsAgentIdAddToKnowledgeBasePost:
    properties:
      name:
        type: optional<string>
        docs: A custom, human-readable name for the document.
        validation:
          minLength: 1
      url:
        type: optional<string>
        docs: >-
          URL to a page of documentation that the agent will have access to in
          order to interact with users.
      file:
        type: optional<string>
        docs: >-
          Documentation that the agent will have access to in order to interact
          with users.
        validation:
          format: binary
    source:
      openapi: openapi.json
  CreatePreviouslyGeneratedVoiceRequest:
    properties:
      voice_name:
        type: string
        docs: Name to use for the created voice.
      voice_description:
        type: string
        docs: Description to use for the created voice.
      generated_voice_id:
        type: string
        docs: >-
          The generated_voice_id to create, call POST
          /v1/text-to-voice/create-previews and fetch the generated_voice_id
          from the response header if don't have one yet.
      played_not_selected_voice_ids:
        type: optional<list<string>>
        docs: >-
          List of voice ids that the user has played but not selected. Used for
          RLHF.
      labels:
        type: optional<map<string, optional<string>>>
        docs: Optional, metadata to add to the created voice. Defaults to None.
    source:
      openapi: openapi.json
  BodyCreatePodcastV1ProjectsPodcastCreatePostMode:
    discriminant: type
    base-properties: {}
    docs: >-
      The type of podcast to generate. Can be 'conversation', an interaction
      between two voices, or 'bulletin', a monologue.
    union:
      conversation:
        type: PodcastConversationMode
      bulletin:
        type: PodcastBulletinMode
    source:
      openapi: openapi.json
  BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem:
    discriminant: type
    base-properties: {}
    union:
      text:
        type: PodcastTextSource
      url:
        type: PodcastUrlSource
    source:
      openapi: openapi.json
  BodyCreatePodcastV1ProjectsPodcastCreatePostSource:
    discriminated: false
    docs: The source content for the Podcast.
    union:
      - type: PodcastTextSource
      - type: PodcastUrlSource
      - list<BodyCreatePodcastV1ProjectsPodcastCreatePostSourceItem>
    source:
      openapi: openapi.json
    inline: true
  BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset:
    enum:
      - standard
      - high
      - highest
      - ultra
      - ultra_lossless
    docs: >
      Output quality of the generated audio. Must be one of:

      standard - standard output format, 128kbps with 44.1kHz sample rate.

      high - high quality output format, 192kbps with 44.1kHz sample rate and
      major improvements on our side. Using this setting increases the credit
      cost by 20%.

      ultra - ultra quality output format, 192kbps with 44.1kHz sample rate and
      highest improvements on our side. Using this setting increases the credit
      cost by 50%.

      ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz
      sample rate and highest improvements on our side in a fully lossless
      format. Using this setting increases the credit cost by 100%.
    default: standard
    inline: true
    source:
      openapi: openapi.json
  BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale:
    enum:
      - short
      - default
      - long
    docs: |
      Duration of the generated podcast. Must be one of:
      short - produces podcasts shorter than 3 minutes.
      default - produces podcasts roughly between 3-7 minutes.
      long - prodces podcasts longer than 7 minutes.
    default: default
    inline: true
    source:
      openapi: openapi.json
  BodyCreatePodcastV1ProjectsPodcastCreatePost:
    properties:
      model_id:
        type: string
        docs: >-
          The ID of the model to be used for this Studio project, you can query
          GET /v1/models to list all available models.
      mode:
        display-name: Mode
        type: BodyCreatePodcastV1ProjectsPodcastCreatePostMode
        docs: >-
          The type of podcast to generate. Can be 'conversation', an interaction
          between two voices, or 'bulletin', a monologue.
      source:
        display-name: Source
        type: BodyCreatePodcastV1ProjectsPodcastCreatePostSource
        docs: The source content for the Podcast.
      quality_preset:
        type: optional<BodyCreatePodcastV1ProjectsPodcastCreatePostQualityPreset>
        docs: >
          Output quality of the generated audio. Must be one of:

          standard - standard output format, 128kbps with 44.1kHz sample rate.

          high - high quality output format, 192kbps with 44.1kHz sample rate
          and major improvements on our side. Using this setting increases the
          credit cost by 20%.

          ultra - ultra quality output format, 192kbps with 44.1kHz sample rate
          and highest improvements on our side. Using this setting increases the
          credit cost by 50%.

          ultra lossless - ultra quality output format, 705.6kbps with 44.1kHz
          sample rate and highest improvements on our side in a fully lossless
          format. Using this setting increases the credit cost by 100%.
        default: standard
      duration_scale:
        type: optional<BodyCreatePodcastV1ProjectsPodcastCreatePostDurationScale>
        docs: |
          Duration of the generated podcast. Must be one of:
          short - produces podcasts shorter than 3 minutes.
          default - produces podcasts roughly between 3-7 minutes.
          long - prodces podcasts longer than 7 minutes.
        default: default
      language:
        type: optional<string>
        docs: >-
          An optional language of the Studio project. Two-letter language code
          (ISO 639-1).
        validation:
          minLength: 2
          maxLength: 2
      highlights:
        type: optional<list<string>>
        docs: >-
          A brief summary or highlights of the Studio project's content,
          providing key points or themes. This should be between 10 and 70
          characters.
      callback_url:
        type: optional<string>
        docs: >-
          A url that will be called by our service when the Studio project is
          converted. Request will contain a json blob containing the status of
          the conversion
    source:
      openapi: openapi.json
  BodyEditBasicProjectInfoV1ProjectsProjectIdPost:
    properties:
      name:
        type: string
        docs: The name of the Studio project, used for identification only.
      default_title_voice_id:
        type: string
        docs: >-
          The voice_id that corresponds to the default voice used for new
          titles.
      default_paragraph_voice_id:
        type: string
        docs: >-
          The voice_id that corresponds to the default voice used for new
          paragraphs.
      title:
        type: optional<string>
        docs: >-
          An optional name of the author of the Studio project, this will be
          added as metadata to the mp3 file on Studio project or chapter
          download.
      author:
        type: optional<string>
        docs: >-
          An optional name of the author of the Studio project, this will be
          added as metadata to the mp3 file on Studio project or chapter
          download.
      isbn_number:
        type: optional<string>
        docs: >-
          An optional ISBN number of the Studio project you want to create, this
          will be added as metadata to the mp3 file on Studio project or chapter
          download.
      volume_normalization:
        type: optional<boolean>
        docs: >-
          When the Studio project is downloaded, should the returned audio have
          postprocessing in order to make it compliant with audiobook normalized
          volume requirements
        default: false
    source:
      openapi: openapi.json
  BodyEditChapterV1ProjectsProjectIdChaptersChapterIdPatch:
    properties:
      name:
        type: optional<string>
        docs: The name of the chapter, used for identification only.
      content:
        type: optional<ChapterContentInputModel>
        docs: The chapter content to use.
    source:
      openapi: openapi.json
  BodyEditProjectContentV1ProjectsProjectIdContentPost:
    properties:
      from_url:
        type: optional<string>
        docs: >-
          An optional URL from which we will extract content to initialize the
          Studio project. If this is set, 'from_url' must be null. If neither
          'from_url' or 'from_document' are provided we will initialize the
          Studio project as blank.
      from_document:
        type: optional<string>
        docs: >-
          An optional .epub, .pdf, .txt or similar file can be provided. If
          provided, we will initialize the Studio project with its content. If
          this is set, 'from_url' must be null.  If neither 'from_url' or
          'from_document' are provided we will initialize the Studio project as
          blank.
        validation:
          format: binary
      auto_convert:
        type: optional<boolean>
        docs: Whether to auto convert the Studio project to audio or not.
        default: false
    source:
      openapi: openapi.json
  BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostGender:
    enum:
      - female
      - male
    docs: >-
      Category code corresponding to the gender of the generated voice. Possible
      values: female, male.
    inline: true
    source:
      openapi: openapi.json
  BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostAge:
    enum:
      - young
      - middle_aged
      - old
    docs: >-
      Category code corresponding to the age of the generated voice. Possible
      values: young, middle_aged, old.
    inline: true
    source:
      openapi: openapi.json
  GenerateVoiceRequest:
    properties:
      gender:
        type: BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostGender
        docs: >-
          Category code corresponding to the gender of the generated voice.
          Possible values: female, male.
      accent:
        type: string
        docs: >-
          Category code corresponding to the accent of the generated voice.
          Possible values: british, american, african, australian, indian.
      age:
        type: BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostAge
        docs: >-
          Category code corresponding to the age of the generated voice.
          Possible values: young, middle_aged, old.
      accent_strength:
        type: double
        docs: >-
          The strength of the accent of the generated voice. Has to be between
          0.3 and 2.0.
      text:
        type: string
        docs: Text to generate, text length has to be between 100 and 1000.
        validation:
          minLength: 100
          maxLength: 1000
    source:
      openapi: openapi.json
  BodyRetrieveVoiceSampleAudioV1VoicesPvcVoiceIdSamplesSampleIdAudioGet:
    properties:
      remove_background_noise:
        type: optional<boolean>
        docs: >-
          If set will remove background noise for voice samples using our audio
          isolation model. If the samples do not include background noise, it
          can make the quality worse.
        default: false
    source:
      openapi: openapi.json
  BodyStreamChapterAudioV1ProjectsProjectIdChaptersChapterIdSnapshotsChapterSnapshotIdStreamPost:
    properties:
      convert_to_mpeg:
        type: optional<boolean>
        docs: Whether to convert the audio to mpeg format.
        default: false
    source:
      openapi: openapi.json
  BodyStreamProjectAudioV1ProjectsProjectIdSnapshotsProjectSnapshotIdStreamPost:
    properties:
      convert_to_mpeg:
        type: optional<boolean>
        docs: Whether to convert the audio to mpeg format.
        default: false
    source:
      openapi: openapi.json
  UpdatePronunciationDictionariesRequest:
    properties:
      pronunciation_dictionary_locators:
        docs: >-
          A list of pronunciation dictionary locators
          (pronunciation_dictionary_id, version_id) encoded as a list of JSON
          strings for pronunciation dictionaries to be applied to the text. A
          list of json encoded strings is required as adding projects may occur
          through formData as opposed to jsonBody. To specify multiple
          dictionaries use multiple --form lines in your curl, such as --form
          'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"Vmd4Zor6fplcA7WrINey\",\"version_id\":\"hRPaxjlTdR7wFMhV4w0b\"}"'
          --form
          'pronunciation_dictionary_locators="{\"pronunciation_dictionary_id\":\"JzWtcGQMJ6bnlWwyMo7e\",\"version_id\":\"lbmwxiLu4q6txYxgdZqn\"}"'.
          Note that multiple dictionaries are not currently supported by our UI
          which will only show the first.
        type: list<PronunciationDictionaryVersionLocator>
      invalidate_affected_text:
        type: optional<boolean>
        docs: >-
          This will automatically mark text in this project for reconversion
          when the new dictionary applies or the old one no longer does.
        default: true
    source:
      openapi: openapi.json
  BreakdownTypes:
    enum:
      - none
      - voice
      - voice_multiplier
      - user
      - groups
      - api_keys
      - all_api_keys
      - product_type
      - model
      - resource
      - request_queue
    docs: >-
      How to break down the information. Cannot be "user" or "api_key" if
      include_workspace_metrics is False.
    source:
      openapi: openapi.json
  ChapterContentBlockExtendableNodeResponseModel:
    docs: Not used. Make sure you anticipate new types in the future.
    properties: {}
    source:
      openapi: openapi.json
  ChapterContentBlockInputModel:
    properties:
      block_id:
        type: optional<string>
      nodes:
        type: list<ChapterContentParagraphTtsNodeInputModel>
    source:
      openapi: openapi.json
  ChapterContentBlockResponseModelNodesItem:
    discriminant: type
    base-properties: {}
    union:
      tts_node:
        type: ChapterContentBlockTtsNodeResponseModel
      _other:
        type: ChapterContentBlockExtendableNodeResponseModel
    source:
      openapi: openapi.json
  ChapterContentBlockResponseModel:
    properties:
      block_id: string
      nodes:
        type: list<ChapterContentBlockResponseModelNodesItem>
    source:
      openapi: openapi.json
  ChapterContentBlockTtsNodeResponseModel:
    properties:
      voice_id: string
      text: string
    source:
      openapi: openapi.json
  ChapterContentInputModel:
    properties:
      blocks:
        type: list<ChapterContentBlockInputModel>
    source:
      openapi: openapi.json
  ChapterContentParagraphTtsNodeInputModel:
    properties:
      type:
        type: literal<"tts_node">
      text: string
      voice_id: string
    source:
      openapi: openapi.json
  ChapterContentResponseModel:
    properties:
      blocks:
        type: list<ChapterContentBlockResponseModel>
    source:
      openapi: openapi.json
  ChapterState:
    enum:
      - default
      - converting
    docs: The state of the chapter.
    inline: true
    source:
      openapi: openapi.json
  ChapterResponse:
    properties:
      chapter_id:
        type: string
        docs: The ID of the chapter.
      name:
        type: string
        docs: The name of the chapter.
      last_conversion_date_unix:
        type: optional<integer>
        docs: The last conversion date of the chapter.
      conversion_progress:
        type: optional<double>
        docs: The conversion progress of the chapter.
      can_be_downloaded:
        type: boolean
        docs: Whether the chapter can be downloaded.
      state:
        type: ChapterState
        docs: The state of the chapter.
      statistics:
        type: optional<ChapterStatisticsResponse>
        docs: The statistics of the chapter.
      last_conversion_error:
        type: optional<string>
        docs: The last conversion error of the chapter.
    source:
      openapi: openapi.json
  ChapterSnapshotExtendedResponseModel:
    properties:
      chapter_snapshot_id:
        type: string
        docs: The ID of the chapter snapshot.
      project_id:
        type: string
        docs: The ID of the project.
      chapter_id:
        type: string
        docs: The ID of the chapter.
      created_at_unix:
        type: integer
        docs: The creation date of the chapter snapshot.
      name:
        type: string
        docs: The name of the chapter snapshot.
      character_alignments:
        type: list<CharacterAlignmentModel>
    source:
      openapi: openapi.json
  ChapterSnapshotResponse:
    properties:
      chapter_snapshot_id:
        type: string
        docs: The ID of the chapter snapshot.
      project_id:
        type: string
        docs: The ID of the project.
      chapter_id:
        type: string
        docs: The ID of the chapter.
      created_at_unix:
        type: integer
        docs: The creation date of the chapter snapshot.
      name:
        type: string
        docs: The name of the chapter snapshot.
    source:
      openapi: openapi.json
  ChapterSnapshotsResponse:
    properties:
      snapshots:
        docs: List of chapter snapshots.
        type: list<ChapterSnapshotResponse>
    source:
      openapi: openapi.json
  ChapterStatisticsResponse:
    properties:
      characters_unconverted:
        type: integer
        docs: The number of unconverted characters.
      characters_converted:
        type: integer
        docs: The number of converted characters.
      paragraphs_converted:
        type: integer
        docs: The number of converted paragraphs.
      paragraphs_unconverted:
        type: integer
        docs: The number of unconverted paragraphs.
    source:
      openapi: openapi.json
  ChapterWithContentResponseModelState:
    enum:
      - default
      - converting
    docs: The state of the chapter.
    inline: true
    source:
      openapi: openapi.json
  ChapterWithContentResponseModel:
    properties:
      chapter_id:
        type: string
        docs: The ID of the chapter.
      name:
        type: string
        docs: The name of the chapter.
      last_conversion_date_unix:
        type: optional<integer>
        docs: The last conversion date of the chapter.
      conversion_progress:
        type: optional<double>
        docs: The conversion progress of the chapter.
      can_be_downloaded:
        type: boolean
        docs: Whether the chapter can be downloaded.
      state:
        type: ChapterWithContentResponseModelState
        docs: The state of the chapter.
      statistics:
        type: optional<ChapterStatisticsResponse>
        docs: The statistics of the chapter.
      last_conversion_error:
        type: optional<string>
        docs: The last conversion error of the chapter.
      content:
        type: ChapterContentResponseModel
    source:
      openapi: openapi.json
  CharacterAlignmentModel:
    properties:
      characters:
        type: list<string>
      character_start_times_seconds:
        type: list<double>
      character_end_times_seconds:
        type: list<double>
    source:
      openapi: openapi.json
  CharacterAlignmentResponseModel:
    properties:
      characters:
        type: list<string>
      character_start_times_seconds:
        type: list<double>
      character_end_times_seconds:
        type: list<double>
    source:
      openapi: openapi.json
  ClientEvent:
    enum:
      - conversation_initiation_metadata
      - asr_initiation_metadata
      - ping
      - audio
      - interruption
      - user_transcript
      - agent_response
      - agent_response_correction
      - client_tool_call
      - vad_score
      - internal_turn_probability
      - internal_tentative_agent_response
    source:
      openapi: openapi.json
  ClientToolConfigInput:
    docs: >-
      A client tool is one that sends an event to the user's client to trigger
      something client side
    properties:
      id:
        type: optional<string>
        default: ''
      name:
        type: string
        validation:
          pattern: ^[a-zA-Z0-9_-]{1,64}$
          minLength: 0
      description:
        type: string
        validation:
          minLength: 0
      response_timeout_secs:
        type: optional<integer>
        docs: >-
          The maximum time in seconds to wait for the tool call to complete.
          Must be between 1 and 30 seconds (inclusive).
        default: 20
        validation:
          min: 1
          max: 30
      parameters:
        type: optional<ObjectJsonSchemaPropertyInput>
        docs: Schema for any parameters to pass to the client
      expects_response:
        type: optional<boolean>
        docs: >-
          If true, calling this tool should block the conversation until the
          client responds with some response which is passed to the llm. If
          false then we will continue the conversation without waiting for the
          client to respond, this is useful to show content to a user but not
          block the conversation
        default: false
      dynamic_variables:
        type: optional<DynamicVariablesConfig>
        docs: Configuration for dynamic variables
    source:
      openapi: openapi.json
  ClientToolConfigOutput:
    docs: >-
      A client tool is one that sends an event to the user's client to trigger
      something client side
    properties:
      id:
        type: optional<string>
        default: ''
      name:
        type: string
        validation:
          pattern: ^[a-zA-Z0-9_-]{1,64}$
          minLength: 0
      description:
        type: string
        validation:
          minLength: 0
      response_timeout_secs:
        type: optional<integer>
        docs: >-
          The maximum time in seconds to wait for the tool call to complete.
          Must be between 1 and 30 seconds (inclusive).
        default: 20
        validation:
          min: 1
          max: 30
      parameters:
        type: optional<ObjectJsonSchemaPropertyOutput>
        docs: Schema for any parameters to pass to the client
      expects_response:
        type: optional<boolean>
        docs: >-
          If true, calling this tool should block the conversation until the
          client responds with some response which is passed to the llm. If
          false then we will continue the conversation without waiting for the
          client to respond, this is useful to show content to a user but not
          block the conversation
        default: false
      dynamic_variables:
        type: optional<DynamicVariablesConfig>
        docs: Configuration for dynamic variables
    source:
      openapi: openapi.json
  ConvAiSecretLocator:
    docs: Used to reference a secret from the agent's secret store.
    properties:
      secret_id: string
    source:
      openapi: openapi.json
  ConvAiStoredSecretDependenciesToolsItem:
    discriminant: type
    base-properties: {}
    union:
      available:
        type: DependentAvailableToolIdentifier
      unknown:
        type: DependentUnknownToolIdentifier
    source:
      openapi: openapi.json
  ConvAiStoredSecretDependenciesAgentToolsItem:
    discriminant: type
    base-properties: {}
    union:
      available:
        type: DependentAvailableAgentToolIdentifier
      unknown:
        type: DependentUnknownAgentToolIdentifier
    source:
      openapi: openapi.json
  ConvAiStoredSecretDependencies:
    properties:
      tools:
        type: list<ConvAiStoredSecretDependenciesToolsItem>
      agent_tools:
        type: list<ConvAiStoredSecretDependenciesAgentToolsItem>
      others:
        type: list<SecretDependencyType>
      phone_numbers:
        type: optional<list<DependentPhoneNumberIdentifier>>
    source:
      openapi: openapi.json
  ConvAiWebhooks:
    properties:
      post_call_webhook_id:
        type: optional<string>
    source:
      openapi: openapi.json
  ConvAiWorkspaceStoredSecretConfig:
    properties:
      type:
        type: literal<"stored">
      secret_id: string
      name: string
      used_by:
        type: ConvAiStoredSecretDependencies
    source:
      openapi: openapi.json
  ConversationChargingCommonModel:
    properties:
      dev_discount:
        type: optional<boolean>
        default: false
      tier:
        type: optional<string>
      llm_usage:
        type: optional<LlmCategoryUsage>
      llm_price:
        type: optional<double>
    source:
      openapi: openapi.json
  ConversationConfig:
    properties:
      max_duration_seconds:
        type: optional<integer>
        docs: The maximum duration of a conversation in seconds
        default: 600
      client_events:
        type: optional<list<ClientEvent>>
        docs: The events that will be sent to the client
    source:
      openapi: openapi.json
  ConversationConfigClientOverrideInput:
    properties:
      agent:
        type: optional<AgentConfigOverride>
        docs: The overrides for the agent configuration
      tts:
        type: optional<TtsConversationalConfigOverride>
        docs: The overrides for the TTS configuration
    source:
      openapi: openapi.json
  ConversationConfigClientOverrideOutput:
    properties:
      agent:
        type: optional<AgentConfigOverride>
        docs: The overrides for the agent configuration
      tts:
        type: optional<TtsConversationalConfigOverride>
        docs: The overrides for the TTS configuration
    source:
      openapi: openapi.json
  ConversationConfigClientOverrideConfigInput:
    properties:
      agent:
        type: optional<AgentConfigOverrideConfig>
        docs: Overrides for the agent configuration
      tts:
        type: optional<TtsConversationalConfigOverrideConfig>
        docs: Overrides for the TTS configuration
    source:
      openapi: openapi.json
  ConversationConfigClientOverrideConfigOutput:
    properties:
      agent:
        type: optional<AgentConfigOverrideConfig>
        docs: Overrides for the agent configuration
      tts:
        type: optional<TtsConversationalConfigOverrideConfig>
        docs: Overrides for the TTS configuration
    source:
      openapi: openapi.json
  ConversationDeletionSettings:
    properties:
      deletion_time_unix_secs:
        type: optional<integer>
      deleted_logs_at_time_unix_secs:
        type: optional<integer>
      deleted_audio_at_time_unix_secs:
        type: optional<integer>
      deleted_transcript_at_time_unix_secs:
        type: optional<integer>
      delete_transcript_and_pii:
        type: optional<boolean>
        default: false
      delete_audio:
        type: optional<boolean>
        default: false
    source:
      openapi: openapi.json
  ConversationHistoryAnalysisCommonModel:
    properties:
      evaluation_criteria_results:
        type: >-
          optional<map<string,
          ConversationHistoryEvaluationCriteriaResultCommonModel>>
      data_collection_results:
        type: optional<map<string, DataCollectionResultCommonModel>>
      call_successful:
        type: EvaluationSuccessResult
      transcript_summary: string
    source:
      openapi: openapi.json
  ConversationHistoryBatchCallModel:
    properties:
      batch_call_id: string
      batch_call_recipient_id: string
    source:
      openapi: openapi.json
  ConversationHistoryErrorCommonModel:
    properties:
      code: integer
      reason:
        type: optional<string>
    source:
      openapi: openapi.json
  ConversationHistoryEvaluationCriteriaResultCommonModel:
    properties:
      criteria_id: string
      result:
        type: EvaluationSuccessResult
      rationale: string
    source:
      openapi: openapi.json
  ConversationHistoryFeedbackCommonModel:
    properties:
      overall_score:
        type: optional<UserFeedbackScore>
      likes:
        type: optional<integer>
        default: 0
      dislikes:
        type: optional<integer>
        default: 0
    source:
      openapi: openapi.json
  ConversationHistoryMetadataCommonModelPhoneCall:
    discriminant: type
    base-properties: {}
    union:
      sip_trunking:
        type: ConversationHistorySipTrunkingPhoneCallModel
      twilio:
        type: ConversationHistoryTwilioPhoneCallModel
    source:
      openapi: openapi.json
  ConversationHistoryMetadataCommonModel:
    properties:
      start_time_unix_secs: integer
      accepted_time_unix_secs:
        type: optional<integer>
      call_duration_secs: integer
      cost:
        type: optional<integer>
      deletion_settings:
        type: optional<ConversationDeletionSettings>
      feedback:
        type: optional<ConversationHistoryFeedbackCommonModel>
      authorization_method:
        type: optional<AuthorizationMethod>
      charging:
        type: optional<ConversationChargingCommonModel>
      phone_call:
        type: optional<ConversationHistoryMetadataCommonModelPhoneCall>
      batch_call:
        type: optional<ConversationHistoryBatchCallModel>
      termination_reason:
        type: optional<string>
        default: ''
      error:
        type: optional<ConversationHistoryErrorCommonModel>
      main_language:
        type: optional<string>
      rag_usage:
        type: optional<ConversationHistoryRagUsageCommonModel>
    source:
      openapi: openapi.json
  ConversationHistoryRagUsageCommonModel:
    properties:
      usage_count: integer
      embedding_model: string
    source:
      openapi: openapi.json
  ConversationHistorySipTrunkingPhoneCallModelDirection:
    enum:
      - inbound
      - outbound
    inline: true
    source:
      openapi: openapi.json
  ConversationHistorySipTrunkingPhoneCallModel:
    properties:
      direction:
        type: ConversationHistorySipTrunkingPhoneCallModelDirection
      phone_number_id: string
      agent_number: string
      external_number: string
      call_sid: string
    source:
      openapi: openapi.json
  ConversationHistoryTranscriptCommonModelInputRole:
    enum:
      - user
      - agent
    inline: true
    source:
      openapi: openapi.json
  ConversationHistoryTranscriptCommonModelInput:
    properties:
      role:
        type: ConversationHistoryTranscriptCommonModelInputRole
      message:
        type: optional<string>
      tool_calls:
        type: optional<list<ConversationHistoryTranscriptToolCallCommonModel>>
      tool_results:
        type: optional<list<ConversationHistoryTranscriptToolResultCommonModel>>
      feedback:
        type: optional<UserFeedback>
      llm_override:
        type: optional<string>
      time_in_call_secs: integer
      conversation_turn_metrics:
        type: optional<ConversationTurnMetrics>
      rag_retrieval_info:
        type: optional<RagRetrievalInfo>
      llm_usage:
        type: optional<LlmUsageInput>
    source:
      openapi: openapi.json
  ConversationHistoryTranscriptCommonModelOutputRole:
    enum:
      - user
      - agent
    inline: true
    source:
      openapi: openapi.json
  ConversationHistoryTranscriptCommonModelOutput:
    properties:
      role:
        type: ConversationHistoryTranscriptCommonModelOutputRole
      message:
        type: optional<string>
      tool_calls:
        type: optional<list<ConversationHistoryTranscriptToolCallCommonModel>>
      tool_results:
        type: optional<list<ConversationHistoryTranscriptToolResultCommonModel>>
      feedback:
        type: optional<UserFeedback>
      llm_override:
        type: optional<string>
      time_in_call_secs: integer
      conversation_turn_metrics:
        type: optional<ConversationTurnMetrics>
      rag_retrieval_info:
        type: optional<RagRetrievalInfo>
      llm_usage:
        type: optional<LlmUsageOutput>
    source:
      openapi: openapi.json
  ConversationHistoryTranscriptToolCallClientDetails:
    properties:
      parameters: string
    source:
      openapi: openapi.json
  ConversationHistoryTranscriptToolCallCommonModelToolDetails:
    discriminant: type
    base-properties: {}
    union:
      client:
        type: ConversationHistoryTranscriptToolCallClientDetails
      webhook:
        type: ConversationHistoryTranscriptToolCallWebhookDetails
    source:
      openapi: openapi.json
  ConversationHistoryTranscriptToolCallCommonModel:
    properties:
      type:
        type: optional<string>
      request_id: string
      tool_name: string
      params_as_json: string
      tool_has_been_called: boolean
      tool_details:
        type: optional<ConversationHistoryTranscriptToolCallCommonModelToolDetails>
    source:
      openapi: openapi.json
  ConversationHistoryTranscriptToolCallWebhookDetails:
    properties:
      method: string
      url: string
      headers:
        type: optional<map<string, string>>
      path_params:
        type: optional<map<string, string>>
      query_params:
        type: optional<map<string, string>>
      body:
        type: optional<string>
    source:
      openapi: openapi.json
  ConversationHistoryTranscriptToolResultCommonModel:
    properties:
      type:
        type: optional<string>
      request_id: string
      tool_name: string
      result_value: string
      is_error: boolean
      tool_has_been_called: boolean
      tool_latency_secs:
        type: optional<double>
        default: 0
    source:
      openapi: openapi.json
  ConversationHistoryTwilioPhoneCallModelDirection:
    enum:
      - inbound
      - outbound
    inline: true
    source:
      openapi: openapi.json
  ConversationHistoryTwilioPhoneCallModel:
    properties:
      direction:
        type: ConversationHistoryTwilioPhoneCallModelDirection
      phone_number_id: string
      agent_number: string
      external_number: string
      stream_sid: string
      call_sid: string
    source:
      openapi: openapi.json
  ConversationInitiationClientDataConfigInput:
    properties:
      conversation_config_override:
        type: optional<ConversationConfigClientOverrideConfigInput>
        docs: Overrides for the conversation configuration
      custom_llm_extra_body:
        type: optional<boolean>
        docs: Whether to include custom LLM extra body
        default: false
      enable_conversation_initiation_client_data_from_webhook:
        type: optional<boolean>
        docs: Whether to enable conversation initiation client data from webhooks
        default: false
    source:
      openapi: openapi.json
  ConversationInitiationClientDataConfigOutput:
    properties:
      conversation_config_override:
        type: optional<ConversationConfigClientOverrideConfigOutput>
        docs: Overrides for the conversation configuration
      custom_llm_extra_body:
        type: optional<boolean>
        docs: Whether to include custom LLM extra body
        default: false
      enable_conversation_initiation_client_data_from_webhook:
        type: optional<boolean>
        docs: Whether to enable conversation initiation client data from webhooks
        default: false
    source:
      openapi: openapi.json
  ConversationInitiationClientDataInternalDynamicVariablesValue:
    discriminated: false
    union:
      - string
      - double
      - integer
      - boolean
    source:
      openapi: openapi.json
    inline: true
  ConversationInitiationClientDataInternal:
    properties:
      conversation_config_override:
        type: optional<ConversationConfigClientOverrideOutput>
      custom_llm_extra_body:
        type: optional<map<string, unknown>>
      dynamic_variables:
        type: >-
          optional<map<string,
          optional<ConversationInitiationClientDataInternalDynamicVariablesValue>>>
    source:
      openapi: openapi.json
  ConversationInitiationClientDataRequestInputDynamicVariablesValue:
    discriminated: false
    union:
      - string
      - double
      - integer
      - boolean
    source:
      openapi: openapi.json
    inline: true
  ConversationInitiationClientDataRequestInput:
    properties:
      conversation_config_override:
        type: optional<ConversationConfigClientOverrideInput>
      custom_llm_extra_body:
        type: optional<map<string, unknown>>
      dynamic_variables:
        type: >-
          optional<map<string,
          optional<ConversationInitiationClientDataRequestInputDynamicVariablesValue>>>
    source:
      openapi: openapi.json
  ConversationInitiationClientDataRequestOutputDynamicVariablesValue:
    discriminated: false
    union:
      - string
      - double
      - integer
      - boolean
    source:
      openapi: openapi.json
    inline: true
  ConversationInitiationClientDataRequestOutput:
    properties:
      conversation_config_override:
        type: optional<ConversationConfigClientOverrideOutput>
      custom_llm_extra_body:
        type: optional<map<string, unknown>>
      dynamic_variables:
        type: >-
          optional<map<string,
          optional<ConversationInitiationClientDataRequestOutputDynamicVariablesValue>>>
    source:
      openapi: openapi.json
  ConversationInitiationClientDataWebhookRequestHeadersValue:
    discriminated: false
    union:
      - string
      - type: ConvAiSecretLocator
    source:
      openapi: openapi.json
    inline: true
  ConversationInitiationClientDataWebhook:
    properties:
      url:
        type: string
        docs: The URL to send the webhook to
      request_headers:
        type: >-
          map<string,
          ConversationInitiationClientDataWebhookRequestHeadersValue>
        docs: The headers to send with the webhook request
    source:
      openapi: openapi.json
  ConversationSignedUrlResponseModel:
    properties:
      signed_url: string
    source:
      openapi: openapi.json
  ConversationSimulationSpecification:
    docs: >-
      A specification that will be used to simulate a conversation between an
      agent and an AI user.
    properties:
      simulated_user_config:
        type: AgentConfigDbModel
      tool_mock_config:
        type: optional<map<string, ToolMockConfig>>
      partial_conversation_history:
        type: optional<list<ConversationHistoryTranscriptCommonModelInput>>
        docs: >-
          A partial conversation history to start the simulation from. If empty,
          simulation starts fresh.
    source:
      openapi: openapi.json
  ConversationSummaryResponseModelStatus:
    enum:
      - value: in-progress
        name: InProgress
      - processing
      - done
      - failed
    inline: true
    source:
      openapi: openapi.json
  ConversationSummaryResponseModel:
    properties:
      agent_id: string
      agent_name:
        type: optional<string>
      conversation_id: string
      start_time_unix_secs: integer
      call_duration_secs: integer
      message_count: integer
      status:
        type: ConversationSummaryResponseModelStatus
      call_successful:
        type: EvaluationSuccessResult
    source:
      openapi: openapi.json
  ConversationTokenDbModel:
    properties:
      agent_id:
        type: string
        docs: The ID of the agent
      conversation_token:
        type: string
        docs: The token for the agent
      expiration_time_unix_secs:
        type: optional<integer>
        docs: The expiration time of the token in unix seconds
      purpose:
        type: optional<ConversationTokenPurpose>
        docs: The purpose of the token
    source:
      openapi: openapi.json
  ConversationTokenPurpose:
    enum:
      - signed_url
      - shareable_link
    source:
      openapi: openapi.json
  ConversationTurnMetrics:
    properties:
      metrics:
        type: optional<map<string, MetricRecord>>
    source:
      openapi: openapi.json
  ConversationalConfig:
    properties:
      asr:
        type: optional<AsrConversationalConfig>
        docs: Configuration for conversational transcription
      turn:
        type: optional<TurnConfig>
        docs: Configuration for turn detection
      tts:
        type: optional<TtsConversationalConfig>
        docs: Configuration for conversational text to speech
      conversation:
        type: optional<ConversationConfig>
        docs: Configuration for conversational events
      language_presets:
        type: optional<map<string, LanguagePresetOutput>>
        docs: Language presets for conversations
      agent:
        type: optional<AgentConfig>
        docs: Agent specific configuration
    source:
      openapi: openapi.json
  ConvertChapterResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the studio chapter conversion request. If the request
          was successful, the status will be 'ok'. Otherwise an error message
          with status 500 will be returned.
    source:
      openapi: openapi.json
  ConvertProjectResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the studio project conversion request. If the request
          was successful, the status will be 'ok'. Otherwise an error message
          with status 500 will be returned.
    source:
      openapi: openapi.json
  CreateAgentResponseModel:
    properties:
      agent_id:
        type: string
        docs: ID of the created agent
    source:
      openapi: openapi.json
  CreatePhoneNumberResponseModel:
    properties:
      phone_number_id:
        type: string
        docs: Phone entity ID
    source:
      openapi: openapi.json
  CreatePronunciationDictionaryResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the create pronunciation dictionary request. If the
          request was successful, the status will be 'ok'. Otherwise an error
          message with status 500 will be returned.
    source:
      openapi: openapi.json
  CreateSipTrunkPhoneNumberRequest:
    docs: >-
      SIP trunk phone number request


      Includes termination URI and optional digest authentication credentials.

      If credentials are provided, both username and password must be included.

      If credentials are not provided, ACL authentication is assumed. (user
      needs to add our ips in their settings)
    properties:
      phone_number:
        type: string
        docs: Phone number
      label:
        type: string
        docs: Label for the phone number
      termination_uri:
        type: string
        docs: SIP trunk termination URI
      address:
        type: optional<string>
        docs: Hostname or IP the SIP INVITE is sent to.
      transport:
        type: optional<SipTrunkTransportEnum>
        docs: Protocol to use for SIP transport (signalling layer).
      media_encryption:
        type: optional<SipMediaEncryptionEnum>
        docs: Whether or not to encrypt media (data layer).
      headers:
        type: optional<map<string, string>>
        docs: >-
          SIP X-* headers for INVITE request. These headers are sent as-is and
          may help identify this call.
      credentials:
        type: optional<SipTrunkCredentials>
        docs: >-
          Optional digest authentication credentials (username/password). If not
          provided, ACL authentication is assumed.
    source:
      openapi: openapi.json
  CreateTwilioPhoneNumberRequest:
    properties:
      phone_number:
        type: string
        docs: Phone number
      label:
        type: string
        docs: Label for the phone number
      sid:
        type: string
        docs: Twilio Account SID
      token:
        type: string
        docs: Twilio Auth Token
    source:
      openapi: openapi.json
  CustomLlm:
    properties:
      url:
        type: string
        docs: The URL of the Chat Completions compatible endpoint
      model_id:
        type: optional<string>
        docs: The model ID to be used if URL serves multiple models
      api_key:
        type: optional<ConvAiSecretLocator>
        docs: The API key for authentication
    source:
      openapi: openapi.json
  DashboardCallSuccessChartModel:
    properties:
      name: string
    source:
      openapi: openapi.json
  DashboardCriteriaChartModel:
    properties:
      name: string
      criteria_id: string
    source:
      openapi: openapi.json
  DashboardDataCollectionChartModel:
    properties:
      name: string
      data_collection_id: string
    source:
      openapi: openapi.json
  DataCollectionResultCommonModel:
    properties:
      data_collection_id: string
      value:
        type: optional<unknown>
      json_schema:
        type: optional<LiteralJsonSchemaProperty>
      rationale: string
    source:
      openapi: openapi.json
  DeleteChapterResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the studio chapter deletion request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  DeleteDubbingResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the dubbing project. If the request was successful, the
          status will be 'ok'. Otherwise an error message with status 500 will
          be returned.
    source:
      openapi: openapi.json
  DeleteHistoryItemResponse:
    properties:
      status:
        type: string
        docs: >-
          The status of the deletion request. If the request was successful, the
          status will be 'ok'. Otherwise an error message with http code 500
          will be returned.
    source:
      openapi: openapi.json
  DeleteProjectResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the studio project deletion request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  DeleteSampleResponse:
    properties:
      status:
        type: string
        docs: >-
          The status of the sample deletion request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  DeleteVoiceResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the voice deletion request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  DeleteVoiceSampleResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the voice sample deletion request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  DeleteWorkspaceGroupMemberResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the workspace group member deletion request. If the
          request was successful, the status will be 'ok'. Otherwise an error
          message with status 500 will be returned.
    source:
      openapi: openapi.json
  DeleteWorkspaceInviteResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the workspace invite deletion request. If the request
          was successful, the status will be 'ok'. Otherwise an error message
          with status 500 will be returned.
    source:
      openapi: openapi.json
  DeleteWorkspaceMemberResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the workspace member deletion request. If the request
          was successful, the status will be 'ok'. Otherwise an error message
          with status 500 will be returned.
    source:
      openapi: openapi.json
  DependentAvailableAgentIdentifierAccessLevel:
    enum:
      - admin
      - editor
      - viewer
    inline: true
    source:
      openapi: openapi.json
  DependentAvailableAgentIdentifier:
    properties:
      id: string
      name: string
      created_at_unix_secs: integer
      access_level:
        type: DependentAvailableAgentIdentifierAccessLevel
    source:
      openapi: openapi.json
  DependentAvailableAgentToolIdentifierAccessLevel:
    enum:
      - admin
      - editor
      - viewer
    inline: true
    source:
      openapi: openapi.json
  DependentAvailableAgentToolIdentifier:
    properties:
      agent_id: string
      agent_name: string
      used_by:
        type: list<string>
      created_at_unix_secs: integer
      access_level:
        type: DependentAvailableAgentToolIdentifierAccessLevel
    source:
      openapi: openapi.json
  DependentAvailableToolIdentifierAccessLevel:
    enum:
      - admin
      - editor
      - viewer
    inline: true
    source:
      openapi: openapi.json
  DependentAvailableToolIdentifier:
    properties:
      id: string
      name: string
      created_at_unix_secs: integer
      access_level:
        type: DependentAvailableToolIdentifierAccessLevel
    source:
      openapi: openapi.json
  DependentPhoneNumberIdentifier:
    properties:
      phone_number_id: string
      phone_number: string
      label: string
      provider:
        type: TelephonyProvider
    source:
      openapi: openapi.json
  DependentUnknownAgentIdentifier:
    docs: |-
      A model that represents an agent dependent on a knowledge base/tools
      to which the user has no direct access.
    properties: {}
    source:
      openapi: openapi.json
  DependentUnknownAgentToolIdentifier:
    docs: |-
      A model that represents an tool dependent on a knowledge base/tools
      to which the user has no direct access.
    properties: {}
    source:
      openapi: openapi.json
  DependentUnknownToolIdentifier:
    docs: |-
      A model that represents an tool dependent on a knowledge base/tools
      to which the user has no direct access.
    properties: {}
    source:
      openapi: openapi.json
  DoDubbingResponse:
    properties:
      dubbing_id:
        type: string
        docs: The ID of the dubbing project.
      expected_duration_sec:
        type: double
        docs: The expected duration of the dubbing project in seconds.
    source:
      openapi: openapi.json
  DocumentUsageModeEnum:
    enum:
      - prompt
      - auto
    source:
      openapi: openapi.json
  DocxExportOptions:
    properties:
      include_speakers:
        type: optional<boolean>
        default: true
      include_timestamps:
        type: optional<boolean>
        default: true
      segment_on_silence_longer_than_s:
        type: optional<double>
      max_segment_duration_s:
        type: optional<double>
      max_segment_chars:
        type: optional<integer>
    source:
      openapi: openapi.json
  DubbedSegment:
    properties:
      start_time: double
      end_time: double
      text: optional<string>
      audio_stale: boolean
      media_ref: optional<DubbingMediaReference>
    source:
      openapi: openapi.json
  DubbingMediaMetadata:
    properties:
      content_type:
        type: string
        docs: The content type of the media.
      duration:
        type: double
        docs: The duration of the media in seconds.
    source:
      openapi: openapi.json
  DubbingMediaReference:
    properties:
      src: string
      content_type: string
      bucket_name: string
      random_path_slug: string
      duration_secs: double
      is_audio: boolean
      url: string
    source:
      openapi: openapi.json
  DubbingMetadataResponse:
    properties:
      dubbing_id:
        type: string
        docs: The ID of the dubbing project.
      name:
        type: string
        docs: The name of the dubbing project.
      status:
        type: string
        docs: >-
          The status of the dubbing project. Either 'dubbed', 'dubbing' or
          'failed'.
      target_languages:
        docs: The target languages of the dubbing project.
        type: list<string>
      media_metadata:
        type: optional<DubbingMediaMetadata>
        docs: The media metadata of the dubbing project.
      error:
        type: optional<string>
        docs: Optional error message if the dubbing project failed.
    source:
      openapi: openapi.json
  DubbingRenderResponseModel:
    properties:
      version: integer
      render_id: string
    source:
      openapi: openapi.json
  DubbingResource:
    properties:
      id: string
      version: integer
      source_language: string
      target_languages:
        type: list<string>
      input:
        type: DubbingMediaReference
      background:
        type: DubbingMediaReference
      foreground:
        type: DubbingMediaReference
      speaker_tracks:
        type: map<string, SpeakerTrack>
      speaker_segments:
        type: map<string, SpeakerSegment>
      renders:
        type: map<string, Render>
    source:
      openapi: openapi.json
  DynamicVariablesConfigDynamicVariablePlaceholdersValue:
    discriminated: false
    union:
      - string
      - double
      - integer
      - boolean
    source:
      openapi: openapi.json
    inline: true
  DynamicVariablesConfig:
    properties:
      dynamic_variable_placeholders:
        type: >-
          optional<map<string,
          DynamicVariablesConfigDynamicVariablePlaceholdersValue>>
        docs: A dictionary of dynamic variable placeholders and their values
    source:
      openapi: openapi.json
  EditChapterResponseModel:
    properties:
      chapter:
        type: ChapterWithContentResponseModel
    source:
      openapi: openapi.json
  EditProjectResponseModel:
    properties:
      project:
        type: ProjectResponse
    source:
      openapi: openapi.json
  EditVoiceResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the voice edit request. If the request was successful,
          the status will be 'ok'. Otherwise an error message with status 500
          will be returned.
    source:
      openapi: openapi.json
  EditVoiceSettingsResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the voice settings edit request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  EmbedVariant:
    enum:
      - compact
      - full
      - expandable
    source:
      openapi: openapi.json
  EmbeddingModelEnum:
    enum:
      - e5_mistral_7b_instruct
      - multilingual_e5_large_instruct
    source:
      openapi: openapi.json
  EndCallToolConfig:
    properties: {}
    source:
      openapi: openapi.json
  EvaluationSettings:
    docs: >-
      Settings to evaluate an agent's performance.

      Agents are evaluated against a set of criteria, with success being defined
      as meeting some combination of those criteria.
    properties:
      criteria:
        type: optional<list<PromptEvaluationCriteria>>
        docs: Individual criteria that the agent should be evaluated against
    source:
      openapi: openapi.json
  EvaluationSuccessResult:
    enum:
      - success
      - failure
      - unknown
    source:
      openapi: openapi.json
  ExportOptions:
    discriminant: format
    base-properties: {}
    union:
      docx:
        type: DocxExportOptions
      html:
        type: HtmlExportOptions
      pdf:
        type: PdfExportOptions
      segmented_json:
        type: SegmentedJsonExportOptions
      srt:
        type: SrtExportOptions
      txt:
        type: TxtExportOptions
    source:
      openapi: openapi.json
  ExtendedSubscriptionResponseModelCurrency:
    enum:
      - usd
      - eur
    inline: true
    source:
      openapi: openapi.json
  ExtendedSubscriptionResponseModelStatus:
    enum:
      - trialing
      - active
      - incomplete
      - incomplete_expired
      - past_due
      - canceled
      - unpaid
      - free
    docs: The status of the user's subscription.
    inline: true
    source:
      openapi: openapi.json
  ExtendedSubscriptionResponseModelBillingPeriod:
    enum:
      - monthly_period
      - annual_period
    inline: true
    source:
      openapi: openapi.json
  ExtendedSubscriptionResponseModelCharacterRefreshPeriod:
    enum:
      - monthly_period
      - annual_period
    inline: true
    source:
      openapi: openapi.json
  Subscription:
    properties:
      tier:
        type: string
        docs: The tier of the user's subscription.
      character_count:
        type: integer
        docs: The number of characters used by the user.
      character_limit:
        type: integer
        docs: >-
          The maximum number of characters allowed in the current billing
          period.
      max_character_limit_extension:
        type: optional<integer>
        docs: >-
          Maximum number of characters that the character limit can be exceeded
          by. Managed by the workspace admin.
      can_extend_character_limit:
        type: boolean
        docs: Whether the user can extend their character limit.
      allowed_to_extend_character_limit:
        type: boolean
        docs: Whether the user is allowed to extend their character limit.
      next_character_count_reset_unix:
        type: optional<integer>
        docs: The Unix timestamp of the next character count reset.
      voice_slots_used:
        type: integer
        docs: The number of voice slots used by the user.
      professional_voice_slots_used:
        type: integer
        docs: >-
          The number of professional voice slots used by the workspace/user if
          single seat.
      voice_limit:
        type: integer
        docs: The maximum number of voice slots allowed for the user.
      max_voice_add_edits:
        type: optional<integer>
        docs: The maximum number of voice add/edits allowed for the user.
      voice_add_edit_counter:
        type: integer
        docs: The number of voice add/edits used by the user.
      professional_voice_limit:
        type: integer
        docs: The maximum number of professional voices allowed for the user.
      can_extend_voice_limit:
        type: boolean
        docs: Whether the user can extend their voice limit.
      can_use_instant_voice_cloning:
        type: boolean
        docs: Whether the user can use instant voice cloning.
      can_use_professional_voice_cloning:
        type: boolean
        docs: Whether the user can use professional voice cloning.
      currency:
        type: optional<ExtendedSubscriptionResponseModelCurrency>
        docs: The currency of the user's subscription.
      status:
        type: ExtendedSubscriptionResponseModelStatus
        docs: The status of the user's subscription.
      billing_period:
        type: optional<ExtendedSubscriptionResponseModelBillingPeriod>
        docs: The billing period of the user's subscription.
      character_refresh_period:
        type: optional<ExtendedSubscriptionResponseModelCharacterRefreshPeriod>
        docs: The character refresh period of the user's subscription.
      next_invoice:
        type: optional<InvoiceResponse>
        docs: The next invoice for the user.
      has_open_invoices:
        type: boolean
        docs: Whether the user has open invoices.
    source:
      openapi: openapi.json
  FeedbackItem:
    properties:
      thumbs_up:
        type: boolean
        docs: Whether the user liked the generated item.
      feedback:
        type: string
        docs: The feedback text provided by the user.
      emotions:
        type: boolean
        docs: Whether the user provided emotions.
      inaccurate_clone:
        type: boolean
        docs: Whether the user thinks the clone is inaccurate.
      glitches:
        type: boolean
        docs: Whether the user thinks there are glitches in the audio.
      audio_quality:
        type: boolean
        docs: Whether the user thinks the audio quality is good.
      other:
        type: boolean
        docs: Whether the user provided other feedback.
      review_status:
        type: optional<string>
        docs: The review status of the item. Defaults to 'not_reviewed'.
        default: not_reviewed
    source:
      openapi: openapi.json
  FineTuningResponseModelStateValue:
    enum:
      - not_started
      - queued
      - fine_tuning
      - fine_tuned
      - failed
      - delayed
    inline: true
    source:
      openapi: openapi.json
  FineTuningResponse:
    properties:
      is_allowed_to_fine_tune:
        type: optional<boolean>
        docs: Whether the user is allowed to fine-tune the voice.
      state:
        type: optional<map<string, FineTuningResponseModelStateValue>>
        docs: The state of the fine-tuning process for each model.
      verification_failures:
        type: optional<list<string>>
        docs: List of verification failures in the fine-tuning process.
      verification_attempts_count:
        type: optional<integer>
        docs: The number of verification attempts in the fine-tuning process.
      manual_verification_requested:
        type: optional<boolean>
        docs: >-
          Whether a manual verification was requested for the fine-tuning
          process.
      language:
        type: optional<string>
        docs: The language of the fine-tuning process.
      progress:
        type: optional<map<string, optional<double>>>
        docs: The progress of the fine-tuning process.
      message:
        type: optional<map<string, optional<string>>>
        docs: The message of the fine-tuning process.
      dataset_duration_seconds:
        type: optional<double>
        docs: The duration of the dataset in seconds.
      verification_attempts:
        type: optional<list<VerificationAttemptResponse>>
        docs: The number of verification attempts.
      slice_ids:
        type: optional<list<string>>
        docs: List of slice IDs.
      manual_verification:
        type: optional<ManualVerificationResponse>
        docs: The manual verification of the fine-tuning process.
      max_verification_attempts:
        type: optional<integer>
        docs: The maximum number of verification attempts.
      next_max_verification_attempts_reset_unix_ms:
        type: optional<integer>
        docs: >-
          The next maximum verification attempts reset time in Unix
          milliseconds.
      finetuning_state:
        type: optional<unknown>
    source:
      openapi: openapi.json
  ForcedAlignmentCharacterResponseModel:
    docs: >-
      Model representing a single character with its timing information from the
      aligner.
    properties:
      text:
        type: string
        docs: The character that was transcribed.
      start:
        type: double
        docs: The start time of the character in seconds.
      end:
        type: double
        docs: The end time of the character in seconds.
    source:
      openapi: openapi.json
  ForcedAlignmentResponseModel:
    docs: Model representing the response from the aligner service.
    properties:
      characters:
        docs: List of characters with their timing information.
        type: list<ForcedAlignmentCharacterResponseModel>
      words:
        docs: List of words with their timing information.
        type: list<ForcedAlignmentWordResponseModel>
    source:
      openapi: openapi.json
  ForcedAlignmentWordResponseModel:
    docs: >-
      Model representing a single word with its timing information from the
      aligner.
    properties:
      text:
        type: string
        docs: The word that was transcribed.
      start:
        type: double
        docs: The start time of the word in seconds.
      end:
        type: double
        docs: The end time of the word in seconds.
    source:
      openapi: openapi.json
  GetAgentEmbedResponseModel:
    properties:
      agent_id: string
      widget_config:
        type: WidgetConfigResponseModel
    source:
      openapi: openapi.json
  GetAgentLinkResponseModel:
    properties:
      agent_id:
        type: string
        docs: The ID of the agent
      token:
        type: optional<ConversationTokenDbModel>
        docs: The token data for the agent
    source:
      openapi: openapi.json
  GetAgentResponseModelPhoneNumbersItem:
    discriminant: provider
    base-properties: {}
    union:
      sip_trunk:
        type: GetPhoneNumberSipTrunkResponseModel
      twilio:
        type: GetPhoneNumberTwilioResponseModel
    source:
      openapi: openapi.json
  GetAgentResponseModel:
    properties:
      agent_id:
        type: string
        docs: The ID of the agent
      name:
        type: string
        docs: The name of the agent
      conversation_config:
        type: ConversationalConfig
        docs: The conversation configuration of the agent
      metadata:
        type: AgentMetadataResponseModel
        docs: The metadata of the agent
      platform_settings:
        type: optional<AgentPlatformSettingsResponseModel>
        docs: The platform settings of the agent
      phone_numbers:
        type: optional<list<GetAgentResponseModelPhoneNumbersItem>>
        docs: The phone numbers of the agent
      access_info:
        type: optional<ResourceAccessInfo>
        docs: The access information of the agent for the user
      tags:
        type: optional<list<string>>
        docs: Agent tags used to categorize the agent
    source:
      openapi: openapi.json
  GetAgentsPageResponseModel:
    properties:
      agents:
        docs: A list of agents and their metadata
        type: list<AgentSummaryResponseModel>
      next_cursor:
        type: optional<string>
        docs: The next cursor to paginate through the agents
      has_more:
        type: boolean
        docs: Whether there are more agents to paginate through
    source:
      openapi: openapi.json
  GetAudioNativeProjectSettingsResponseModel:
    properties:
      enabled:
        type: boolean
        docs: Whether the project is enabled.
      snapshot_id:
        type: optional<string>
        docs: The ID of the latest snapshot of the project.
      settings:
        type: optional<AudioNativeProjectSettingsResponseModel>
        docs: The settings of the project.
    source:
      openapi: openapi.json
  GetChaptersResponse:
    properties:
      chapters:
        type: list<ChapterResponse>
    source:
      openapi: openapi.json
  GetConvAiDashboardSettingsResponseModelChartsItem:
    discriminant: type
    base-properties: {}
    union:
      call_success:
        type: DashboardCallSuccessChartModel
      criteria:
        type: DashboardCriteriaChartModel
      data_collection:
        type: DashboardDataCollectionChartModel
    source:
      openapi: openapi.json
  GetConvAiDashboardSettingsResponseModel:
    properties:
      charts:
        type: optional<list<GetConvAiDashboardSettingsResponseModelChartsItem>>
    source:
      openapi: openapi.json
  GetConvAiSettingsResponseModel:
    properties:
      conversation_initiation_client_data_webhook:
        type: optional<ConversationInitiationClientDataWebhook>
      webhooks:
        type: optional<ConvAiWebhooks>
      rag_retention_period_days:
        type: optional<integer>
        default: 10
        validation:
          max: 30
    source:
      openapi: openapi.json
  GetConversationResponseModelStatus:
    enum:
      - value: in-progress
        name: InProgress
      - processing
      - done
      - failed
    inline: true
    source:
      openapi: openapi.json
  GetConversationResponseModel:
    properties:
      agent_id: string
      conversation_id: string
      status:
        type: GetConversationResponseModelStatus
      transcript:
        type: list<ConversationHistoryTranscriptCommonModelOutput>
      metadata:
        type: ConversationHistoryMetadataCommonModel
      analysis:
        type: optional<ConversationHistoryAnalysisCommonModel>
      conversation_initiation_client_data:
        type: optional<ConversationInitiationClientDataRequestOutput>
      has_audio: boolean
      has_user_audio: boolean
      has_response_audio: boolean
    source:
      openapi: openapi.json
  GetConversationsPageResponseModel:
    properties:
      conversations:
        type: list<ConversationSummaryResponseModel>
      next_cursor:
        type: optional<string>
      has_more: boolean
    source:
      openapi: openapi.json
  GetKnowledgeBaseDependentAgentsResponseModelAgentsItem:
    discriminant: type
    base-properties: {}
    union:
      available:
        type: DependentAvailableAgentIdentifier
      unknown:
        type: DependentUnknownAgentIdentifier
    source:
      openapi: openapi.json
  GetKnowledgeBaseDependentAgentsResponseModel:
    properties:
      agents:
        type: list<GetKnowledgeBaseDependentAgentsResponseModelAgentsItem>
      next_cursor:
        type: optional<string>
      has_more: boolean
    source:
      openapi: openapi.json
  GetKnowledgeBaseFileResponseModel:
    properties:
      id: string
      name: string
      metadata:
        type: KnowledgeBaseDocumentMetadataResponseModel
      prompt_injectable: boolean
      access_info:
        type: ResourceAccessInfo
      extracted_inner_html: string
    source:
      openapi: openapi.json
  GetKnowledgeBaseListResponseModelDocumentsItem:
    discriminant: type
    base-properties: {}
    union:
      file:
        type: GetKnowledgeBaseSummaryFileResponseModel
      text:
        type: GetKnowledgeBaseSummaryTextResponseModel
      url:
        type: GetKnowledgeBaseSummaryUrlResponseModel
    source:
      openapi: openapi.json
  GetKnowledgeBaseListResponseModel:
    properties:
      documents:
        type: list<GetKnowledgeBaseListResponseModelDocumentsItem>
      next_cursor:
        type: optional<string>
      has_more: boolean
    source:
      openapi: openapi.json
  GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem:
    discriminant: type
    base-properties: {}
    union:
      available:
        type: DependentAvailableAgentIdentifier
      unknown:
        type: DependentUnknownAgentIdentifier
    source:
      openapi: openapi.json
  GetKnowledgeBaseSummaryFileResponseModel:
    properties:
      id: string
      name: string
      metadata:
        type: KnowledgeBaseDocumentMetadataResponseModel
      prompt_injectable: boolean
      access_info:
        type: ResourceAccessInfo
      dependent_agents:
        type: list<GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem>
    source:
      openapi: openapi.json
  GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem:
    discriminant: type
    base-properties: {}
    union:
      available:
        type: DependentAvailableAgentIdentifier
      unknown:
        type: DependentUnknownAgentIdentifier
    source:
      openapi: openapi.json
  GetKnowledgeBaseSummaryTextResponseModel:
    properties:
      id: string
      name: string
      metadata:
        type: KnowledgeBaseDocumentMetadataResponseModel
      prompt_injectable: boolean
      access_info:
        type: ResourceAccessInfo
      dependent_agents:
        type: list<GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem>
    source:
      openapi: openapi.json
  GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem:
    discriminant: type
    base-properties: {}
    union:
      available:
        type: DependentAvailableAgentIdentifier
      unknown:
        type: DependentUnknownAgentIdentifier
    source:
      openapi: openapi.json
  GetKnowledgeBaseSummaryUrlResponseModel:
    properties:
      id: string
      name: string
      metadata:
        type: KnowledgeBaseDocumentMetadataResponseModel
      prompt_injectable: boolean
      access_info:
        type: ResourceAccessInfo
      dependent_agents:
        type: list<GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem>
      url: string
    source:
      openapi: openapi.json
  GetKnowledgeBaseTextResponseModel:
    properties:
      id: string
      name: string
      metadata:
        type: KnowledgeBaseDocumentMetadataResponseModel
      prompt_injectable: boolean
      access_info:
        type: ResourceAccessInfo
      extracted_inner_html: string
    source:
      openapi: openapi.json
  GetKnowledgeBaseUrlResponseModel:
    properties:
      id: string
      name: string
      metadata:
        type: KnowledgeBaseDocumentMetadataResponseModel
      prompt_injectable: boolean
      access_info:
        type: ResourceAccessInfo
      extracted_inner_html: string
      url: string
    source:
      openapi: openapi.json
  GetLibraryVoicesResponse:
    properties:
      voices:
        docs: The list of shared voices
        type: list<LibraryVoiceResponse>
      has_more:
        type: boolean
        docs: Whether there are more shared voices in subsequent pages.
      last_sort_id:
        type: optional<string>
    source:
      openapi: openapi.json
  GetPhoneNumberSipTrunkResponseModel:
    properties:
      phone_number:
        type: string
        docs: Phone number
      label:
        type: string
        docs: Label for the phone number
      phone_number_id:
        type: string
        docs: The ID of the phone number
      assigned_agent:
        type: optional<PhoneNumberAgentInfo>
        docs: The agent that is assigned to the phone number
      provider_config:
        type: optional<SipTrunkConfigResponseModel>
    source:
      openapi: openapi.json
  GetPhoneNumberTwilioResponseModel:
    properties:
      phone_number:
        type: string
        docs: Phone number
      label:
        type: string
        docs: Label for the phone number
      phone_number_id:
        type: string
        docs: The ID of the phone number
      assigned_agent:
        type: optional<PhoneNumberAgentInfo>
        docs: The agent that is assigned to the phone number
    source:
      openapi: openapi.json
  GetProjectsResponse:
    properties:
      projects:
        docs: A list of projects with their metadata.
        type: list<ProjectResponse>
    source:
      openapi: openapi.json
  GetPronunciationDictionariesMetadataResponseModel:
    properties:
      pronunciation_dictionaries:
        docs: A list of pronunciation dictionaries and their metadata.
        type: list<GetPronunciationDictionaryMetadataResponse>
      next_cursor:
        type: optional<string>
        docs: The next cursor to use for pagination.
      has_more:
        type: boolean
        docs: Whether there are more pronunciation dictionaries to fetch.
    source:
      openapi: openapi.json
  GetPronunciationDictionaryMetadataResponseModelPermissionOnResource:
    enum:
      - admin
      - editor
      - viewer
    inline: true
    source:
      openapi: openapi.json
  GetPronunciationDictionaryMetadataResponse:
    properties:
      id:
        type: string
        docs: The ID of the pronunciation dictionary.
      latest_version_id:
        type: string
        docs: The ID of the latest version of the pronunciation dictionary.
      latest_version_rules_num:
        type: integer
        docs: >-
          The number of rules in the latest version of the pronunciation
          dictionary.
      name:
        type: string
        docs: The name of the pronunciation dictionary.
      permission_on_resource:
        type: >-
          optional<GetPronunciationDictionaryMetadataResponseModelPermissionOnResource>
        docs: The permission on the resource of the pronunciation dictionary.
      created_by:
        type: string
        docs: The user ID of the creator of the pronunciation dictionary.
      creation_time_unix:
        type: integer
        docs: The creation time of the pronunciation dictionary in Unix timestamp.
      archived_time_unix:
        type: optional<integer>
        docs: The archive time of the pronunciation dictionary in Unix timestamp.
      description:
        type: optional<string>
        docs: The description of the pronunciation dictionary.
    source:
      openapi: openapi.json
  GetSpeechHistoryResponse:
    properties:
      history:
        docs: A list of speech history items.
        type: list<SpeechHistoryItemResponse>
      last_history_item_id:
        type: optional<string>
        docs: The ID of the last history item.
      has_more:
        type: boolean
        docs: Whether there are more history items to fetch.
    source:
      openapi: openapi.json
  GetVoicesResponse:
    properties:
      voices:
        docs: A list of available voices.
        type: list<Voice>
    source:
      openapi: openapi.json
  GetVoicesV2Response:
    properties:
      voices:
        type: list<Voice>
      has_more: boolean
      total_count: integer
      next_page_token:
        type: optional<string>
    source:
      openapi: openapi.json
  GetWorkspaceSecretsResponseModel:
    properties:
      secrets:
        type: list<ConvAiWorkspaceStoredSecretConfig>
    source:
      openapi: openapi.json
  HTTPValidationError:
    properties:
      detail:
        type: optional<list<ValidationError>>
    source:
      openapi: openapi.json
  HistoryAlignmentResponseModel:
    properties:
      characters:
        docs: The characters in the alignment.
        type: list<string>
      character_start_times_seconds:
        docs: The start times of the characters in seconds.
        type: list<double>
      character_end_times_seconds:
        docs: The end times of the characters in seconds.
        type: list<double>
    source:
      openapi: openapi.json
  HistoryAlignmentsResponseModel:
    properties:
      alignment:
        type: HistoryAlignmentResponseModel
        docs: The alignment of the text.
      normalized_alignment:
        type: HistoryAlignmentResponseModel
        docs: The normalized alignment of the text.
    source:
      openapi: openapi.json
  HtmlExportOptions:
    properties:
      include_speakers:
        type: optional<boolean>
        default: true
      include_timestamps:
        type: optional<boolean>
        default: true
      segment_on_silence_longer_than_s:
        type: optional<double>
      max_segment_duration_s:
        type: optional<double>
      max_segment_chars:
        type: optional<integer>
    source:
      openapi: openapi.json
  ImageAvatar:
    properties:
      url:
        type: optional<string>
        docs: The URL of the avatar
        default: ''
    source:
      openapi: openapi.json
  InvoiceResponse:
    properties:
      amount_due_cents:
        type: integer
        docs: The amount due in cents.
      discount_percent_off:
        type: optional<double>
        docs: The discount applied to the invoice. E.g. [20.0f] for 20% off.
      next_payment_attempt_unix:
        type: integer
        docs: The Unix timestamp of the next payment attempt.
    source:
      openapi: openapi.json
  KnowledgeBaseDocumentChunkResponseModel:
    properties:
      id: string
      name: string
      content: string
    source:
      openapi: openapi.json
  KnowledgeBaseDocumentMetadataResponseModel:
    properties:
      created_at_unix_secs: integer
      last_updated_at_unix_secs: integer
      size_bytes: integer
    source:
      openapi: openapi.json
  KnowledgeBaseDocumentType:
    enum:
      - file
      - url
      - text
    source:
      openapi: openapi.json
  KnowledgeBaseLocator:
    properties:
      type:
        type: KnowledgeBaseDocumentType
        docs: The type of the knowledge base
      name:
        type: string
        docs: The name of the knowledge base
      id:
        type: string
        docs: The ID of the knowledge base
      usage_mode:
        type: optional<DocumentUsageModeEnum>
        docs: The usage mode of the knowledge base
    source:
      openapi: openapi.json
  Llm:
    enum:
      - value: gpt-4o-mini
        name: Gpt4OMini
      - value: gpt-4o
        name: Gpt4O
      - value: gpt-4
        name: Gpt4
      - value: gpt-4-turbo
        name: Gpt4Turbo
      - value: gpt-4.1
        name: Gpt41
      - value: gpt-4.1-mini
        name: Gpt41Mini
      - value: gpt-4.1-nano
        name: Gpt41Nano
      - value: gpt-3.5-turbo
        name: Gpt35Turbo
      - value: gemini-1.5-pro
        name: Gemini15Pro
      - value: gemini-1.5-flash
        name: Gemini15Flash
      - value: gemini-2.0-flash-001
        name: Gemini20Flash001
      - value: gemini-2.0-flash-lite
        name: Gemini20FlashLite
      - value: gemini-2.5-flash
        name: Gemini25Flash
      - value: gemini-1.0-pro
        name: Gemini10Pro
      - value: claude-3-7-sonnet
        name: Claude37Sonnet
      - value: claude-3-5-sonnet
        name: Claude35Sonnet
      - value: claude-3-5-sonnet-v1
        name: Claude35SonnetV1
      - value: claude-3-haiku
        name: Claude3Haiku
      - value: grok-beta
        name: GrokBeta
      - value: custom-llm
        name: CustomLlm
    source:
      openapi: openapi.json
  LlmCategoryUsage:
    properties:
      irreversible_generation:
        type: optional<LlmUsageOutput>
      initiated_generation:
        type: optional<LlmUsageOutput>
    source:
      openapi: openapi.json
  LlmInputOutputTokensUsage:
    properties:
      input:
        type: optional<LlmTokensCategoryUsage>
      input_cache_read:
        type: optional<LlmTokensCategoryUsage>
      input_cache_write:
        type: optional<LlmTokensCategoryUsage>
      output_total:
        type: optional<LlmTokensCategoryUsage>
    source:
      openapi: openapi.json
  LlmTokensCategoryUsage:
    properties:
      tokens:
        type: optional<integer>
        default: 0
      price:
        type: optional<double>
        default: 0
    source:
      openapi: openapi.json
  LlmUsageInput:
    properties:
      model_usage:
        type: optional<map<string, LlmInputOutputTokensUsage>>
    source:
      openapi: openapi.json
  LlmUsageOutput:
    properties:
      model_usage:
        type: optional<map<string, LlmInputOutputTokensUsage>>
    source:
      openapi: openapi.json
  LanguageAddedResponse:
    properties:
      version: integer
    source:
      openapi: openapi.json
  LanguageDetectionToolConfig:
    properties: {}
    source:
      openapi: openapi.json
  LanguagePresetInput:
    properties:
      overrides:
        type: ConversationConfigClientOverrideInput
        docs: The overrides for the language preset
      first_message_translation:
        type: optional<LanguagePresetTranslation>
        docs: The translation of the first message
    source:
      openapi: openapi.json
  LanguagePresetOutput:
    properties:
      overrides:
        type: ConversationConfigClientOverrideOutput
        docs: The overrides for the language preset
      first_message_translation:
        type: optional<LanguagePresetTranslation>
        docs: The translation of the first message
    source:
      openapi: openapi.json
  LanguagePresetTranslation:
    properties:
      source_hash: string
      text: string
    source:
      openapi: openapi.json
  LanguageResponse:
    properties:
      language_id:
        type: string
        docs: The unique identifier of the language.
      name:
        type: string
        docs: The name of the language.
    source:
      openapi: openapi.json
  LibraryVoiceResponseModelCategory:
    enum:
      - generated
      - cloned
      - premade
      - professional
      - famous
      - high_quality
    docs: The category of the voice.
    inline: true
    source:
      openapi: openapi.json
  LibraryVoiceResponse:
    properties:
      public_owner_id:
        type: string
        docs: The public owner id of the voice.
      voice_id:
        type: string
        docs: The id of the voice.
      date_unix:
        type: integer
        docs: The date the voice was added to the library in Unix time.
      name:
        type: string
        docs: The name of the voice.
      accent:
        type: string
        docs: The accent of the voice.
      gender:
        type: string
        docs: The gender of the voice.
      age:
        type: string
        docs: The age of the voice.
      descriptive:
        type: string
        docs: The descriptive of the voice.
      use_case:
        type: string
        docs: The use case of the voice.
      category:
        type: LibraryVoiceResponseModelCategory
        docs: The category of the voice.
      language:
        type: optional<string>
        docs: The language of the voice.
      locale:
        type: optional<string>
        docs: The locale of the voice.
      description:
        type: optional<string>
        docs: The description of the voice.
      preview_url:
        type: optional<string>
        docs: The preview URL of the voice.
      usage_character_count_1y:
        type: integer
        docs: The usage character count of the voice in the last year.
      usage_character_count_7d:
        type: integer
        docs: The usage character count of the voice in the last 7 days.
      play_api_usage_character_count_1y:
        type: integer
        docs: The play API usage character count of the voice in the last year.
      cloned_by_count:
        type: integer
        docs: The number of times the voice has been cloned.
      rate:
        type: optional<double>
        docs: The rate of the voice.
      free_users_allowed:
        type: boolean
        docs: Whether free users are allowed to use the voice.
      live_moderation_enabled:
        type: boolean
        docs: Whether live moderation is enabled for the voice.
      featured:
        type: boolean
        docs: Whether the voice is featured.
      verified_languages:
        type: optional<list<VerifiedVoiceLanguageResponseModel>>
        docs: The verified languages of the voice.
      notice_period:
        type: optional<integer>
        docs: The notice period of the voice.
      instagram_username:
        type: optional<string>
        docs: The Instagram username of the voice.
      twitter_username:
        type: optional<string>
        docs: The Twitter username of the voice.
      youtube_username:
        type: optional<string>
        docs: The YouTube username of the voice.
      tiktok_username:
        type: optional<string>
        docs: The TikTok username of the voice.
      image_url:
        type: optional<string>
        docs: The image URL of the voice.
      is_added_by_user:
        type: optional<boolean>
        docs: Whether the voice was added by the user.
    source:
      openapi: openapi.json
  LiteralJsonSchemaPropertyType:
    enum:
      - boolean
      - string
      - integer
      - number
    inline: true
    source:
      openapi: openapi.json
  LiteralJsonSchemaPropertyConstantValue:
    discriminated: false
    docs: The constant value of the property
    union:
      - string
      - integer
      - double
      - boolean
    source:
      openapi: openapi.json
    inline: true
  LiteralJsonSchemaProperty:
    properties:
      type:
        type: LiteralJsonSchemaPropertyType
      description:
        type: optional<string>
        docs: The description of the property
        default: ''
      dynamic_variable:
        type: optional<string>
        docs: The dynamic variable of the property
        default: ''
      constant_value:
        type: optional<LiteralJsonSchemaPropertyConstantValue>
        docs: The constant value of the property
    source:
      openapi: openapi.json
  McpToolConfigInput:
    docs: A MCP tool is a tool that is used to call a MCP server
    properties:
      id:
        type: optional<string>
        default: ''
      name:
        type: string
        validation:
          pattern: ^[a-zA-Z0-9_-]{1,64}$
          minLength: 0
      description:
        type: string
        validation:
          minLength: 0
      response_timeout_secs:
        type: optional<integer>
        docs: The maximum time in seconds to wait for the tool call to complete.
        default: 20
      parameters:
        type: optional<ObjectJsonSchemaPropertyInput>
        docs: Schema for any parameters the LLM needs to provide to the MCP tool.
      mcp_tool_name:
        type: string
        docs: The name of the MCP tool to call
      mcp_server_id:
        type: string
        docs: The id of the MCP server to call
    source:
      openapi: openapi.json
  McpToolConfigOutput:
    docs: A MCP tool is a tool that is used to call a MCP server
    properties:
      id:
        type: optional<string>
        default: ''
      name:
        type: string
        validation:
          pattern: ^[a-zA-Z0-9_-]{1,64}$
          minLength: 0
      description:
        type: string
        validation:
          minLength: 0
      response_timeout_secs:
        type: optional<integer>
        docs: The maximum time in seconds to wait for the tool call to complete.
        default: 20
      parameters:
        type: optional<ObjectJsonSchemaPropertyOutput>
        docs: Schema for any parameters the LLM needs to provide to the MCP tool.
      mcp_tool_name:
        type: string
        docs: The name of the MCP tool to call
      mcp_server_id:
        type: string
        docs: The id of the MCP server to call
    source:
      openapi: openapi.json
  ManualVerificationFileResponse:
    properties:
      file_id:
        type: string
        docs: The ID of the file.
      file_name:
        type: string
        docs: The name of the file.
      mime_type:
        type: string
        docs: The MIME type of the file.
      size_bytes:
        type: integer
        docs: The size of the file in bytes.
      upload_date_unix:
        type: integer
        docs: The date of the file in Unix time.
    source:
      openapi: openapi.json
  ManualVerificationResponse:
    properties:
      extra_text:
        type: string
        docs: The extra text of the manual verification.
      request_time_unix:
        type: integer
        docs: The date of the manual verification in Unix time.
      files:
        docs: The files of the manual verification.
        type: list<ManualVerificationFileResponse>
    source:
      openapi: openapi.json
  MetricRecord:
    properties:
      elapsed_time: double
    source:
      openapi: openapi.json
  MetricType:
    enum:
      - credits
      - minutes_used
      - request_count
      - ttfb_avg
      - ttfb_p95
      - fiat_units_spent
    source:
      openapi: openapi.json
  ModelRatesResponseModel:
    properties:
      character_cost_multiplier:
        type: double
        docs: The cost multiplier for characters.
    source:
      openapi: openapi.json
  ModelResponseModelConcurrencyGroup:
    enum:
      - standard
      - turbo
    docs: The concurrency group for the model.
    inline: true
    source:
      openapi: openapi.json
  Model:
    properties:
      model_id:
        type: string
        docs: The unique identifier of the model.
      name:
        type: optional<string>
        docs: The name of the model.
      can_be_finetuned:
        type: optional<boolean>
        docs: Whether the model can be finetuned.
      can_do_text_to_speech:
        type: optional<boolean>
        docs: Whether the model can do text-to-speech.
      can_do_voice_conversion:
        type: optional<boolean>
        docs: Whether the model can do voice conversion.
      can_use_style:
        type: optional<boolean>
        docs: Whether the model can use style.
      can_use_speaker_boost:
        type: optional<boolean>
        docs: Whether the model can use speaker boost.
      serves_pro_voices:
        type: optional<boolean>
        docs: Whether the model serves pro voices.
      token_cost_factor:
        type: optional<double>
        docs: The cost factor for the model.
      description:
        type: optional<string>
        docs: The description of the model.
      requires_alpha_access:
        type: optional<boolean>
        docs: Whether the model requires alpha access.
      max_characters_request_free_user:
        type: optional<integer>
        docs: The maximum number of characters that can be requested by a free user.
      max_characters_request_subscribed_user:
        type: optional<integer>
        docs: >-
          The maximum number of characters that can be requested by a subscribed
          user.
      maximum_text_length_per_request:
        type: optional<integer>
        docs: The maximum length of text that can be requested for this model.
      languages:
        type: optional<list<LanguageResponse>>
        docs: The languages supported by the model.
      model_rates:
        type: optional<ModelRatesResponseModel>
        docs: The rates for the model.
      concurrency_group:
        type: optional<ModelResponseModelConcurrencyGroup>
        docs: The concurrency group for the model.
    source:
      openapi: openapi.json
  ModerationStatusResponseModelSafetyStatus:
    enum:
      - appeal_approved
      - appeal_denied
      - false_positive
    inline: true
    source:
      openapi: openapi.json
  ModerationStatusResponseModelWarningStatus:
    enum:
      - warning
      - warning_cleared
    inline: true
    source:
      openapi: openapi.json
  ModerationStatusResponseModel:
    properties:
      is_in_probation:
        type: boolean
        docs: Whether the user is in probation.
      enterprise_check_nogo_voice:
        type: boolean
        docs: Whether the user's enterprise check nogo voice is enabled.
      enterprise_check_block_nogo_voice:
        type: boolean
        docs: Whether the user's enterprise check block nogo voice is enabled.
      never_live_moderate:
        type: boolean
        docs: Whether the user's never live moderate is enabled.
      nogo_voice_similar_voice_upload_count:
        type: integer
        docs: The number of similar voice uploads that have been blocked.
      enterprise_background_moderation_enabled:
        type: boolean
        docs: Whether the user's enterprise background moderation is enabled.
      safety_status:
        type: optional<ModerationStatusResponseModelSafetyStatus>
        docs: The safety status of the user.
      warning_status:
        type: optional<ModerationStatusResponseModelWarningStatus>
        docs: The warning status of the user.
      on_watchlist:
        type: boolean
        docs: Whether the user is on the watchlist.
    source:
      openapi: openapi.json
  ObjectJsonSchemaPropertyInputPropertiesValue:
    discriminated: false
    union:
      - type: LiteralJsonSchemaProperty
      - type: ObjectJsonSchemaPropertyInput
      - type: ArrayJsonSchemaPropertyInput
    source:
      openapi: openapi.json
    inline: true
  ObjectJsonSchemaPropertyInput:
    properties:
      type:
        type: optional<literal<"object">>
      required:
        type: optional<list<string>>
      description:
        type: optional<string>
        default: ''
      properties:
        type: optional<map<string, ObjectJsonSchemaPropertyInputPropertiesValue>>
    source:
      openapi: openapi.json
  ObjectJsonSchemaPropertyOutputPropertiesValue:
    discriminated: false
    union:
      - type: LiteralJsonSchemaProperty
      - type: ObjectJsonSchemaPropertyOutput
      - type: ArrayJsonSchemaPropertyOutput
    source:
      openapi: openapi.json
    inline: true
  ObjectJsonSchemaPropertyOutput:
    properties:
      type:
        type: optional<literal<"object">>
      required:
        type: optional<list<string>>
      description:
        type: optional<string>
        default: ''
      properties:
        type: optional<map<string, ObjectJsonSchemaPropertyOutputPropertiesValue>>
    source:
      openapi: openapi.json
  OrbAvatar:
    properties:
      color_1:
        type: optional<string>
        docs: The first color of the avatar
        default: '#2792dc'
      color_2:
        type: optional<string>
        docs: The second color of the avatar
        default: '#9ce6e6'
    source:
      openapi: openapi.json
  OutboundCallRecipientResponseModel:
    properties:
      id: string
      phone_number: string
      status:
        type: BatchCallRecipientStatus
      created_at_unix: integer
      updated_at_unix: integer
      conversation_id: optional<string>
      conversation_initiation_client_data:
        type: optional<ConversationInitiationClientDataInternal>
    source:
      openapi: openapi.json
  PdfExportOptions:
    properties:
      include_speakers:
        type: optional<boolean>
        default: true
      include_timestamps:
        type: optional<boolean>
        default: true
      segment_on_silence_longer_than_s:
        type: optional<double>
      max_segment_duration_s:
        type: optional<double>
      max_segment_chars:
        type: optional<integer>
    source:
      openapi: openapi.json
  PhoneNumberAgentInfo:
    properties:
      agent_id:
        type: string
        docs: The ID of the agent
      agent_name:
        type: string
        docs: The name of the agent
    source:
      openapi: openapi.json
  PhoneNumberTransfer:
    properties:
      phone_number: string
      condition: string
    source:
      openapi: openapi.json
  PodcastBulletinMode:
    properties:
      bulletin:
        type: PodcastBulletinModeData
        docs: The voice settings for the bulletin.
    source:
      openapi: openapi.json
  PodcastBulletinModeData:
    properties:
      host_voice_id:
        type: string
        docs: The ID of the host voice.
    source:
      openapi: openapi.json
  PodcastConversationMode:
    properties:
      conversation:
        type: PodcastConversationModeData
        docs: The voice settings for the conversation.
    source:
      openapi: openapi.json
  PodcastConversationModeData:
    properties:
      host_voice_id:
        type: string
        docs: The ID of the host voice.
      guest_voice_id:
        type: string
        docs: The ID of the guest voice.
    source:
      openapi: openapi.json
  PodcastProjectResponseModel:
    properties:
      project:
        type: ProjectResponse
        docs: The project associated with the created podcast.
    source:
      openapi: openapi.json
  PodcastTextSource:
    properties:
      text:
        type: string
        docs: The text to create the podcast from.
    source:
      openapi: openapi.json
  PodcastUrlSource:
    properties:
      url:
        type: string
        docs: The URL to create the podcast from.
    source:
      openapi: openapi.json
  PostAgentAvatarResponseModel:
    properties:
      agent_id: string
      avatar_url:
        type: optional<string>
    source:
      openapi: openapi.json
  PostWorkspaceSecretResponseModel:
    properties:
      type:
        type: literal<"stored">
      secret_id: string
      name: string
    source:
      openapi: openapi.json
  PrivacyConfig:
    properties:
      record_voice:
        type: optional<boolean>
        docs: Whether to record the conversation
        default: true
      retention_days:
        type: optional<integer>
        docs: >-
          The number of days to retain the conversation. -1 indicates there is
          no retention limit
        default: -1
      delete_transcript_and_pii:
        type: optional<boolean>
        docs: Whether to delete the transcript and PII
        default: false
      delete_audio:
        type: optional<boolean>
        docs: Whether to delete the audio
        default: false
      apply_to_existing_conversations:
        type: optional<boolean>
        docs: Whether to apply the privacy settings to existing conversations
        default: false
      zero_retention_mode:
        type: optional<boolean>
        docs: Whether to enable zero retention mode - no PII data is stored
        default: false
    source:
      openapi: openapi.json
  ProjectCreationMetaResponseModelStatus:
    enum:
      - pending
      - creating
      - finished
      - failed
    docs: The status of the project creation action.
    inline: true
    source:
      openapi: openapi.json
  ProjectCreationMetaResponseModelType:
    enum:
      - blank
      - generate_podcast
      - auto_assign_voices
    docs: The type of the project creation action.
    inline: true
    source:
      openapi: openapi.json
  ProjectCreationMetaResponseModel:
    properties:
      creation_progress:
        type: double
        docs: The progress of the project creation.
      status:
        type: ProjectCreationMetaResponseModelStatus
        docs: The status of the project creation action.
      type:
        type: ProjectCreationMetaResponseModelType
        docs: The type of the project creation action.
    source:
      openapi: openapi.json
  ProjectExtendedResponseModelTargetAudience:
    enum:
      - children
      - value: young adult
        name: YoungAdult
      - adult
      - value: all ages
        name: AllAges
    inline: true
    source:
      openapi: openapi.json
  ProjectState:
    enum:
      - creating
      - default
      - converting
      - in_queue
    docs: The state of the project.
    inline: true
    source:
      openapi: openapi.json
  ProjectExtendedResponseModelAccessLevel:
    enum:
      - admin
      - editor
      - viewer
    docs: The access level of the project.
    inline: true
    source:
      openapi: openapi.json
  ProjectExtendedResponseModelFiction:
    enum:
      - fiction
      - value: non-fiction
        name: NonFiction
    inline: true
    source:
      openapi: openapi.json
  ProjectExtendedResponseModelSourceType:
    enum:
      - blank
      - book
      - article
      - genfm
    inline: true
    source:
      openapi: openapi.json
  ProjectExtendedResponseModelQualityPreset:
    enum:
      - standard
      - high
      - highest
      - ultra
      - ultra_lossless
    docs: The quality preset level of the project.
    inline: true
    source:
      openapi: openapi.json
  ProjectExtendedResponseModelApplyTextNormalization:
    enum:
      - auto
      - 'on'
      - 'off'
      - apply_english
    docs: Whether text normalization is applied to the project.
    inline: true
    source:
      openapi: openapi.json
  ProjectExtendedResponse:
    properties:
      project_id:
        type: string
        docs: The ID of the project.
      name:
        type: string
        docs: The name of the project.
      create_date_unix:
        type: integer
        docs: The creation date of the project.
      default_title_voice_id:
        type: string
        docs: The default title voice ID.
      default_paragraph_voice_id:
        type: string
        docs: The default paragraph voice ID.
      default_model_id:
        type: string
        docs: The default model ID.
      last_conversion_date_unix:
        type: optional<integer>
        docs: The last conversion date of the project.
      can_be_downloaded:
        type: boolean
        docs: Whether the project can be downloaded.
      title:
        type: optional<string>
        docs: The title of the project.
      author:
        type: optional<string>
        docs: The author of the project.
      description:
        type: optional<string>
        docs: The description of the project.
      genres:
        type: optional<list<string>>
        docs: List of genres of the project.
      cover_image_url:
        type: optional<string>
        docs: The cover image URL of the project.
      target_audience:
        type: optional<ProjectExtendedResponseModelTargetAudience>
        docs: The target audience of the project.
      language:
        type: optional<string>
        docs: Two-letter language code (ISO 639-1) of the language of the project.
      content_type:
        type: optional<string>
        docs: The content type of the project, e.g. 'Novel' or 'Short Story'
      original_publication_date:
        type: optional<string>
        docs: The original publication date of the project.
      mature_content:
        type: optional<boolean>
        docs: Whether the project contains mature content.
      isbn_number:
        type: optional<string>
        docs: The ISBN number of the project.
      volume_normalization:
        type: boolean
        docs: Whether the project uses volume normalization.
      state:
        type: ProjectState
        docs: The state of the project.
      access_level:
        type: ProjectExtendedResponseModelAccessLevel
        docs: The access level of the project.
      fiction:
        type: optional<ProjectExtendedResponseModelFiction>
        docs: Whether the project is fiction.
      quality_check_on:
        type: boolean
        docs: Whether quality check is enabled for this project.
        availability: deprecated
      quality_check_on_when_bulk_convert:
        type: boolean
        docs: Whether quality check is enabled on the project when bulk converting.
        availability: deprecated
      creation_meta:
        type: optional<ProjectCreationMetaResponseModel>
        docs: The creation meta of the project.
      source_type:
        type: optional<ProjectExtendedResponseModelSourceType>
        docs: The source type of the project.
      chapters_enabled:
        type: optional<boolean>
        docs: Whether chapters are enabled for the project.
      quality_preset:
        type: ProjectExtendedResponseModelQualityPreset
        docs: The quality preset level of the project.
      chapters:
        docs: List of chapters of the project and their metadata.
        type: list<ChapterResponse>
      pronunciation_dictionary_versions:
        docs: >-
          List of pronunciation dictionary versions of the project and their
          metadata.
        type: list<PronunciationDictionaryVersionResponseModel>
      pronunciation_dictionary_locators:
        docs: List of pronunciation dictionary locators.
        type: list<PronunciationDictionaryLocatorResponseModel>
      apply_text_normalization:
        type: ProjectExtendedResponseModelApplyTextNormalization
        docs: Whether text normalization is applied to the project.
      experimental:
        type: map<string, unknown>
        docs: Experimental features of the project.
    source:
      openapi: openapi.json
  ProjectResponseModelTargetAudience:
    enum:
      - children
      - value: young adult
        name: YoungAdult
      - adult
      - value: all ages
        name: AllAges
    inline: true
    source:
      openapi: openapi.json
  ProjectResponseModelAccessLevel:
    enum:
      - admin
      - editor
      - viewer
    docs: The access level of the project.
    inline: true
    source:
      openapi: openapi.json
  ProjectResponseModelFiction:
    enum:
      - fiction
      - value: non-fiction
        name: NonFiction
    inline: true
    source:
      openapi: openapi.json
  ProjectResponseModelSourceType:
    enum:
      - blank
      - book
      - article
      - genfm
    inline: true
    source:
      openapi: openapi.json
  ProjectResponse:
    properties:
      project_id:
        type: string
        docs: The ID of the project.
      name:
        type: string
        docs: The name of the project.
      create_date_unix:
        type: integer
        docs: The creation date of the project.
      default_title_voice_id:
        type: string
        docs: The default title voice ID.
      default_paragraph_voice_id:
        type: string
        docs: The default paragraph voice ID.
      default_model_id:
        type: string
        docs: The default model ID.
      last_conversion_date_unix:
        type: optional<integer>
        docs: The last conversion date of the project.
      can_be_downloaded:
        type: boolean
        docs: Whether the project can be downloaded.
      title:
        type: optional<string>
        docs: The title of the project.
      author:
        type: optional<string>
        docs: The author of the project.
      description:
        type: optional<string>
        docs: The description of the project.
      genres:
        type: optional<list<string>>
        docs: List of genres of the project.
      cover_image_url:
        type: optional<string>
        docs: The cover image URL of the project.
      target_audience:
        type: optional<ProjectResponseModelTargetAudience>
        docs: The target audience of the project.
      language:
        type: optional<string>
        docs: Two-letter language code (ISO 639-1) of the language of the project.
      content_type:
        type: optional<string>
        docs: The content type of the project, e.g. 'Novel' or 'Short Story'
      original_publication_date:
        type: optional<string>
        docs: The original publication date of the project.
      mature_content:
        type: optional<boolean>
        docs: Whether the project contains mature content.
      isbn_number:
        type: optional<string>
        docs: The ISBN number of the project.
      volume_normalization:
        type: boolean
        docs: Whether the project uses volume normalization.
      state:
        type: ProjectState
        docs: The state of the project.
      access_level:
        type: ProjectResponseModelAccessLevel
        docs: The access level of the project.
      fiction:
        type: optional<ProjectResponseModelFiction>
        docs: Whether the project is fiction.
      quality_check_on:
        type: boolean
        docs: Whether quality check is enabled for this project.
        availability: deprecated
      quality_check_on_when_bulk_convert:
        type: boolean
        docs: Whether quality check is enabled on the project when bulk converting.
        availability: deprecated
      creation_meta:
        type: optional<ProjectCreationMetaResponseModel>
        docs: The creation meta of the project.
      source_type:
        type: optional<ProjectResponseModelSourceType>
        docs: The source type of the project.
      chapters_enabled:
        type: optional<boolean>
        docs: Whether chapters are enabled for the project.
    source:
      openapi: openapi.json
  ProjectSnapshotExtendedResponseModel:
    properties:
      project_snapshot_id:
        type: string
        docs: The ID of the project snapshot.
      project_id:
        type: string
        docs: The ID of the project.
      created_at_unix:
        type: integer
        docs: The creation date of the project snapshot.
      name:
        type: string
        docs: The name of the project snapshot.
      audio_upload:
        type: optional<map<string, unknown>>
        docs: (Deprecated)
      zip_upload:
        type: optional<map<string, unknown>>
        docs: (Deprecated)
      character_alignments:
        type: list<CharacterAlignmentModel>
    source:
      openapi: openapi.json
  ProjectSnapshotResponse:
    properties:
      project_snapshot_id:
        type: string
        docs: The ID of the project snapshot.
      project_id:
        type: string
        docs: The ID of the project.
      created_at_unix:
        type: integer
        docs: The creation date of the project snapshot.
      name:
        type: string
        docs: The name of the project snapshot.
      audio_upload:
        type: optional<map<string, unknown>>
        docs: (Deprecated)
      zip_upload:
        type: optional<map<string, unknown>>
        docs: (Deprecated)
    source:
      openapi: openapi.json
  ProjectSnapshotsResponse:
    properties:
      snapshots:
        docs: List of project snapshots.
        type: list<ProjectSnapshotResponse>
    source:
      openapi: openapi.json
  PromptAgentInputToolsItem:
    discriminant: type
    base-properties: {}
    docs: The type of tool
    union:
      client:
        type: ClientToolConfigInput
      mcp:
        type: McpToolConfigInput
      system:
        type: SystemToolConfigInput
      webhook:
        type: WebhookToolConfigInput
    source:
      openapi: openapi.json
  PromptAgent:
    properties:
      prompt:
        type: optional<string>
        docs: The prompt for the agent
        default: ''
      llm:
        type: optional<Llm>
        docs: The LLM to query with the prompt and the chat history
      temperature:
        type: optional<double>
        docs: The temperature for the LLM
        default: 0
      max_tokens:
        type: optional<integer>
        docs: If greater than 0, maximum number of tokens the LLM can predict
        default: -1
      tools:
        type: optional<list<PromptAgentOutputToolsItem>>
        docs: >-
          A list of tools that the agent can use over the course of the
          conversation
      tool_ids:
        type: optional<list<string>>
        docs: A list of IDs of tools used by the agent
      mcp_server_ids:
        type: optional<list<string>>
        docs: A list of MCP server ids to be used by the agent
      knowledge_base:
        type: optional<list<KnowledgeBaseLocator>>
        docs: A list of knowledge bases to be used by the agent
      custom_llm:
        type: optional<CustomLlm>
        docs: Definition for a custom LLM if LLM field is set to 'CUSTOM_LLM'
      ignore_default_personality:
        type: optional<boolean>
        docs: Whether to ignore the default personality
      rag:
        type: optional<RagConfig>
        docs: Configuration for RAG
    source:
      openapi: openapi.json
  PromptAgentOutputToolsItem:
    discriminant: type
    base-properties: {}
    docs: The type of tool
    union:
      client:
        type: ClientToolConfigOutput
      mcp:
        type: McpToolConfigOutput
      system:
        type: SystemToolConfigOutput
      webhook:
        type: WebhookToolConfigOutput
    source:
      openapi: openapi.json
  PromptAgentDbModelToolsItem:
    discriminant: type
    base-properties: {}
    docs: The type of tool
    union:
      client:
        type: ClientToolConfigInput
      mcp:
        type: McpToolConfigInput
      system:
        type: SystemToolConfigInput
      webhook:
        type: WebhookToolConfigInput
    source:
      openapi: openapi.json
  PromptAgentDbModel:
    properties:
      prompt:
        type: optional<string>
        docs: The prompt for the agent
        default: ''
      llm:
        type: optional<Llm>
        docs: The LLM to query with the prompt and the chat history
      temperature:
        type: optional<double>
        docs: The temperature for the LLM
        default: 0
      max_tokens:
        type: optional<integer>
        docs: If greater than 0, maximum number of tokens the LLM can predict
        default: -1
      tools:
        type: optional<list<PromptAgentDbModelToolsItem>>
        docs: >-
          A list of tools that the agent can use over the course of the
          conversation
      tool_ids:
        type: optional<list<string>>
        docs: A list of IDs of tools used by the agent
      mcp_server_ids:
        type: optional<list<string>>
        docs: A list of MCP server ids to be used by the agent
      knowledge_base:
        type: optional<list<KnowledgeBaseLocator>>
        docs: A list of knowledge bases to be used by the agent
      custom_llm:
        type: optional<CustomLlm>
        docs: Definition for a custom LLM if LLM field is set to 'CUSTOM_LLM'
      ignore_default_personality:
        type: optional<boolean>
        docs: Whether to ignore the default personality
      rag:
        type: optional<RagConfig>
        docs: Configuration for RAG
      knowledge_base_document_ids:
        type: optional<list<string>>
    source:
      openapi: openapi.json
  PromptAgentOverride:
    properties:
      prompt:
        type: optional<string>
        docs: >-
          The initial system message that defines the agent, e.g. “You are a
          German language teacher named Laura.”
        default: ''
    source:
      openapi: openapi.json
  PromptAgentOverrideConfig:
    properties:
      prompt:
        type: optional<boolean>
        docs: Whether to allow prompt overriding
        default: false
    source:
      openapi: openapi.json
  PromptEvaluationCriteria:
    docs: >-
      An evaluation using the transcript and a prompt for a yes/no achieved
      answer
    properties:
      id:
        type: string
        docs: The unique identifier for the evaluation criteria
      name: string
      type:
        type: optional<literal<"prompt">>
        docs: The type of evaluation criteria
      conversation_goal_prompt:
        type: string
        docs: The prompt that the agent should use to evaluate the conversation
        validation:
          maxLength: 2000
      use_knowledge_base:
        type: optional<boolean>
        docs: When evaluating the prompt, should the agent's knowledge base be used.
        default: false
    source:
      openapi: openapi.json
  PronunciationDictionaryAliasRuleRequestModel:
    properties:
      string_to_replace:
        type: string
        docs: The string to replace. Must be a non-empty string.
      alias:
        type: string
        docs: The alias for the string to be replaced.
    source:
      openapi: openapi.json
  PronunciationDictionaryLocatorResponseModel:
    properties:
      pronunciation_dictionary_id: string
      version_id: optional<string>
    source:
      openapi: openapi.json
  PronunciationDictionaryPhonemeRuleRequestModel:
    properties:
      string_to_replace:
        type: string
        docs: The string to replace. Must be a non-empty string.
      phoneme:
        type: string
        docs: The phoneme rule.
      alphabet:
        type: string
        docs: The alphabet to use with the phoneme rule.
    source:
      openapi: openapi.json
  PronunciationDictionaryRulesResponseModel:
    properties:
      id:
        type: string
        docs: The ID of the pronunciation dictionary.
      version_id:
        type: string
        docs: The version ID of the pronunciation dictionary.
      version_rules_num:
        type: integer
        docs: The number of rules in the version of the pronunciation dictionary.
    source:
      openapi: openapi.json
  PronunciationDictionaryVersionLocator:
    properties:
      pronunciation_dictionary_id: string
      version_id: optional<string>
    source:
      openapi: openapi.json
  PronunciationDictionaryVersionLocatorRequestModel:
    properties:
      pronunciation_dictionary_id:
        type: string
        docs: The ID of the pronunciation dictionary.
      version_id:
        type: optional<string>
        docs: >-
          The ID of the version of the pronunciation dictionary. If not
          provided, the latest version will be used.
    source:
      openapi: openapi.json
  PronunciationDictionaryVersionResponseModelPermissionOnResource:
    enum:
      - admin
      - editor
      - viewer
    inline: true
    source:
      openapi: openapi.json
  PronunciationDictionaryVersionResponseModel:
    properties:
      version_id: string
      version_rules_num: integer
      pronunciation_dictionary_id: string
      dictionary_name: string
      version_name: string
      permission_on_resource: >-
        optional<PronunciationDictionaryVersionResponseModelPermissionOnResource>
      created_by: string
      creation_time_unix: integer
      archived_time_unix:
        type: optional<integer>
    source:
      openapi: openapi.json
  PydanticPronunciationDictionaryVersionLocator:
    docs: >-
      A locator for other documents to be able to reference a specific
      dictionary and it's version.

      This is a pydantic version of
      PronunciationDictionaryVersionLocatorDBModel.

      Required to ensure compat with the rest of the agent data models.
    properties:
      pronunciation_dictionary_id:
        type: string
        docs: The ID of the pronunciation dictionary
      version_id:
        type: optional<string>
        docs: The ID of the version of the pronunciation dictionary
    source:
      openapi: openapi.json
  QueryParamsJsonSchema:
    properties:
      properties:
        type: map<string, LiteralJsonSchemaProperty>
      required:
        type: optional<list<string>>
    source:
      openapi: openapi.json
  RagIndexResponseModel:
    properties:
      status:
        type: RagIndexStatus
      progress_percentage: double
    source:
      openapi: openapi.json
  RagIndexStatus:
    enum:
      - created
      - processing
      - failed
      - succeeded
    source:
      openapi: openapi.json
  RagChunkMetadata:
    properties:
      document_id: string
      chunk_id: string
      vector_distance: double
    source:
      openapi: openapi.json
  RagConfig:
    properties:
      enabled:
        type: optional<boolean>
        default: false
      embedding_model:
        type: optional<EmbeddingModelEnum>
      max_vector_distance:
        type: optional<double>
        docs: Maximum vector distance of retrieved chunks.
        default: 0.6
      max_documents_length:
        type: optional<integer>
        docs: Maximum total length of document chunks retrieved from RAG.
        default: 50000
    source:
      openapi: openapi.json
  RagRetrievalInfo:
    properties:
      chunks:
        type: list<RagChunkMetadata>
      embedding_model:
        type: EmbeddingModelEnum
      retrieval_query: string
      rag_latency_secs: double
    source:
      openapi: openapi.json
  ReaderResourceResponseModelResourceType:
    enum:
      - read
      - collection
    docs: The type of resource.
    inline: true
    source:
      openapi: openapi.json
  ReaderResourceResponseModel:
    properties:
      resource_type:
        type: ReaderResourceResponseModelResourceType
        docs: The type of resource.
      resource_id:
        type: string
        docs: The ID of the resource.
    source:
      openapi: openapi.json
  RecordingResponse:
    properties:
      recording_id:
        type: string
        docs: The ID of the recording.
      mime_type:
        type: string
        docs: The MIME type of the recording.
      size_bytes:
        type: integer
        docs: The size of the recording in bytes.
      upload_date_unix:
        type: integer
        docs: The date of the recording in Unix time.
      transcription:
        type: string
        docs: The transcription of the recording.
    source:
      openapi: openapi.json
  RenderStatus:
    enum:
      - complete
      - processing
      - failed
    inline: true
    source:
      openapi: openapi.json
  Render:
    properties:
      id: string
      version: integer
      language: optional<string>
      type: optional<RenderType>
      media_ref: optional<DubbingMediaReference>
      status:
        type: RenderStatus
    source:
      openapi: openapi.json
  RenderType:
    enum:
      - mp4
      - aac
      - mp3
      - wav
      - aaf
      - tracks_zip
      - clips_zip
    source:
      openapi: openapi.json
  RequestPvcManualVerificationResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the request PVC manual verification request. If the
          request was successful, the status will be 'ok'. Otherwise an error
          message with status 500 will be returned.
    source:
      openapi: openapi.json
  ResourceAccessInfoRole:
    enum:
      - admin
      - editor
      - viewer
    docs: The role of the user making the request
    inline: true
    source:
      openapi: openapi.json
  ResourceAccessInfo:
    properties:
      is_creator:
        type: boolean
        docs: Whether the user making the request is the creator of the agent
      creator_name:
        type: string
        docs: Name of the agent's creator
      creator_email:
        type: string
        docs: Email of the agent's creator
      role:
        type: ResourceAccessInfoRole
        docs: The role of the user making the request
    source:
      openapi: openapi.json
  ResourceMetadataResponseModel:
    properties:
      resource_id:
        type: string
        docs: The ID of the resource.
      resource_type:
        type: WorkspaceResourceType
        docs: The type of the resource.
      creator_user_id:
        type: optional<string>
        docs: The ID of the user who created the resource.
      role_to_group_ids:
        type: map<string, list<string>>
        docs: >-
          A mapping of roles to group IDs. When the resource is shared with a
          user, the group id is the user's id.
      share_options:
        docs: >-
          List of options for sharing the resource further in the workspace.
          These are users who don't have access to the resource yet.
        type: list<ShareOptionResponseModel>
    source:
      openapi: openapi.json
  SipMediaEncryptionEnum:
    enum:
      - disabled
      - allowed
      - required
    source:
      openapi: openapi.json
  SipTrunkConfigResponseModel:
    docs: SIP Trunk configuration details for a phone number
    properties:
      address:
        type: string
        docs: Hostname or IP the SIP INVITE is sent to
      transport:
        type: SipTrunkTransportEnum
        docs: Protocol to use for SIP transport
      media_encryption:
        type: SipMediaEncryptionEnum
        docs: Whether or not to encrypt media (data layer).
      headers:
        type: optional<map<string, string>>
        docs: SIP headers for INVITE request
      has_auth_credentials:
        type: boolean
        docs: Whether authentication credentials are configured
      username:
        type: optional<string>
        docs: SIP trunk username (if available)
      has_outbound_trunk:
        type: optional<boolean>
        docs: Whether a LiveKit SIP outbound trunk is configured
        default: false
    source:
      openapi: openapi.json
  SipTrunkCredentials:
    properties:
      username:
        type: string
        docs: SIP trunk username
      password:
        type: string
        docs: SIP trunk password
    source:
      openapi: openapi.json
  SipTrunkOutboundCallResponse:
    properties:
      success: boolean
      message: string
      sip_call_id: optional<string>
    source:
      openapi: openapi.json
  SipTrunkTransportEnum:
    enum:
      - auto
      - udp
      - tcp
      - tls
    source:
      openapi: openapi.json
  SafetyCommonModel:
    docs: >-
      Safety object that has the information of safety evaluations based on used
      voice.
    properties:
      ivc:
        type: optional<SafetyEvaluation>
      non_ivc:
        type: optional<SafetyEvaluation>
    source:
      openapi: openapi.json
  SafetyEvaluation:
    docs: >-
      Safety evaluation of the agent. Prompt and first message is taken into
      account.

      The unsafe reason is provided from the evaluation
    properties:
      is_unsafe:
        type: optional<boolean>
        default: false
      llm_reason:
        type: optional<string>
        default: ''
      safety_prompt_version:
        type: optional<integer>
        default: 0
      matched_rule_id:
        type: optional<list<SafetyRule>>
    source:
      openapi: openapi.json
  SafetyResponseModel:
    properties:
      is_blocked_ivc:
        type: optional<boolean>
        default: false
      is_blocked_non_ivc:
        type: optional<boolean>
        default: false
      ignore_safety_evaluation:
        type: optional<boolean>
        default: false
    source:
      openapi: openapi.json
  SafetyRule:
    enum:
      - sexual_minors
      - forget_moderation
      - extremism
      - scam_fraud
      - political
      - self_harm
      - illegal_distribution_medical
      - sexual_adults
      - unknown
    source:
      openapi: openapi.json
  VoiceSample:
    properties:
      sample_id:
        type: optional<string>
        docs: The ID of the sample.
      file_name:
        type: optional<string>
        docs: The name of the sample file.
      mime_type:
        type: optional<string>
        docs: The MIME type of the sample file.
      size_bytes:
        type: optional<integer>
        docs: The size of the sample file in bytes.
      hash:
        type: optional<string>
        docs: The hash of the sample file.
      duration_secs:
        type: optional<double>
      remove_background_noise:
        type: optional<boolean>
      has_isolated_audio:
        type: optional<boolean>
      has_isolated_audio_preview:
        type: optional<boolean>
      speaker_separation:
        type: optional<SpeakerSeparationResponseModel>
      trim_start:
        type: optional<integer>
      trim_end:
        type: optional<integer>
    source:
      openapi: openapi.json
  SecretDependencyType:
    type: literal<"conversation_initiation_webhook">
  SegmentCreateResponse:
    properties:
      version: integer
      new_segment: string
    source:
      openapi: openapi.json
  SegmentDeleteResponse:
    properties:
      version: integer
    source:
      openapi: openapi.json
  SegmentDubResponse:
    properties:
      version: integer
    source:
      openapi: openapi.json
  SegmentTranscriptionResponse:
    properties:
      version: integer
    source:
      openapi: openapi.json
  SegmentTranslationResponse:
    properties:
      version: integer
    source:
      openapi: openapi.json
  SegmentUpdateResponse:
    properties:
      version: integer
    source:
      openapi: openapi.json
  SegmentedJsonExportOptions:
    properties:
      include_speakers:
        type: optional<boolean>
        default: true
      include_timestamps:
        type: optional<boolean>
        default: true
      segment_on_silence_longer_than_s:
        type: optional<double>
      max_segment_duration_s:
        type: optional<double>
      max_segment_chars:
        type: optional<integer>
    source:
      openapi: openapi.json
  ShareOptionResponseModelType:
    enum:
      - user
      - group
      - key
    docs: 'The type of the principal: user, group, or service account (under ''key'').'
    inline: true
    source:
      openapi: openapi.json
  ShareOptionResponseModel:
    properties:
      name:
        type: string
        docs: The name of the principal.
      id:
        type: string
        docs: The ID of the principal.
      type:
        type: ShareOptionResponseModelType
        docs: >-
          The type of the principal: user, group, or service account (under
          'key').
    source:
      openapi: openapi.json
  SimilarVoiceCategory:
    enum:
      - premade
      - cloned
      - generated
      - professional
      - famous
    inline: true
    source:
      openapi: openapi.json
  SimilarVoice:
    properties:
      voice_id: string
      name: string
      category:
        type: SimilarVoiceCategory
      description:
        type: optional<string>
      preview_url:
        type: optional<string>
    source:
      openapi: openapi.json
  SimilarVoicesForSpeakerResponse:
    properties:
      voices:
        type: list<SimilarVoice>
    source:
      openapi: openapi.json
  SpeakerAudioResponseModel:
    properties:
      audio_base_64:
        type: string
        docs: The base64 encoded audio.
      media_type:
        type: string
        docs: The media type of the audio.
      duration_secs:
        type: double
        docs: The duration of the audio in seconds.
    source:
      openapi: openapi.json
  SpeakerResponseModel:
    properties:
      speaker_id:
        type: string
        docs: The ID of the speaker.
      duration_secs:
        type: double
        docs: The duration of the speaker segment in seconds.
      utterances:
        type: optional<list<UtteranceResponseModel>>
        docs: The utterances of the speaker.
    source:
      openapi: openapi.json
  SpeakerSegment:
    properties:
      id: string
      start_time: double
      end_time: double
      text: string
      dubs:
        type: map<string, DubbedSegment>
    source:
      openapi: openapi.json
  SpeakerSeparationResponseModelStatus:
    enum:
      - not_started
      - pending
      - completed
      - failed
    docs: The status of the speaker separation.
    inline: true
    source:
      openapi: openapi.json
  SpeakerSeparationResponseModel:
    properties:
      voice_id:
        type: string
        docs: The ID of the voice.
      sample_id:
        type: string
        docs: The ID of the sample.
      status:
        type: SpeakerSeparationResponseModelStatus
        docs: The status of the speaker separation.
      speakers:
        type: optional<map<string, optional<SpeakerResponseModel>>>
        docs: The speakers of the sample.
      selected_speaker_ids:
        type: optional<list<string>>
        docs: The IDs of the selected speakers.
    source:
      openapi: openapi.json
  SpeakerTrack:
    properties:
      id: string
      media_ref:
        type: DubbingMediaReference
      speaker_name: string
      voices:
        type: map<string, string>
      segments:
        type: list<string>
    source:
      openapi: openapi.json
  SpeakerUpdatedResponse:
    properties:
      version: integer
    source:
      openapi: openapi.json
  SpeechHistoryItemResponseModelVoiceCategory:
    enum:
      - premade
      - cloned
      - generated
      - professional
    inline: true
    source:
      openapi: openapi.json
  SpeechHistoryItemResponseModelSource:
    enum:
      - TTS
      - STS
      - Projects
      - PD
      - AN
      - Dubbing
      - PlayAPI
      - ConvAI
    inline: true
    source:
      openapi: openapi.json
  SpeechHistoryItemResponse:
    properties:
      history_item_id:
        type: string
        docs: The ID of the history item.
      request_id:
        type: optional<string>
        docs: The ID of the request.
      voice_id:
        type: string
        docs: The ID of the voice used.
      model_id:
        type: optional<string>
        docs: The ID of the model.
      voice_name:
        type: string
        docs: The name of the voice.
      voice_category:
        type: optional<SpeechHistoryItemResponseModelVoiceCategory>
        docs: >-
          The category of the voice. Either 'premade', 'cloned', 'generated' or
          'professional'.
      text:
        type: string
        docs: The text used to generate the audio item.
      date_unix:
        type: integer
        docs: Unix timestamp of when the item was created.
      character_count_change_from:
        type: integer
        docs: The character count change from.
      character_count_change_to:
        type: integer
        docs: The character count change to.
      content_type:
        type: string
        docs: The content type of the generated item.
      state: unknown
      settings:
        type: optional<map<string, unknown>>
        docs: The settings of the history item.
      feedback:
        type: optional<FeedbackItem>
        docs: >-
          Feedback associated with the generated item. Returns null if no
          feedback has been provided.
      share_link_id:
        type: optional<string>
        docs: The ID of the share link.
      source:
        type: optional<SpeechHistoryItemResponseModelSource>
        docs: >-
          The source of the history item. Either TTS (text to speech), STS
          (speech to text), AN (audio native), Projects, Dubbing, PlayAPI, PD
          (pronunciation dictionary) or ConvAI (conversational AI).
      alignments:
        type: optional<HistoryAlignmentsResponseModel>
        docs: The alignments of the history item.
    source:
      openapi: openapi.json
  SpeechToTextCharacterResponseModel:
    properties:
      text:
        type: string
        docs: The character that was transcribed.
      start:
        type: optional<double>
        docs: The start time of the character in seconds.
      end:
        type: optional<double>
        docs: The end time of the character in seconds.
    source:
      openapi: openapi.json
  SpeechToTextChunkResponseModel:
    docs: Chunk-level detail of the transcription with timing information.
    properties:
      language_code:
        type: string
        docs: The detected language code (e.g. 'eng' for English).
      language_probability:
        type: double
        docs: The confidence score of the language detection (0 to 1).
      text:
        type: string
        docs: The raw text of the transcription.
      words:
        docs: List of words with their timing information.
        type: list<SpeechToTextWordResponseModel>
      additional_formats:
        type: optional<list<optional<AdditionalFormatResponseModel>>>
        docs: Requested additional formats of the transcript.
    source:
      openapi: openapi.json
  SpeechToTextWordResponseModelType:
    enum:
      - word
      - spacing
      - audio_event
    docs: >-
      The type of the word or sound. 'audio_event' is used for non-word sounds
      like laughter or footsteps.
    inline: true
    source:
      openapi: openapi.json
  SpeechToTextWordResponseModel:
    docs: Word-level detail of the transcription with timing information.
    properties:
      text:
        type: string
        docs: The word or sound that was transcribed.
      start:
        type: optional<double>
        docs: The start time of the word or sound in seconds.
      end:
        type: optional<double>
        docs: The end time of the word or sound in seconds.
      type:
        type: SpeechToTextWordResponseModelType
        docs: >-
          The type of the word or sound. 'audio_event' is used for non-word
          sounds like laughter or footsteps.
      speaker_id:
        type: optional<string>
        docs: Unique identifier for the speaker of this word.
      logprob:
        type: double
        docs: >-
          The log of the probability with which this word was predicted.
          Logprobs are in range [-infinity, 0], higher logprobs indicate a
          higher confidence the model has in its predictions.
      characters:
        type: optional<list<SpeechToTextCharacterResponseModel>>
        docs: The characters that make up the word and their timing information.
    source:
      openapi: openapi.json
  SrtExportOptions:
    properties:
      max_characters_per_line:
        type: optional<integer>
      include_speakers:
        type: optional<boolean>
        default: false
      include_timestamps:
        type: optional<boolean>
        default: true
      segment_on_silence_longer_than_s:
        type: optional<double>
      max_segment_duration_s:
        type: optional<double>
      max_segment_chars:
        type: optional<integer>
    source:
      openapi: openapi.json
  StartPvcVoiceTrainingResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the start PVC voice training request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  StartSpeakerSeparationResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the start speaker seperation request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  StreamingAudioChunkWithTimestampsResponse:
    properties:
      audio_base64:
        type: string
        docs: Base64 encoded audio data
      alignment:
        type: optional<CharacterAlignmentResponseModel>
        docs: Timestamp information for each character in the original text
      normalized_alignment:
        type: optional<CharacterAlignmentResponseModel>
        docs: Timestamp information for each character in the normalized text
    source:
      openapi: openapi.json
  SubscriptionExtrasResponseModel:
    properties:
      concurrency:
        type: integer
        docs: The concurrency of the user.
      convai_concurrency:
        type: integer
        docs: The Convai concurrency of the user.
      convai_chars_per_minute:
        type: optional<integer>
        docs: The Convai characters per minute of the user.
      convai_asr_chars_per_minute:
        type: optional<integer>
        docs: The Convai ASR characters per minute of the user.
      force_logging_disabled:
        type: boolean
        docs: Whether the user's logging is disabled.
      can_request_manual_pro_voice_verification:
        type: boolean
        docs: Whether the user can request manual pro voice verification.
      can_bypass_voice_captcha:
        type: boolean
        docs: Whether the user can bypass the voice captcha.
      moderation:
        type: ModerationStatusResponseModel
        docs: The moderation status of the user.
      unused_characters_rolled_over_from_previous_period:
        type: optional<integer>
        docs: The unused characters rolled over from the previous period.
      overused_characters_rolled_over_from_previous_period:
        type: optional<integer>
        docs: The overused characters rolled over from the previous period.
      usage:
        type: optional<SubscriptionUsageResponseModel>
        docs: Data on how the subscription is being used.
    source:
      openapi: openapi.json
  SubscriptionResponseModelCurrency:
    enum:
      - usd
      - eur
    inline: true
    source:
      openapi: openapi.json
  SubscriptionStatus:
    enum:
      - trialing
      - active
      - incomplete
      - incomplete_expired
      - past_due
      - canceled
      - unpaid
      - free
    docs: The status of the user's subscription.
    inline: true
    source:
      openapi: openapi.json
  SubscriptionResponseModelBillingPeriod:
    enum:
      - monthly_period
      - annual_period
    inline: true
    source:
      openapi: openapi.json
  SubscriptionResponseModelCharacterRefreshPeriod:
    enum:
      - monthly_period
      - annual_period
    inline: true
    source:
      openapi: openapi.json
  SubscriptionResponse:
    properties:
      tier:
        type: string
        docs: The tier of the user's subscription.
      character_count:
        type: integer
        docs: The number of characters used by the user.
      character_limit:
        type: integer
        docs: >-
          The maximum number of characters allowed in the current billing
          period.
      max_character_limit_extension:
        type: optional<integer>
        docs: >-
          Maximum number of characters that the character limit can be exceeded
          by. Managed by the workspace admin.
      can_extend_character_limit:
        type: boolean
        docs: Whether the user can extend their character limit.
      allowed_to_extend_character_limit:
        type: boolean
        docs: Whether the user is allowed to extend their character limit.
      next_character_count_reset_unix:
        type: optional<integer>
        docs: The Unix timestamp of the next character count reset.
      voice_slots_used:
        type: integer
        docs: The number of voice slots used by the user.
      professional_voice_slots_used:
        type: integer
        docs: >-
          The number of professional voice slots used by the workspace/user if
          single seat.
      voice_limit:
        type: integer
        docs: The maximum number of voice slots allowed for the user.
      max_voice_add_edits:
        type: optional<integer>
        docs: The maximum number of voice add/edits allowed for the user.
      voice_add_edit_counter:
        type: integer
        docs: The number of voice add/edits used by the user.
      professional_voice_limit:
        type: integer
        docs: The maximum number of professional voices allowed for the user.
      can_extend_voice_limit:
        type: boolean
        docs: Whether the user can extend their voice limit.
      can_use_instant_voice_cloning:
        type: boolean
        docs: Whether the user can use instant voice cloning.
      can_use_professional_voice_cloning:
        type: boolean
        docs: Whether the user can use professional voice cloning.
      currency:
        type: optional<SubscriptionResponseModelCurrency>
        docs: The currency of the user's subscription.
      status:
        type: SubscriptionStatus
        docs: The status of the user's subscription.
      billing_period:
        type: optional<SubscriptionResponseModelBillingPeriod>
        docs: The billing period of the user's subscription.
      character_refresh_period:
        type: optional<SubscriptionResponseModelCharacterRefreshPeriod>
        docs: The character refresh period of the user's subscription.
    source:
      openapi: openapi.json
  SubscriptionUsageResponseModel:
    properties:
      rollover_credits_quota:
        type: integer
        docs: The rollover credits quota.
      subscription_cycle_credits_quota:
        type: integer
        docs: The subscription cycle credits quota.
      manually_gifted_credits_quota:
        type: integer
        docs: The manually gifted credits quota.
      rollover_credits_used:
        type: integer
        docs: The rollover credits used.
      subscription_cycle_credits_used:
        type: integer
        docs: The subscription cycle credits used.
      manually_gifted_credits_used:
        type: integer
        docs: The manually gifted credits used.
      paid_usage_based_credits_used:
        type: integer
        docs: The paid usage based credits used.
      actual_reported_credits:
        type: integer
        docs: The actual reported credits.
    source:
      openapi: openapi.json
  SystemToolConfigInputParams:
    discriminant: system_tool_type
    base-properties: {}
    union:
      end_call:
        type: EndCallToolConfig
      language_detection:
        type: LanguageDetectionToolConfig
      transfer_to_agent:
        type: TransferToAgentToolConfig
      transfer_to_number:
        type: TransferToNumberToolConfig
    source:
      openapi: openapi.json
  SystemToolConfigInput:
    docs: A system tool is a tool that is used to call a system method in the server
    properties:
      id:
        type: optional<string>
        default: ''
      name:
        type: string
        validation:
          pattern: ^[a-zA-Z0-9_-]{1,64}$
          minLength: 0
      description:
        type: string
        validation:
          minLength: 0
      response_timeout_secs:
        type: optional<integer>
        docs: The maximum time in seconds to wait for the tool call to complete.
        default: 20
      params:
        display-name: Params
        type: SystemToolConfigInputParams
    source:
      openapi: openapi.json
  SystemToolConfigOutputParams:
    discriminant: system_tool_type
    base-properties: {}
    union:
      end_call:
        type: EndCallToolConfig
      language_detection:
        type: LanguageDetectionToolConfig
      transfer_to_agent:
        type: TransferToAgentToolConfig
      transfer_to_number:
        type: TransferToNumberToolConfig
    source:
      openapi: openapi.json
  SystemToolConfigOutput:
    docs: A system tool is a tool that is used to call a system method in the server
    properties:
      id:
        type: optional<string>
        default: ''
      name:
        type: string
        validation:
          pattern: ^[a-zA-Z0-9_-]{1,64}$
          minLength: 0
      description:
        type: string
        validation:
          minLength: 0
      response_timeout_secs:
        type: optional<integer>
        docs: The maximum time in seconds to wait for the tool call to complete.
        default: 20
      params:
        display-name: Params
        type: SystemToolConfigOutputParams
    source:
      openapi: openapi.json
  TtsConversationalConfig:
    properties:
      model_id:
        type: optional<TtsConversationalModel>
        docs: The model to use for TTS
      voice_id:
        type: optional<string>
        docs: The voice ID to use for TTS
        default: cjVigY5qzO86Huf0OWal
        validation:
          minLength: 0
      agent_output_audio_format:
        type: optional<TtsOutputFormat>
        docs: The audio format to use for TTS
      optimize_streaming_latency:
        type: optional<TtsOptimizeStreamingLatency>
        docs: The optimization for streaming latency
      stability:
        type: optional<double>
        docs: The stability of generated speech
        default: 0.5
        validation:
          min: 0
          max: 1
      speed:
        type: optional<double>
        docs: The speed of generated speech
        default: 1
        validation:
          min: 0.7
          max: 1.2
      similarity_boost:
        type: optional<double>
        docs: The similarity boost for generated speech
        default: 0.8
        validation:
          min: 0
          max: 1
      pronunciation_dictionary_locators:
        type: optional<list<PydanticPronunciationDictionaryVersionLocator>>
        docs: The pronunciation dictionary locators
    source:
      openapi: openapi.json
  TtsConversationalConfigOverride:
    properties:
      voice_id:
        type: optional<string>
    source:
      openapi: openapi.json
  TtsConversationalConfigOverrideConfig:
    properties:
      voice_id:
        type: optional<boolean>
        docs: Whether to allow overriding the voice ID
        default: false
    source:
      openapi: openapi.json
  TtsConversationalModel:
    enum:
      - eleven_turbo_v2
      - eleven_turbo_v2_5
      - eleven_flash_v2
      - eleven_flash_v2_5
    source:
      openapi: openapi.json
  TtsOptimizeStreamingLatency: integer
  TtsOutputFormat:
    enum:
      - pcm_8000
      - pcm_16000
      - pcm_22050
      - pcm_24000
      - pcm_44100
      - pcm_48000
      - ulaw_8000
    source:
      openapi: openapi.json
  TelephonyProvider:
    enum:
      - twilio
      - sip_trunk
    source:
      openapi: openapi.json
  ToolMockConfig:
    properties:
      default_return_value:
        type: optional<string>
        default: Tool Called.
      default_is_error:
        type: optional<boolean>
        default: false
    source:
      openapi: openapi.json
  TransferToAgentToolConfig:
    properties:
      transfers:
        type: list<AgentTransfer>
    source:
      openapi: openapi.json
  TransferToNumberToolConfig:
    properties:
      transfers:
        type: list<PhoneNumberTransfer>
    source:
      openapi: openapi.json
  TurnConfig:
    properties:
      turn_timeout:
        type: optional<double>
        docs: Maximum wait time for the user's reply before re-engaging the user
        default: 7
      silence_end_call_timeout:
        type: optional<double>
        docs: >-
          Maximum wait time since the user last spoke before terminating the
          call
        default: -1
      mode:
        type: optional<TurnMode>
        docs: The mode of turn detection
    source:
      openapi: openapi.json
  TurnMode:
    enum:
      - silence
      - turn
    source:
      openapi: openapi.json
  TwilioOutboundCallResponse:
    properties:
      success: boolean
      message: string
      callSid: optional<string>
    source:
      openapi: openapi.json
  TxtExportOptions:
    properties:
      max_characters_per_line:
        type: optional<integer>
      include_speakers:
        type: optional<boolean>
        default: true
      include_timestamps:
        type: optional<boolean>
        default: true
      segment_on_silence_longer_than_s:
        type: optional<double>
      max_segment_duration_s:
        type: optional<double>
      max_segment_chars:
        type: optional<integer>
    source:
      openapi: openapi.json
  UrlAvatar:
    properties:
      custom_url:
        type: optional<string>
        docs: The custom URL of the avatar
        default: ''
    source:
      openapi: openapi.json
  UpdateWorkspaceMemberResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the workspace member update request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  UsageAggregationInterval:
    enum:
      - hour
      - day
      - week
      - month
      - cumulative
    docs: The time interval over which to aggregate the usage data.
    source:
      openapi: openapi.json
  UsageCharactersResponseModel:
    properties:
      time:
        docs: The time axis with unix timestamps for each day.
        type: list<integer>
      usage:
        type: map<string, list<double>>
        docs: The usage of each breakdown type along the time axis.
    source:
      openapi: openapi.json
  UserFeedback:
    properties:
      score:
        type: UserFeedbackScore
      time_in_call_secs: integer
    source:
      openapi: openapi.json
  UserFeedbackScore:
    enum:
      - like
      - dislike
    source:
      openapi: openapi.json
  User:
    properties:
      user_id:
        type: string
        docs: The unique identifier of the user.
      subscription:
        type: SubscriptionResponse
        docs: Details of the user's subscription.
      subscription_extras:
        type: optional<SubscriptionExtrasResponseModel>
        docs: Optional additional details about the user's subscription.
      is_new_user:
        type: boolean
        docs: Whether the user is new.
      xi_api_key:
        type: optional<string>
        docs: The API key of the user.
      can_use_delayed_payment_methods:
        type: boolean
        docs: Whether the user can use delayed payment methods.
      is_onboarding_completed:
        type: boolean
        docs: Whether the user's onboarding is completed.
      is_onboarding_checklist_completed:
        type: boolean
        docs: Whether the user's onboarding checklist is completed.
      first_name:
        type: optional<string>
        docs: First name of the user.
      is_api_key_hashed:
        type: optional<boolean>
        docs: Whether the user's API key is hashed.
        default: false
      xi_api_key_preview:
        type: optional<string>
        docs: The preview of the user's API key.
      referral_link_code:
        type: optional<string>
        docs: The referral link code of the user.
      partnerstack_partner_default_link:
        type: optional<string>
        docs: The Partnerstack partner default link of the user.
    source:
      openapi: openapi.json
  UtteranceResponseModel:
    properties:
      start:
        type: double
        docs: The start time of the utterance in seconds.
      end:
        type: double
        docs: The end time of the utterance in seconds.
    source:
      openapi: openapi.json
  ValidationErrorLocItem:
    discriminated: false
    union:
      - string
      - integer
    source:
      openapi: openapi.json
    inline: true
  ValidationError:
    properties:
      loc:
        type: list<ValidationErrorLocItem>
      msg: string
      type: string
    source:
      openapi: openapi.json
  VerificationAttemptResponse:
    properties:
      text:
        type: string
        docs: The text of the verification attempt.
      date_unix:
        type: integer
        docs: The date of the verification attempt in Unix time.
      accepted:
        type: boolean
        docs: Whether the verification attempt was accepted.
      similarity:
        type: double
        docs: The similarity of the verification attempt.
      levenshtein_distance:
        type: double
        docs: The Levenshtein distance of the verification attempt.
      recording:
        type: optional<RecordingResponse>
        docs: The recording of the verification attempt.
    source:
      openapi: openapi.json
  VerifiedVoiceLanguageResponseModel:
    properties:
      language:
        type: string
        docs: The language of the voice.
      model_id:
        type: string
        docs: The voice's model ID.
      accent:
        type: optional<string>
        docs: The voice's accent, if applicable.
      locale:
        type: optional<string>
        docs: The voice's locale, if applicable.
      preview_url:
        type: optional<string>
        docs: The voice's preview URL, if applicable.
    source:
      openapi: openapi.json
  VerifyPvcVoiceCaptchaResponseModel:
    properties:
      status:
        type: string
        docs: >-
          The status of the verify PVC captcha request. If the request was
          successful, the status will be 'ok'. Otherwise an error message with
          status 500 will be returned.
    source:
      openapi: openapi.json
  VoiceGenerationParameterOptionResponse:
    properties:
      name: string
      code: string
    source:
      openapi: openapi.json
  VoiceGenerationParameterResponse:
    properties:
      genders:
        type: list<VoiceGenerationParameterOptionResponse>
      accents:
        type: list<VoiceGenerationParameterOptionResponse>
      ages:
        type: list<VoiceGenerationParameterOptionResponse>
      minimum_characters: integer
      maximum_characters: integer
      minimum_accent_strength: double
      maximum_accent_strength: double
    source:
      openapi: openapi.json
  VoicePreviewResponseModel:
    properties:
      audio_base_64: string
      generated_voice_id: string
      media_type: string
      duration_secs: double
    source:
      openapi: openapi.json
  VoiceDesignPreviewResponse:
    properties:
      previews:
        type: list<VoicePreviewResponseModel>
      text: string
    source:
      openapi: openapi.json
  VoiceResponseModelCategory:
    enum:
      - generated
      - cloned
      - premade
      - professional
      - famous
      - high_quality
    docs: The category of the voice.
    inline: true
    source:
      openapi: openapi.json
  VoiceResponseModelSafetyControl:
    enum:
      - NONE
      - BAN
      - CAPTCHA
      - CAPTCHA_AND_MODERATION
      - ENTERPRISE_BAN
      - ENTERPRISE_CAPTCHA
    inline: true
    source:
      openapi: openapi.json
  Voice:
    properties:
      voice_id:
        type: string
        docs: The ID of the voice.
      name:
        type: optional<string>
        docs: The name of the voice.
      samples:
        type: optional<list<VoiceSample>>
        docs: List of samples associated with the voice.
      category:
        type: optional<VoiceResponseModelCategory>
        docs: The category of the voice.
      fine_tuning:
        type: optional<FineTuningResponse>
        docs: Fine-tuning information for the voice.
      labels:
        type: optional<map<string, string>>
        docs: Labels associated with the voice.
      description:
        type: optional<string>
        docs: The description of the voice.
      preview_url:
        type: optional<string>
        docs: The preview URL of the voice.
      available_for_tiers:
        type: optional<list<string>>
        docs: The tiers the voice is available for.
      settings:
        type: optional<VoiceSettings>
        docs: The settings of the voice.
      sharing:
        type: optional<VoiceSharingResponse>
        docs: The sharing information of the voice.
      high_quality_base_model_ids:
        type: optional<list<string>>
        docs: The base model IDs for high-quality voices.
      verified_languages:
        type: optional<list<VerifiedVoiceLanguageResponseModel>>
        docs: The verified languages of the voice.
      safety_control:
        type: optional<VoiceResponseModelSafetyControl>
        docs: The safety controls of the voice.
      voice_verification:
        type: optional<VoiceVerificationResponse>
        docs: The voice verification of the voice.
      permission_on_resource:
        type: optional<string>
        docs: The permission on the resource of the voice.
      is_owner:
        type: optional<boolean>
        docs: Whether the voice is owned by the user.
      is_legacy:
        type: optional<boolean>
        docs: Whether the voice is legacy.
        default: false
      is_mixed:
        type: optional<boolean>
        docs: Whether the voice is mixed.
        default: false
      created_at_unix:
        type: optional<integer>
        docs: The creation time of the voice in Unix time.
    source:
      openapi: openapi.json
  VoiceSamplePreviewResponseModel:
    properties:
      audio_base_64:
        type: string
        docs: The base64 encoded audio.
      voice_id:
        type: string
        docs: The ID of the voice.
      sample_id:
        type: string
        docs: The ID of the sample.
      media_type:
        type: string
        docs: The media type of the audio.
      duration_secs:
        type: optional<double>
        docs: The duration of the audio in seconds.
    source:
      openapi: openapi.json
  VoiceSampleVisualWaveformResponseModel:
    properties:
      sample_id:
        type: string
        docs: The ID of the sample.
      visual_waveform:
        docs: The visual waveform of the sample, represented as a list of floats.
        type: list<double>
    source:
      openapi: openapi.json
  VoiceSettings:
    properties:
      stability:
        type: optional<double>
        docs: >-
          Determines how stable the voice is and the randomness between each
          generation. Lower values introduce broader emotional range for the
          voice. Higher values can result in a monotonous voice with limited
          emotion.
      similarity_boost:
        type: optional<double>
        docs: >-
          Determines how closely the AI should adhere to the original voice when
          attempting to replicate it.
      style:
        type: optional<double>
        docs: >-
          Determines the style exaggeration of the voice. This setting attempts
          to amplify the style of the original speaker. It does consume
          additional computational resources and might increase latency if set
          to anything other than 0.
      use_speaker_boost:
        type: optional<boolean>
        docs: >-
          This setting boosts the similarity to the original speaker. Using this
          setting requires a slightly higher computational load, which in turn
          increases latency.
      speed:
        type: optional<double>
        docs: >-
          Adjusts the speed of the voice. A value of 1.0 is the default speed,
          while values less than 1.0 slow down the speech, and values greater
          than 1.0 speed it up.
    source:
      openapi: openapi.json
  VoiceSharingModerationCheckResponseModel:
    properties:
      date_checked_unix:
        type: optional<integer>
        docs: The date the moderation check was made in Unix time.
      name_value:
        type: optional<string>
        docs: The name value of the voice.
      name_check:
        type: optional<boolean>
        docs: Whether the name check was successful.
      description_value:
        type: optional<string>
        docs: The description value of the voice.
      description_check:
        type: optional<boolean>
        docs: Whether the description check was successful.
      sample_ids:
        type: optional<list<string>>
        docs: A list of sample IDs.
      sample_checks:
        type: optional<list<double>>
        docs: A list of sample checks.
      captcha_ids:
        type: optional<list<string>>
        docs: A list of captcha IDs.
      captcha_checks:
        type: optional<list<double>>
        docs: A list of CAPTCHA check values.
    source:
      openapi: openapi.json
  voice_sharing_state:
    enum:
      - enabled
      - disabled
      - copied
      - copied_disabled
    docs: The status of the voice sharing.
    inline: true
    source:
      openapi: openapi.json
  VoiceSharingResponseModelCategory:
    enum:
      - generated
      - cloned
      - premade
      - professional
      - famous
      - high_quality
    docs: The category of the voice.
    inline: true
    source:
      openapi: openapi.json
  review_status:
    enum:
      - not_requested
      - pending
      - declined
      - allowed
      - allowed_with_changes
    docs: The review status of the voice.
    inline: true
    source:
      openapi: openapi.json
  VoiceSharingResponse:
    properties:
      status:
        type: optional<voice_sharing_state>
        docs: The status of the voice sharing.
      history_item_sample_id:
        type: optional<string>
        docs: The sample ID of the history item.
      date_unix:
        type: optional<integer>
        docs: The date of the voice sharing in Unix time.
      whitelisted_emails:
        type: optional<list<string>>
        docs: A list of whitelisted emails.
      public_owner_id:
        type: optional<string>
        docs: The ID of the public owner.
      original_voice_id:
        type: optional<string>
        docs: The ID of the original voice.
      financial_rewards_enabled:
        type: optional<boolean>
        docs: Whether financial rewards are enabled.
      free_users_allowed:
        type: optional<boolean>
        docs: Whether free users are allowed.
      live_moderation_enabled:
        type: optional<boolean>
        docs: Whether live moderation is enabled.
      rate:
        type: optional<double>
        docs: The rate of the voice sharing.
      notice_period:
        type: optional<integer>
        docs: The notice period of the voice sharing.
      disable_at_unix:
        type: optional<integer>
        docs: The date of the voice sharing in Unix time.
      voice_mixing_allowed:
        type: optional<boolean>
        docs: Whether voice mixing is allowed.
      featured:
        type: optional<boolean>
        docs: Whether the voice is featured.
      category:
        type: optional<VoiceSharingResponseModelCategory>
        docs: The category of the voice.
      reader_app_enabled:
        type: optional<boolean>
        docs: Whether the reader app is enabled.
      image_url:
        type: optional<string>
        docs: The image URL of the voice.
      ban_reason:
        type: optional<string>
        docs: The ban reason of the voice.
      liked_by_count:
        type: optional<integer>
        docs: The number of likes on the voice.
      cloned_by_count:
        type: optional<integer>
        docs: The number of clones on the voice.
      name:
        type: optional<string>
        docs: The name of the voice.
      description:
        type: optional<string>
        docs: The description of the voice.
      labels:
        type: optional<map<string, string>>
        docs: The labels of the voice.
      review_status:
        type: optional<review_status>
        docs: The review status of the voice.
      review_message:
        type: optional<string>
        docs: The review message of the voice.
      enabled_in_library:
        type: optional<boolean>
        docs: Whether the voice is enabled in the library.
      instagram_username:
        type: optional<string>
        docs: The Instagram username of the voice.
      twitter_username:
        type: optional<string>
        docs: The Twitter/X username of the voice.
      youtube_username:
        type: optional<string>
        docs: The YouTube username of the voice.
      tiktok_username:
        type: optional<string>
        docs: The TikTok username of the voice.
      moderation_check:
        type: optional<VoiceSharingModerationCheckResponseModel>
        docs: The moderation check of the voice.
      reader_restricted_on:
        type: optional<list<ReaderResourceResponseModel>>
        docs: The reader restricted on of the voice.
    source:
      openapi: openapi.json
  VoiceVerificationResponse:
    properties:
      requires_verification:
        type: boolean
        docs: Whether the voice requires verification.
      is_verified:
        type: boolean
        docs: Whether the voice has been verified.
      verification_failures:
        docs: List of verification failures.
        type: list<string>
      verification_attempts_count:
        type: integer
        docs: The number of verification attempts.
      language:
        type: optional<string>
        docs: The language of the voice.
      verification_attempts:
        type: optional<list<VerificationAttemptResponse>>
        docs: Number of times a verification was attempted.
    source:
      openapi: openapi.json
  WebhookToolApiSchemaConfigInputMethod:
    enum:
      - GET
      - POST
      - PUT
      - PATCH
      - DELETE
    docs: The HTTP method to use for the webhook
    default: GET
    inline: true
    source:
      openapi: openapi.json
  WebhookToolApiSchemaConfigInputRequestHeadersValue:
    discriminated: false
    union:
      - string
      - type: ConvAiSecretLocator
    source:
      openapi: openapi.json
    inline: true
  WebhookToolApiSchemaConfigInput:
    docs: Configuration for a webhook that will be called by an LLM tool.
    properties:
      url:
        type: string
        docs: >-
          The URL that the webhook will be sent to. May include path parameters,
          e.g. https://example.com/agents/{agent_id}
      method:
        type: optional<WebhookToolApiSchemaConfigInputMethod>
        docs: The HTTP method to use for the webhook
        default: GET
      path_params_schema:
        type: optional<map<string, LiteralJsonSchemaProperty>>
        docs: >-
          Schema for path parameters, if any. The keys should match the
          placeholders in the URL.
      query_params_schema:
        type: optional<QueryParamsJsonSchema>
        docs: >-
          Schema for any query params, if any. These will be added to end of the
          URL as query params. Note: properties in a query param must all be
          literal types
      request_body_schema:
        type: optional<ObjectJsonSchemaPropertyInput>
        docs: >-
          Schema for the body parameters, if any. Used for POST/PATCH/PUT
          requests. The schema should be an object which will be sent as the
          json body
      request_headers:
        type: >-
          optional<map<string,
          WebhookToolApiSchemaConfigInputRequestHeadersValue>>
        docs: Headers that should be included in the request
    source:
      openapi: openapi.json
  WebhookToolApiSchemaConfigOutputMethod:
    enum:
      - GET
      - POST
      - PUT
      - PATCH
      - DELETE
    docs: The HTTP method to use for the webhook
    default: GET
    inline: true
    source:
      openapi: openapi.json
  WebhookToolApiSchemaConfigOutputRequestHeadersValue:
    discriminated: false
    union:
      - string
      - type: ConvAiSecretLocator
    source:
      openapi: openapi.json
    inline: true
  WebhookToolApiSchemaConfigOutput:
    docs: Configuration for a webhook that will be called by an LLM tool.
    properties:
      url:
        type: string
        docs: >-
          The URL that the webhook will be sent to. May include path parameters,
          e.g. https://example.com/agents/{agent_id}
      method:
        type: optional<WebhookToolApiSchemaConfigOutputMethod>
        docs: The HTTP method to use for the webhook
        default: GET
      path_params_schema:
        type: optional<map<string, LiteralJsonSchemaProperty>>
        docs: >-
          Schema for path parameters, if any. The keys should match the
          placeholders in the URL.
      query_params_schema:
        type: optional<QueryParamsJsonSchema>
        docs: >-
          Schema for any query params, if any. These will be added to end of the
          URL as query params. Note: properties in a query param must all be
          literal types
      request_body_schema:
        type: optional<ObjectJsonSchemaPropertyOutput>
        docs: >-
          Schema for the body parameters, if any. Used for POST/PATCH/PUT
          requests. The schema should be an object which will be sent as the
          json body
      request_headers:
        type: >-
          optional<map<string,
          WebhookToolApiSchemaConfigOutputRequestHeadersValue>>
        docs: Headers that should be included in the request
    source:
      openapi: openapi.json
  WebhookToolConfigInput:
    docs: A webhook tool is a tool that calls an external webhook from our server
    properties:
      id:
        type: optional<string>
        default: ''
      name:
        type: string
        validation:
          pattern: ^[a-zA-Z0-9_-]{1,64}$
          minLength: 0
      description:
        type: string
        validation:
          minLength: 0
      response_timeout_secs:
        type: optional<integer>
        docs: >-
          The maximum time in seconds to wait for the tool call to complete.
          Must be between 5 and 120 seconds (inclusive).
        default: 20
        validation:
          min: 5
          max: 120
      api_schema:
        type: WebhookToolApiSchemaConfigInput
        docs: >-
          The schema for the outgoing webhoook, including parameters and URL
          specification
      dynamic_variables:
        type: optional<DynamicVariablesConfig>
        docs: Configuration for dynamic variables
    source:
      openapi: openapi.json
  WebhookToolConfigOutput:
    docs: A webhook tool is a tool that calls an external webhook from our server
    properties:
      id:
        type: optional<string>
        default: ''
      name:
        type: string
        validation:
          pattern: ^[a-zA-Z0-9_-]{1,64}$
          minLength: 0
      description:
        type: string
        validation:
          minLength: 0
      response_timeout_secs:
        type: optional<integer>
        docs: >-
          The maximum time in seconds to wait for the tool call to complete.
          Must be between 5 and 120 seconds (inclusive).
        default: 20
        validation:
          min: 5
          max: 120
      api_schema:
        type: WebhookToolApiSchemaConfigOutput
        docs: >-
          The schema for the outgoing webhoook, including parameters and URL
          specification
      dynamic_variables:
        type: optional<DynamicVariablesConfig>
        docs: Configuration for dynamic variables
    source:
      openapi: openapi.json
  WidgetConfigAvatar:
    discriminant: type
    base-properties: {}
    docs: The avatar of the widget
    union:
      orb:
        type: OrbAvatar
      url:
        type: UrlAvatar
      image:
        type: ImageAvatar
    source:
      openapi: openapi.json
  WidgetConfig:
    properties:
      variant:
        type: optional<EmbedVariant>
        docs: The variant of the widget
      expandable:
        type: optional<WidgetExpandable>
        docs: Whether the widget is expandable
      avatar:
        type: optional<WidgetConfigAvatar>
        docs: The avatar of the widget
      feedback_mode:
        type: optional<WidgetFeedbackMode>
        docs: The feedback mode of the widget
      bg_color:
        type: optional<string>
        docs: The background color of the widget
        default: '#ffffff'
      text_color:
        type: optional<string>
        docs: The text color of the widget
        default: '#000000'
      btn_color:
        type: optional<string>
        docs: The button color of the widget
        default: '#000000'
      btn_text_color:
        type: optional<string>
        docs: The button text color of the widget
        default: '#ffffff'
      border_color:
        type: optional<string>
        docs: The border color of the widget
        default: '#e1e1e1'
      focus_color:
        type: optional<string>
        docs: The focus color of the widget
        default: '#000000'
      border_radius:
        type: optional<integer>
        docs: The border radius of the widget
      btn_radius:
        type: optional<integer>
        docs: The button radius of the widget
      action_text:
        type: optional<string>
        docs: The action text of the widget
      start_call_text:
        type: optional<string>
        docs: The start call text of the widget
      end_call_text:
        type: optional<string>
        docs: The end call text of the widget
      expand_text:
        type: optional<string>
        docs: The expand text of the widget
      listening_text:
        type: optional<string>
        docs: The text to display when the agent is listening
      speaking_text:
        type: optional<string>
        docs: The text to display when the agent is speaking
      shareable_page_text:
        type: optional<string>
        docs: The text to display when sharing
      shareable_page_show_terms:
        type: optional<boolean>
        docs: Whether to show terms and conditions on the shareable page
        default: true
      terms_text:
        type: optional<string>
        docs: The text to display for terms and conditions
      terms_html:
        type: optional<string>
        docs: The HTML to display for terms and conditions
      terms_key:
        type: optional<string>
        docs: The key to display for terms and conditions
      show_avatar_when_collapsed:
        type: optional<boolean>
        docs: Whether to show the avatar when the widget is collapsed
      disable_banner:
        type: optional<boolean>
        docs: Whether to disable the banner
        default: false
      mic_muting_enabled:
        type: optional<boolean>
        docs: Whether to enable mic muting
        default: false
      language_selector:
        type: optional<boolean>
        docs: Whether to show the language selector
        default: false
      custom_avatar_path:
        type: optional<string>
        docs: The custom avatar path
    source:
      openapi: openapi.json
  WidgetConfigResponseModelAvatar:
    discriminant: type
    base-properties: {}
    docs: The avatar of the widget
    union:
      orb:
        type: OrbAvatar
      url:
        type: UrlAvatar
      image:
        type: ImageAvatar
    source:
      openapi: openapi.json
  WidgetConfigResponseModel:
    properties:
      variant:
        type: optional<EmbedVariant>
        docs: The variant of the widget
      expandable:
        type: optional<WidgetExpandable>
        docs: Whether the widget is expandable
      avatar:
        type: optional<WidgetConfigResponseModelAvatar>
        docs: The avatar of the widget
      feedback_mode:
        type: optional<WidgetFeedbackMode>
        docs: The feedback mode of the widget
      bg_color:
        type: optional<string>
        docs: The background color of the widget
        default: '#ffffff'
      text_color:
        type: optional<string>
        docs: The text color of the widget
        default: '#000000'
      btn_color:
        type: optional<string>
        docs: The button color of the widget
        default: '#000000'
      btn_text_color:
        type: optional<string>
        docs: The button text color of the widget
        default: '#ffffff'
      border_color:
        type: optional<string>
        docs: The border color of the widget
        default: '#e1e1e1'
      focus_color:
        type: optional<string>
        docs: The focus color of the widget
        default: '#000000'
      border_radius:
        type: optional<integer>
        docs: The border radius of the widget
      btn_radius:
        type: optional<integer>
        docs: The button radius of the widget
      action_text:
        type: optional<string>
        docs: The action text of the widget
      start_call_text:
        type: optional<string>
        docs: The start call text of the widget
      end_call_text:
        type: optional<string>
        docs: The end call text of the widget
      expand_text:
        type: optional<string>
        docs: The expand text of the widget
      listening_text:
        type: optional<string>
        docs: The text to display when the agent is listening
      speaking_text:
        type: optional<string>
        docs: The text to display when the agent is speaking
      shareable_page_text:
        type: optional<string>
        docs: The text to display when sharing
      shareable_page_show_terms:
        type: optional<boolean>
        docs: Whether to show terms and conditions on the shareable page
        default: true
      terms_text:
        type: optional<string>
        docs: The text to display for terms and conditions
      terms_html:
        type: optional<string>
        docs: The HTML to display for terms and conditions
      terms_key:
        type: optional<string>
        docs: The key to display for terms and conditions
      show_avatar_when_collapsed:
        type: optional<boolean>
        docs: Whether to show the avatar when the widget is collapsed
      disable_banner:
        type: optional<boolean>
        docs: Whether to disable the banner
        default: false
      mic_muting_enabled:
        type: optional<boolean>
        docs: Whether to enable mic muting
        default: false
      language: string
      supported_language_overrides:
        type: optional<list<string>>
    source:
      openapi: openapi.json
  WidgetExpandable:
    enum:
      - never
      - mobile
      - desktop
      - always
    source:
      openapi: openapi.json
  WidgetFeedbackMode:
    enum:
      - none
      - during
      - end
    source:
      openapi: openapi.json
  WorkspaceGroupByNameResponseModel:
    properties:
      name:
        type: string
        docs: The name of the workspace group.
      id:
        type: string
        docs: The ID of the workspace group.
      members_emails:
        docs: The emails of the members of the workspace group.
        type: list<string>
    source:
      openapi: openapi.json
  WorkspaceResourceType:
    enum:
      - voice
      - voice_collection
      - pronunciation_dictionary
      - dubbing
      - project
      - convai_agents
      - convai_knowledge_base_documents
      - convai_tools
      - convai_settings
      - convai_secrets
      - music_latent
      - convai_phone_numbers
      - convai_mcps
      - convai_batch_calls
    docs: >-
      Resource types that can be shared in the workspace. The name always need
      to match the collection names
    source:
      openapi: openapi.json
  OutputFormat:
    enum:
      - value: mp3_22050_32
        docs: Output format, mp3 with 22.05kHz sample rate at 32kbps
      - value: mp3_44100_32
        docs: Output format, mp3 with 44.1kHz sample rate at 32kbps
      - value: mp3_44100_64
        docs: Output format, mp3 with 44.1kHz sample rate at 64kbps
      - value: mp3_44100_96
        docs: Output format, mp3 with 44.1kHz sample rate at 96kbps
      - value: mp3_44100_128
        docs: Default output format, mp3 with 44.1kHz sample rate at 128kbps
      - value: mp3_44100_192
        docs: |
          Output format, mp3 with 44.1kHz sample rate at 192kbps.
      - value: pcm_16000
        docs: |
          PCM format (S16LE) with 16kHz sample rate.
      - value: pcm_22050
        docs: |
          PCM format (S16LE) with 22.05kHz sample rate.
      - value: pcm_24000
        docs: |
          PCM format (S16LE) with 24kHz sample rate.
      - value: pcm_44100
        docs: >
          PCM format (S16LE) with 44.1kHz sample rate. Requires you to be
          subscribed to Independent Publisher tier or above.
      - value: ulaw_8000
        docs: >
          μ-law format (sometimes written mu-law, often approximated as u-law)
          with 8kHz sample rate. Note that this format is commonly used for
          Twilio audio inputs.
    source:
      openapi: openapi.json
  HistoryItemResponse:
    properties:
      state: optional<unknown>
      voice_category: optional<unknown>
    source:
      openapi: openapi.json
  Age: unknown
  Gender: unknown
  AddSharingVoiceRequest: unknown
  CreateAudioNativeProjectRequest: unknown
  TextToSpeechStreamRequest: unknown
  EditVoiceSettingsRequest: unknown
  GetChaptersRequest: unknown
  GetChapterRequest: unknown
  DeleteChapterRequest: unknown
  GetChapterSnapshotsRequest: unknown
  GetProjectsRequest: unknown
  GetProjectRequest: unknown
  DeleteProjectRequest: unknown
  CreateTranscriptRequest: unknown
  RemoveMemberFromGroupRequest: unknown
  UpdateAudioNativeProjectRequest: unknown
  UpdateProjectRequest: unknown
  UpdateChapterRequest: unknown
  CharacterUsageResponse: unknown
  GetPronunciationDictionariesResponse: unknown
  GetPronunciationDictionaryResponse: unknown
  InitializeConnection:
    properties:
      text:
        type: literal<" ">
        docs: The initial text that must be sent is a blank space.
      voice_settings: optional<RealtimeVoiceSettings>
      generation_config: optional<GenerationConfig>
      pronunciation_dictionary_locators:
        type: optional<list<PronunciationDictionaryLocator>>
        docs: >
          Optional list of pronunciation dictionary locators. If provided, these
          dictionaries will be used to

          modify pronunciation of matching text. Must only be provided in the
          first message.


          Note: Pronunciation dictionary matches will only be respected within a
          provided chunk.
      xi-api-key:
        type: optional<string>
        docs: >
          Your ElevenLabs API key. This can only be included in the first
          message and is not needed if present in the header.
      authorization:
        type: optional<string>
        docs: >
          Your authorization bearer token. This can only be included in the
          first message and is not needed if present in the header.
    source:
      openapi: asyncapi.yml
  CloseConnection:
    properties:
      text:
        type: literal<"">
        docs: End the stream with an empty string
    source:
      openapi: asyncapi.yml
  SendText:
    properties:
      text:
        type: string
        docs: >-
          The text to be sent to the API for audio generation. Should always end
          with a single space string.
      try_trigger_generation:
        type: optional<boolean>
        docs: >
          This is an advanced setting that most users shouldn't need to use. It
          relates to our generation schedule.


          Use this to attempt to immediately trigger the generation of audio,
          overriding the `chunk_length_schedule`.

          Unlike flush, `try_trigger_generation` will only generate audio if our

          buffer contains more than a minimum

          threshold of characters, this is to ensure a higher quality response
          from our model.


          Note that overriding the chunk schedule to generate small amounts of

          text may result in lower quality audio, therefore, only use this
          parameter if you

          really need text to be processed immediately. We generally recommend
          keeping the default value of

          `false` and adjusting the `chunk_length_schedule` in the
          `generation_config` instead.
        default: false
      voice_settings:
        type: optional<RealtimeVoiceSettings>
        docs: >-
          The voice settings field can be provided in the first
          `InitializeConnection` message and then must either be not provided or
          not changed.
      generator_config:
        type: optional<GenerationConfig>
        docs: >-
          The generator config field can be provided in the first
          `InitializeConnection` message and then must either be not provided or
          not changed.
      flush:
        type: optional<boolean>
        docs: >
          Flush forces the generation of audio. Set this value to true when you
          have finished sending text, but want to keep the websocket connection
          open.


          This is useful when you want to ensure that the last chunk of audio is
          generated even when the length of text sent is smaller than the value
          set in chunk_length_schedule (e.g. 120 or 50).
        default: false
    source:
      openapi: asyncapi.yml
  RealtimeVoiceSettings:
    properties:
      stability:
        type: optional<double>
        docs: Defines the stability for voice settings.
        default: 0.5
      similarity_boost:
        type: optional<double>
        docs: Defines the similarity boost for voice settings.
        default: 0.75
      style:
        type: optional<double>
        docs: >-
          Defines the style for voice settings. This parameter is available on
          V2+ models.
        default: 0
      use_speaker_boost:
        type: optional<boolean>
        docs: >-
          Defines the use speaker boost for voice settings. This parameter is
          available on V2+ models.
        default: true
      speed:
        type: optional<double>
        docs: >-
          Controls the speed of the generated speech. Values range from 0.7 to
          1.2, with 1.0 being the default speed.
        default: 1
    source:
      openapi: asyncapi.yml
  GenerationConfig:
    properties:
      chunk_length_schedule:
        type: optional<list<double>>
        docs: >
          This is an advanced setting that most users shouldn't need to use. It
          relates to our

          generation schedule.


          Our WebSocket service incorporates a buffer system designed to
          optimize the Time To First Byte (TTFB) while maintaining high-quality
          streaming.


          All text sent to the WebSocket endpoint is added to this buffer and
          only when that buffer reaches a certain size is an audio generation
          attempted. This is because our model provides higher quality audio
          when the model has longer inputs, and can deduce more context about
          how the text should be delivered.


          The buffer ensures smooth audio data delivery and is automatically
          emptied with a final audio generation either when the stream is
          closed, or upon sending a `flush` command. We have advanced settings
          for changing the chunk schedule, which can improve latency at the cost
          of quality by generating audio more frequently with smaller text
          inputs.


          The `chunk_length_schedule` determines the minimum amount of text that
          needs to be sent and present in our

          buffer before audio starts being generated. This is to maximise the
          amount of context available to

          the model to improve audio quality, whilst balancing latency of the
          returned audio chunks.


          The default value for `chunk_length_schedule` is: [120, 160, 250,
          290].


          This means that the first chunk of audio will not be generated until
          you send text that

          totals at least 120 characters long. The next chunk of audio will only
          be generated once a

          further 160 characters have been sent. The third audio chunk will be
          generated after the

          next 250 characters. Then the fourth, and beyond, will be generated in
          sets of at least 290 characters.


          Customize this array to suit your needs. If you want to generate audio
          more frequently

          to optimise latency, you can reduce the values in the array. Note that
          setting the values

          too low may result in lower quality audio. Please test and adjust as
          needed.


          Each item should be in the range 50-500.
    source:
      openapi: asyncapi.yml
  AudioOutput:
    properties:
      audio:
        type: string
        docs: >
          A generated partial audio chunk, encoded using the selected
          output_format, by default this

          is MP3 encoded as a base64 string.
      normalizedAlignment: optional<NormalizedAlignment>
      alignment: optional<Alignment>
    source:
      openapi: asyncapi.yml
  FinalOutput:
    properties:
      isFinal:
        type: optional<literal<true>>
        docs: >
          Indicates if the generation is complete. If set to `True`, `audio`
          will be null.
    source:
      openapi: asyncapi.yml
  NormalizedAlignment:
    docs: >
      Alignment information for the generated audio given the input normalized
      text sequence.
    properties:
      charStartTimesMs:
        type: optional<list<integer>>
        docs: >
          A list of starting times (in milliseconds) for each character in the
          normalized text as it

          corresponds to the audio. For instance, the character 'H' starts at
          time 0 ms in the audio.

          Note these times are relative to the returned chunk from the model,
          and not the

          full audio response.
      charsDurationsMs:
        type: optional<list<integer>>
        docs: >
          A list of durations (in milliseconds) for each character in the
          normalized text as it

          corresponds to the audio. For instance, the character 'H' lasts for 3
          ms in the audio.

          Note these times are relative to the returned chunk from the model,
          and not the

          full audio response.
      chars:
        type: optional<list<string>>
        docs: >
          A list of characters in the normalized text sequence. For instance,
          the first character is 'H'.

          Note that this list may contain spaces, punctuation, and other special
          characters.

          The length of this list should be the same as the lengths of
          `charStartTimesMs` and `charsDurationsMs`.
    source:
      openapi: asyncapi.yml
  Alignment:
    docs: >
      Alignment information for the generated audio given the input text
      sequence.
    properties:
      charStartTimesMs:
        type: optional<list<integer>>
        docs: >
          A list of starting times (in milliseconds) for each character in the
          text as it

          corresponds to the audio. For instance, the character 'H' starts at
          time 0 ms in the audio.

          Note these times are relative to the returned chunk from the model,
          and not the

          full audio response.
      charsDurationsMs:
        type: optional<list<integer>>
        docs: >
          A list of durations (in milliseconds) for each character in the text
          as it

          corresponds to the audio. For instance, the character 'H' lasts for 3
          ms in the audio.

          Note these times are relative to the returned chunk from the model,
          and not the

          full audio response.
      chars:
        type: optional<list<string>>
        docs: >
          A list of characters in the text sequence. For instance, the first
          character is 'H'.

          Note that this list may contain spaces, punctuation, and other special
          characters.

          The length of this list should be the same as the lengths of
          `charStartTimesMs` and `charsDurationsMs`.
    source:
      openapi: asyncapi.yml
  PronunciationDictionaryLocator:
    docs: Identifies a specific pronunciation dictionary to use
    properties:
      dictionary_id:
        type: string
        docs: The unique identifier of the pronunciation dictionary
      version_id:
        type: string
        docs: The version identifier of the pronunciation dictionary
    source:
      openapi: asyncapi.yml
  WebsocketTtsClientMessageMulti:
    docs: Message sent from the client to the multi-context TTS WebSocket.
    properties:
      text:
        type: optional<string>
        docs: >
          Text to be synthesized. 

          For the first message establishing a new context (identified by
          `context_id`, or a default context if `context_id` is absent), this
          should be a single space character (' '). 

          For subsequent messages to an active context, this is the text to
          synthesize. 

          This field can be null or an empty string if the message is primarily
          for control (e.g., using `flush`, `close_context`, or `close_socket`).
      voice_settings:
        type: optional<RealtimeVoiceSettings>
        docs: >-
          Voice settings. Can only be provided in the first message for a given
          context_id (or first message overall if context_id is not
          used/default).
      generation_config:
        type: optional<GenerationConfig>
        docs: >-
          Generation config. Can only be provided in the first message for a
          given context_id (or first message overall if context_id is not
          used/default).
      xi-api-key:
        type: optional<string>
        docs: >-
          Your ElevenLabs API key. Can only be provided in the first message for
          a given context_id if not present in the header.
      authorization:
        type: optional<string>
        docs: >-
          Your authorization bearer token. Can only be provided in the first
          message for a given context_id if not present in the header.
      flush:
        type: optional<boolean>
        docs: >-
          If true, flushes the audio buffer and returns the remaining audio for
          the specified `context_id`.
        default: false
      pronunciation_dictionary_locators:
        type: optional<list<PronunciationDictionaryLocator>>
        docs: >-
          Optional list of pronunciation dictionary locators. Can only be
          provided in the first message for a given context_id.
      context_id:
        type: optional<string>
        docs: >-
          An identifier for the text-to-speech context. Allows managing multiple
          independent audio generation streams over a single WebSocket
          connection. If omitted, a default context is used.
      close_context:
        type: optional<boolean>
        docs: >-
          If true, closes the specified `context_id`. No further audio will be
          generated for this context. The `text` field is ignored.
        default: false
      close_socket:
        type: optional<boolean>
        docs: >-
          If true, flushes all contexts and closes the entire WebSocket
          connection. The `text` and `context_id` fields are ignored.
        default: false
    source:
      openapi: asyncapi.yml
  WebsocketTtsServerMessageMulti:
    docs: >-
      Message sent from the server to the client for the multi-context TTS
      WebSocket.
    properties:
      audio:
        type: optional<string>
        docs: >-
          A generated partial audio chunk, encoded using the selected
          output_format (e.g., MP3 as a base64 string).
      is_final:
        type: optional<boolean>
        docs: >-
          If true, indicates that this is the final message for the specified
          `context_id`. This is sent when a context is closed. `audio` will be
          null or empty.
      normalizedAlignment: optional<NormalizedAlignment>
      alignment: optional<Alignment>
      context_id:
        type: optional<string>
        docs: The context identifier to which this message pertains.
    source:
      openapi: asyncapi.yml
  InitializeConnectionMulti:
    docs: >-
      Payload to initialize a new context in a multi-stream WebSocket
      connection.
    properties:
      text:
        type: literal<" ">
        docs: Must be a single space character to initiate the context.
      voice_settings: optional<RealtimeVoiceSettings>
      generation_config: optional<GenerationConfig>
      pronunciation_dictionary_locators:
        type: optional<list<PronunciationDictionaryLocator>>
        docs: Optional pronunciation dictionaries for this context.
      xi_api_key:
        type: optional<string>
        docs: >-
          Your ElevenLabs API key (if not in header). For this context's first
          message only.
      authorization:
        type: optional<string>
        docs: >-
          Your authorization bearer token (if not in header). For this context's
          first message only.
      context_id:
        type: optional<string>
        docs: >-
          A unique identifier for the first context created in the websocket. If
          not provided, a default context will be used.
    source:
      openapi: asyncapi.yml
  SendTextMulti:
    docs: Payload to send text for synthesis to an existing context.
    properties:
      text:
        type: string
        docs: Text to synthesize. Should end with a single space.
      context_id:
        type: optional<string>
        docs: The target context_id for this text.
      flush:
        type: optional<boolean>
        docs: >-
          If true, flushes the audio buffer for the specified context. If false,
          the text will be appended to the buffer to be generated.
        default: false
    source:
      openapi: asyncapi.yml
  FlushContext:
    docs: Payload to flush the audio buffer for a specific context.
    properties:
      context_id:
        type: string
        docs: The context_id to flush.
      text:
        type: optional<string>
        docs: The text to append to the buffer to be flushed.
      flush:
        type: boolean
        docs: >-
          If true, flushes the audio buffer for the specified context. If false,
          the context will remain open and the text will be appended to the
          buffer to be generated.
        default: false
    source:
      openapi: asyncapi.yml
  CloseContext:
    docs: Payload to close a specific TTS context.
    properties:
      context_id:
        type: string
        docs: The context_id to close.
      close_context:
        type: boolean
        docs: >-
          Must set the close_context to true, to close the specified context. If
          false, the context will remain open and the text will be ignored. If
          set to true, the context will close. If it has already been set to
          flush it will continue flushing. The same context id can be used again
          but will not be linked to the previous context with the same name.
        default: false
    source:
      openapi: asyncapi.yml
  CloseSocket:
    docs: Payload to signal closing the entire WebSocket connection.
    properties:
      close_socket:
        type: optional<boolean>
        docs: >-
          If true, closes all contexts and closes the entire WebSocket
          connection. Any context that was previously set to flush will wait to
          flush before closing.
        default: false
    source:
      openapi: asyncapi.yml
  AudioOutputMulti:
    docs: Server payload containing an audio chunk for a specific context.
    properties:
      audio:
        type: string
        docs: Base64 encoded audio chunk.
      normalizedAlignment: optional<NormalizedAlignment>
      alignment: optional<Alignment>
      context_id:
        type: optional<string>
        docs: The context_id for which this audio is.
    source:
      openapi: asyncapi.yml
  FinalOutputMulti:
    docs: Server payload indicating the final output for a specific context.
    properties:
      isFinal:
        type: literal<true>
        docs: Indicates this is the final message for the context.
      context_id:
        type: optional<string>
        docs: The context_id for which this is the final message.
    source:
      openapi: asyncapi.yml
  KeepContextAlive:
    docs: >-
      Payload to keep a specific context alive by resetting its inactivity
      timeout. Empty text is ignored but resets the clock.
    properties:
      text:
        type: literal<"">
        docs: >-
          An empty string. This text is ignored by the server but its presence
          resets the inactivity timeout for the specified context.
      context_id:
        type: string
        docs: The identifier of the context to keep alive.
    source:
      openapi: asyncapi.yml
  InitialiseContext:
    docs: >-
      Payload to initialize or re-initialize a TTS context with specific
      settings and initial text for multi-stream connections.
    properties:
      text:
        type: string
        docs: The initial text to synthesize. Should end with a single space.
      voice_settings: optional<RealtimeVoiceSettings>
      generation_config: optional<GenerationConfig>
      pronunciation_dictionary_locators:
        type: optional<list<PronunciationDictionaryLocator>>
        docs: >-
          Optional list of pronunciation dictionary locators to be used for this
          context.
      xi_api_key:
        type: optional<string>
        docs: >-
          Your ElevenLabs API key. Required if not provided in the WebSocket
          connection's header or query parameters. This applies to the
          (re)initialization of this specific context.
      authorization:
        type: optional<string>
        docs: >-
          Your authorization bearer token. Required if not provided in the
          WebSocket connection's header or query parameters. This applies to the
          (re)initialization of this specific context.
      context_id:
        type: optional<string>
        docs: >-
          An identifier for the text-to-speech context. If omitted, a default
          context ID may be assigned by the server. If provided, this message
          will create a new context with this ID or re-initialize an existing
          one with the new settings and text.
    source:
      openapi: asyncapi.yml
  TextToSpeechApplyTextNormalizationEnum:
    enum:
      - auto
      - 'on'
      - 'off'
    docs: >-
      This parameter controls text normalization with three modes - 'auto',
      'on', and 'off'. When set to 'auto', the system will automatically decide
      whether to apply text normalization (e.g., spelling out numbers). With
      'on', text normalization will always be applied, while with 'off', it will
      be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or
      'eleven_flash_v2_5' models. Defaults to 'auto'.
    default: auto
    source:
      openapi: asyncapi.yml
  TextToSpeechOutputFormatEnum:
    enum:
      - mp3_22050_32
      - mp3_44100_32
      - mp3_44100_64
      - mp3_44100_96
      - mp3_44100_128
      - mp3_44100_192
      - pcm_8000
      - pcm_16000
      - pcm_22050
      - pcm_24000
      - pcm_44100
      - ulaw_8000
      - alaw_8000
      - opus_48000_32
      - opus_48000_64
      - opus_48000_96
      - opus_48000_128
      - opus_48000_192
    docs: The output audio format
    source:
      openapi: asyncapi.yml
  StreamInputQueryParametersOptimizeStreamingLatency:
    enum:
      - value: '0'
        name: Zero
      - value: '1'
        name: One
      - value: '2'
        name: Two
      - value: '3'
        name: Three
      - value: '4'
        name: Four
    docs: Latency optimization level (deprecated)
    default: '0'
    inline: true
    source:
      openapi: asyncapi.yml
  StreamInputQueryParameters:
    properties:
      model_id:
        type: optional<string>
        docs: The model ID to use
      language_code:
        type: optional<string>
        docs: >-
          The ISO 639-1 language code (for Turbo v2.5 and Flash v2.5 models
          only)
      enable_logging:
        type: optional<string>
        docs: Whether to enable logging of the request
      enable_ssml_parsing:
        type: optional<boolean>
        docs: Whether to enable SSML parsing
        default: false
      optimize_streaming_latency:
        type: optional<StreamInputQueryParametersOptimizeStreamingLatency>
        docs: Latency optimization level (deprecated)
        default: '0'
        availability: deprecated
      output_format: optional<TextToSpeechOutputFormatEnum>
      inactivity_timeout:
        type: optional<double>
        docs: Timeout for inactivity before connection is closed
        default: 20
      sync_alignment:
        type: optional<boolean>
        docs: Whether to include timing data with every audio chunk
        default: false
      auto_mode:
        type: optional<boolean>
        docs: >-
          This parameter focuses on reducing the latency by disabling the chunk
          schedule and all buffers. It is only recommended when sending full
          sentences or phrases, sending partial phrases will result in highly
          reduced quality. By default it's set to false.
        default: false
      apply_text_normalization: optional<TextToSpeechApplyTextNormalizationEnum>
      seed:
        type: optional<integer>
        docs: >-
          If specified, our system will make a best effort to sample
          deterministically, such that repeated requests with the same seed and
          parameters should return the same result. Determinism is not
          guaranteed. Must be an integer between 0 and 4294967295.
        validation:
          min: 0
    source:
      openapi: asyncapi.yml
