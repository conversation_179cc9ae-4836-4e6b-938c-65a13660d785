import { useState, useCallback, useRef } from "react";

// Declare global speech recognition types
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

interface WebSocketHook {
  isConnected: boolean;
  sendMessage: (message: any) => void;
  subscribe: (messageType: string, handler: (data: any) => void) => () => void;
}

export function useSpeech(websocket?: WebSocketHook) {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [error, setError] = useState<string | null>(null);

  const recognitionRef = useRef<any>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);



  const requestMicrophonePermission = useCallback(async () => {
    try {
      console.log('🎤 Requesting microphone permission...');
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
      console.log('✅ Microphone permission granted');
      console.log('🎵 Audio tracks:', stream.getAudioTracks().length);
      stream.getAudioTracks().forEach((track, index) => {
        console.log(`🎵 Track ${index}:`, track.label, 'enabled:', track.enabled);
      });
      stream.getTracks().forEach(track => track.stop()); // Stop the test stream
      return true;
    } catch (err) {
      console.error('❌ Microphone permission denied:', err);
      setError(`Microphone permission denied: ${err.message}. Please allow microphone access and try again.`);
      return false;
    }
  }, []);

  const startWhisperRecording = useCallback(async () => {
    try {
      console.log('Starting Whisper recording...');
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });

        // Send audio to server for Whisper transcription
        if (websocket?.isConnected) {
          const arrayBuffer = await audioBlob.arrayBuffer();
          const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

          websocket.sendMessage({
            type: 'whisper_audio',
            audio: base64Audio,
            timestamp: Date.now()
          });
        }

        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();

      // Record for 3 seconds, then process
      setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
        }
      }, 3000);

    } catch (err) {
      setError('Failed to start audio recording');
    }
  }, []);

  const startListening = useCallback(async () => {
    console.log('🎤 Starting speech recognition...');
    console.log('🔌 WebSocket connected:', websocket?.isConnected);
    console.log('📱 MediaDevices available:', !!navigator.mediaDevices);
    console.log('🎵 MediaRecorder supported:', MediaRecorder.isTypeSupported('audio/webm;codecs=opus'));
    console.log('🌐 Browser:', navigator.userAgent.includes('Chrome') ? 'Chrome' : navigator.userAgent.includes('Firefox') ? 'Firefox' : 'Other');

    // Request microphone permission first
    const hasPermission = await requestMicrophonePermission();
    if (!hasPermission) {
      return;
    }

    // Use browser Speech Recognition first (more reliable for testing)
    // TODO: Switch to Whisper after browser STT is confirmed working

    if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const errorMsg = 'Speech recognition not supported in this browser';
      console.error('❌', errorMsg);
      setError(errorMsg);
      return;
    }

    console.log('🎤 Using browser Speech Recognition API');

    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();
      
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;
      
      console.log('Speech recognition configured:', {
        continuous: recognition.continuous,
        interimResults: recognition.interimResults,
        lang: recognition.lang
      });

      recognition.onstart = () => {
        console.log('Speech recognition started successfully');
        setIsListening(true);
        setError(null);
      };

      recognition.onresult = (event: any) => {
        let finalTranscript = '';
        let interimTranscript = '';

        console.log('Speech recognition result event:', event.results.length, 'results');

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          if (result.isFinal) {
            finalTranscript += result[0].transcript;
            console.log('Final transcript:', result[0].transcript);
          } else {
            interimTranscript += result[0].transcript;
            console.log('Interim transcript:', result[0].transcript);
          }
        }

        // Show interim results immediately for better user feedback
        if (interimTranscript) {
          console.log('Updating with interim results:', interimTranscript);
          setTranscript(prev => prev + ' [listening: ' + interimTranscript + ']');
        }

        if (finalTranscript) {
          console.log('Adding final transcript:', finalTranscript);
          setTranscript(prev => prev.replace(/\[listening:.*?\]/g, '') + '\n' + finalTranscript);

          // Send speech data to server
          if (websocket?.isConnected) {
            console.log('Sending speech data to server');
            websocket.sendMessage({
              type: 'speech_data',
              transcript: finalTranscript,
              confidence: event.results[event.resultIndex]?.[0]?.confidence || 0.8,
              timestamp: Date.now()
            });
          } else {
            console.log('WebSocket not ready, speech data not sent');
          }
        }

        // Send interim results to show processing feedback
        if (interimTranscript && websocket?.isConnected) {
          websocket.sendMessage({
            type: 'speech_interim',
            transcript: interimTranscript,
            timestamp: Date.now()
          });
        }
      };

      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error, event);
        const errorMsg = `Speech recognition error: ${event.error}`;
        setError(errorMsg);
        setIsListening(false);
        
        // Handle specific error cases
        if (event.error === 'not-allowed') {
          setError('Microphone permission denied. Please allow microphone access and try again.');
        } else if (event.error === 'no-speech') {
          console.log('⚠️ No speech detected, restarting recognition...');
          // Auto-restart on no-speech (common in continuous mode)
          setTimeout(() => {
            if (!isListening) {
              console.log('🔄 Auto-restarting speech recognition...');
              startListening();
            }
          }, 1000);
        } else if (event.error === 'audio-capture') {
          setError('Microphone not accessible. Please check your microphone settings.');
        } else if (event.error === 'network') {
          setError('Network error. Please check your internet connection.');
        }
      };

      recognition.onend = () => {
        console.log('Speech recognition ended');
        setIsListening(false);
      };

      recognitionRef.current = recognition;
      recognition.start();
    } catch (err) {
      console.error('Speech recognition error:', err);
      setError(err instanceof Error ? err.message : 'Failed to start speech recognition');
    }
  }, [websocket, requestMicrophonePermission, startWhisperRecording]);

  const stopListening = useCallback(() => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
    }
    setIsListening(false);
  }, []);

  return {
    isListening,
    transcript,
    error,
    startListening,
    stopListening,
  };
}
