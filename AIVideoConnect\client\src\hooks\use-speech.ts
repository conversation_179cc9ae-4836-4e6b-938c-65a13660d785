import { useState, useCallback, useRef, useEffect } from "react";
import { adaptiveSpeechManager } from "@/lib/adaptive-speech-config";

// Declare global speech recognition types
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

interface WebSocketHook {
  isConnected: boolean;
  sendMessage: (message: any) => void;
  subscribe: (messageType: string, handler: (data: any) => void) => () => void;
}

interface SpeechConfig {
  useWhisper: boolean;
  recordingDuration: number;
  language: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  noiseReduction: boolean;
  echoCancellation: boolean;
}

const DEFAULT_CONFIG: SpeechConfig = {
  useWhisper: false, // Use browser API for simplicity and reliability
  recordingDuration: 5000, // 5 seconds
  language: 'en-US',
  continuous: false, // Single recognition for better reliability
  interimResults: true,
  maxAlternatives: 1,
  noiseReduction: true,
  echoCancellation: true,
};

export function useSpeech(websocket?: WebSocketHook, config: Partial<SpeechConfig> = {}) {
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [confidence, setConfidence] = useState<number>(0);
  const [isProcessing, setIsProcessing] = useState(false);

  const recognitionRef = useRef<any>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const streamRef = useRef<MediaStream | null>(null);
  const recordingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef<number>(0);

  // Get adaptive configuration that learns from user's speech patterns
  const adaptiveConfig = adaptiveSpeechManager.getConfiguration();
  const finalConfig = { ...DEFAULT_CONFIG, ...adaptiveConfig, ...config };



  // Enhanced microphone permission and capability check
  const requestMicrophonePermission = useCallback(async () => {
    try {
      console.log('🎤 Requesting microphone permission...');

      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia is not supported in this browser');
      }

      // Request permission with enhanced audio constraints
      const constraints = {
        audio: {
          echoCancellation: finalConfig.echoCancellation,
          noiseSuppression: finalConfig.noiseReduction,
          autoGainControl: true,
          sampleRate: 16000, // Optimal for speech recognition
          channelCount: 1, // Mono audio
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log('✅ Microphone permission granted');
      console.log('🎵 Audio tracks:', stream.getAudioTracks().length);

      stream.getAudioTracks().forEach((track, index) => {
        console.log(`🎵 Track ${index}:`, track.label, 'enabled:', track.enabled);
        console.log(`🎵 Track settings:`, track.getSettings());
      });

      // Test audio levels briefly
      try {
        const audioContext = new AudioContext();
        const analyser = audioContext.createAnalyser();
        const source = audioContext.createMediaStreamSource(stream);
        source.connect(analyser);

        const dataArray = new Uint8Array(analyser.frequencyBinCount);
        analyser.getByteFrequencyData(dataArray);

        // Check if we're getting audio input
        const hasAudio = dataArray.some(value => value > 0);
        console.log('🎵 Audio input detected:', hasAudio);

        // Cleanup test resources
        source.disconnect();
        audioContext.close();

        if (!hasAudio) {
          console.warn('⚠️ No audio input detected - microphone may be muted');
        }
      } catch (audioTestError) {
        console.warn('⚠️ Could not test audio levels:', audioTestError);
      }

      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (err: any) {
      console.error('❌ Microphone permission/setup failed:', err);
      let errorMessage = 'Microphone access failed. ';

      if (err.name === 'NotAllowedError') {
        errorMessage += 'Please allow microphone access and try again.';
      } else if (err.name === 'NotFoundError') {
        errorMessage += 'No microphone found. Please connect a microphone.';
      } else if (err.name === 'NotReadableError') {
        errorMessage += 'Microphone is being used by another application.';
      } else {
        errorMessage += err.message || 'Unknown error occurred.';
      }

      setError(errorMessage);
      return false;
    }
  }, [finalConfig.echoCancellation, finalConfig.noiseReduction]);

  // Enhanced Whisper recording with better error handling and audio processing
  const startWhisperRecording = useCallback(async () => {
    try {
      console.log('🎙️ Starting Whisper recording...');
      setIsProcessing(true);
      setError(null);

      // Get audio stream with optimized settings
      const constraints = {
        audio: {
          echoCancellation: finalConfig.echoCancellation,
          noiseSuppression: finalConfig.noiseReduction,
          autoGainControl: true,
          sampleRate: 16000,
          channelCount: 1,
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      // Check for supported MIME types - prioritize formats that work well with Whisper
      const supportedTypes = [
        'audio/wav',           // Best compatibility with Whisper
        'audio/mp4',           // Good compatibility
        'audio/webm',          // Basic webm without codec specification
        'audio/ogg',           // Alternative format
        'audio/webm;codecs=opus' // Last resort - sometimes causes issues
      ];

      let mimeType = 'audio/wav'; // Default to most compatible
      for (const type of supportedTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          mimeType = type;
          console.log(`🎵 Selected MIME type: ${type}`);
          break;
        }
      }

      // If no supported types found, fall back to browser speech recognition
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        console.warn('⚠️ No supported audio formats for MediaRecorder, falling back to browser API');
        throw new Error('MediaRecorder not supported with compatible audio formats');
      }

      console.log('🎵 Using MIME type:', mimeType);

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType,
        audioBitsPerSecond: 128000, // Good quality for speech
      });

      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          console.log('📦 Audio chunk received:', event.data.size, 'bytes');
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        console.log('🛑 Recording stopped, processing audio...');
        setIsProcessing(true);

        try {
          const audioBlob = new Blob(audioChunksRef.current, { type: mimeType });
          console.log('🎵 Audio blob created:', audioBlob.size, 'bytes');

          if (audioBlob.size === 0) {
            throw new Error('No audio data recorded');
          }

          // Send audio to server for Whisper transcription
          if (websocket?.isConnected) {
            const arrayBuffer = await audioBlob.arrayBuffer();
            const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

            console.log('📤 Sending audio to server for transcription...');
            websocket.sendMessage({
              type: 'whisper_audio',
              audio: base64Audio,
              mimeType: mimeType,
              timestamp: Date.now(),
              duration: finalConfig.recordingDuration
            });
          } else {
            throw new Error('WebSocket not connected');
          }
        } catch (processingError) {
          console.error('❌ Audio processing failed:', processingError);
          setError(`Audio processing failed: ${processingError.message}`);
          setIsProcessing(false);
        }

        // Cleanup stream
        if (streamRef.current) {
          streamRef.current.getTracks().forEach(track => track.stop());
          streamRef.current = null;
        }
      };

      mediaRecorder.onerror = (event) => {
        console.error('❌ MediaRecorder error:', event);
        setError('Recording failed. Please try again.');
        setIsProcessing(false);
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start(1000); // Collect data every second

      // Set recording timeout
      recordingTimeoutRef.current = setTimeout(() => {
        if (mediaRecorder.state === 'recording') {
          console.log('⏰ Recording timeout reached, stopping...');
          mediaRecorder.stop();
        }
      }, finalConfig.recordingDuration);

      console.log(`🎙️ Recording started for ${finalConfig.recordingDuration}ms`);

    } catch (err: any) {
      console.error('❌ Failed to start Whisper recording:', err);
      setError(`Recording failed: ${err.message || 'Unknown error'}`);
      setIsProcessing(false);

      // Cleanup on error
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
        streamRef.current = null;
      }
    }
  }, [websocket, finalConfig.echoCancellation, finalConfig.noiseReduction, finalConfig.recordingDuration]);

  // Browser Speech Recognition implementation - moved before startListening to fix hoisting issue
  const startBrowserSpeechRecognition = useCallback(async () => {

    try {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognition = new SpeechRecognition();

      recognition.continuous = finalConfig.continuous;
      recognition.interimResults = finalConfig.interimResults;
      recognition.lang = finalConfig.language;
      recognition.maxAlternatives = finalConfig.maxAlternatives;

      recognition.onstart = () => {
        console.log('🎤 Browser speech recognition started');
        setIsProcessing(false);
      };

      recognition.onresult = (event) => {
        let finalTranscript = '';
        let interimTranscript = '';
        let maxConfidence = 0;

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const result = event.results[i];
          const transcript = result[0].transcript;
          const confidence = result[0].confidence || 0.8;

          if (result.isFinal) {
            finalTranscript += transcript;
            maxConfidence = Math.max(maxConfidence, confidence);
          } else {
            interimTranscript += transcript;
          }
        }

        if (finalTranscript) {
          console.log('✅ Browser speech result:', finalTranscript, 'confidence:', maxConfidence);
          setTranscript(finalTranscript);
          setConfidence(maxConfidence);

          // Record success for adaptive learning
          adaptiveSpeechManager.recordSuccess(maxConfidence, Date.now() - startTimeRef.current);

          // Send to WebSocket if connected
          if (websocket?.isConnected) {
            websocket.sendMessage({
              type: 'speech_data',
              transcript: finalTranscript,
              confidence: maxConfidence,
              source: 'browser'
            });
          }

          // Stop recognition after getting final result
          recognition.stop();
        } else if (interimTranscript) {
          setTranscript(interimTranscript);
        }
      };

      recognition.onerror = (event) => {
        console.error('❌ Browser speech recognition error:', event.error);
        const errorMsg = `Speech recognition error: ${event.error}`;

        // Record error for adaptive learning
        adaptiveSpeechManager.recordError(errorMsg);

        setError(errorMsg);
        setIsListening(false);
        setIsProcessing(false);
      };

      recognition.onend = () => {
        console.log('🔚 Browser speech recognition ended');
        setIsListening(false);
        setIsProcessing(false);
      };

      recognitionRef.current = recognition;
      startTimeRef.current = Date.now();
      recognition.start();

    } catch (error: any) {
      console.error('❌ Failed to start browser speech recognition:', error);
      const errorMsg = error.message || 'Failed to start speech recognition';

      // Record error for adaptive learning
      adaptiveSpeechManager.recordError(errorMsg);

      setError(errorMsg);
      setIsListening(false);
      setIsProcessing(false);
    }
  }, [finalConfig, websocket]);

  const startListening = useCallback(async () => {
    console.log('🎤 Starting speech recognition...');
    console.log('🔌 WebSocket connected:', websocket?.isConnected);
    console.log('📱 MediaDevices available:', !!navigator.mediaDevices);
    console.log('🎵 MediaRecorder supported:', MediaRecorder.isTypeSupported('audio/wav'));
    console.log('🌐 Browser:', navigator.userAgent.includes('Chrome') ? 'Chrome' : navigator.userAgent.includes('Firefox') ? 'Firefox' : 'Other');
    console.log('⚙️ Config:', finalConfig);

    // Reset state
    setError(null);
    setIsProcessing(false);
    retryCountRef.current = 0;

    // Request microphone permission first
    const hasPermission = await requestMicrophonePermission();
    if (!hasPermission) {
      return;
    }

    // Try methods in order of preference with fallbacks
    let success = false;

    // Method 1: Try Whisper API if configured and WebSocket is connected
    if (finalConfig.useWhisper && websocket?.isConnected) {
      console.log('🎙️ Attempting Whisper API for speech recognition');
      try {
        await startWhisperRecording();
        success = true;
      } catch (error) {
        console.warn('⚠️ Whisper API failed, trying browser API:', error);
        adaptiveSpeechManager.recordError('whisper_failed');
      }
    }

    // Method 2: Fallback to browser Speech Recognition API
    if (!success && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      console.log('🎤 Using browser Speech Recognition API');
      try {
        await startBrowserSpeechRecognition();
        success = true;
      } catch (error) {
        console.warn('⚠️ Browser speech recognition failed:', error);
        adaptiveSpeechManager.recordError('browser_api_failed');
      }
    }

    // Method 3: If both fail, provide helpful error message
    if (!success) {
      const errorMsg = 'Speech recognition is not available. Please check your microphone permissions and try refreshing the page.';
      console.error('❌', errorMsg);
      setError(errorMsg);

      // Provide specific troubleshooting based on the situation
      if (!websocket?.isConnected) {
        setError('WebSocket connection required for speech recognition. Please refresh the page.');
      } else if (!('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
        setError('Your browser does not support speech recognition. Please use Chrome, Edge, or Safari.');
      }
    }
  }, [websocket, finalConfig, requestMicrophonePermission, startWhisperRecording, startBrowserSpeechRecognition]);

  // Retry logic for failed attempts with adaptive configuration
  const retryRecognition = useCallback(() => {
    const maxRetries = finalConfig.retryAttempts || 3;
    if (retryCountRef.current < maxRetries) {
      retryCountRef.current++;
      console.log(`🔄 Retrying speech recognition (attempt ${retryCountRef.current}/${maxRetries})...`);

      // Record the retry as an error for adaptive learning
      adaptiveSpeechManager.recordError(error || 'retry_needed');

      setTimeout(() => {
        startListening();
      }, 1000 * retryCountRef.current); // Exponential backoff
    } else {
      console.error('❌ Max retry attempts reached');
      const recommendations = adaptiveSpeechManager.getRecommendations();
      const errorMsg = recommendations.length > 0
        ? `Speech recognition failed. Try: ${recommendations[0]}`
        : 'Speech recognition failed after multiple attempts. Please check your microphone and try again.';

      setError(errorMsg);
      setIsListening(false);
      setIsProcessing(false);
    }
  }, [startListening, finalConfig.retryAttempts, error]);

  // Duplicate function removed - using the one defined earlier

  const stopListening = useCallback(() => {
    console.log('🛑 Stopping speech recognition...');

    // Stop browser speech recognition
    if (recognitionRef.current) {
      recognitionRef.current.stop();
      recognitionRef.current = null;
    }

    // Stop Whisper recording
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current = null;
    }

    // Stop audio stream
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }

    // Clear timeout
    if (recordingTimeoutRef.current) {
      clearTimeout(recordingTimeoutRef.current);
      recordingTimeoutRef.current = null;
    }

    setIsListening(false);
    setIsProcessing(false);
  }, []);

  // Handle WebSocket messages for Whisper transcription results
  useEffect(() => {
    if (!websocket?.isConnected) return;

    const unsubscribe = websocket.subscribe('ai_response', (data) => {
      if (data.transcript) {
        console.log('📝 Received Whisper transcription:', data.transcript);
        setTranscript(prev => prev + (prev ? '\n' : '') + data.transcript);
        setConfidence(data.confidence || 0.8);
        setIsProcessing(false);

        // Record success for adaptive learning
        adaptiveSpeechManager.recordSuccess(
          data.confidence || 0.8,
          data.duration || 3000
        );

        // Auto-restart recording if continuous mode is enabled
        if (finalConfig.continuous && isListening) {
          setTimeout(() => {
            startWhisperRecording();
          }, 500); // Small delay before next recording
        }
      } else if (data.error) {
        // Record error for adaptive learning
        adaptiveSpeechManager.recordError(data.errorDetails || data.error);
        setError(data.message || 'Speech recognition failed');
        setIsProcessing(false);
      }
    });

    return unsubscribe;
  }, [websocket, isListening, finalConfig.continuous, startWhisperRecording]);



  const getRecommendations = useCallback(() => {
    return adaptiveSpeechManager.getRecommendations();
  }, []);

  const getMetrics = useCallback(() => {
    return adaptiveSpeechManager.getMetrics();
  }, []);

  const resetAdaptiveConfig = useCallback(() => {
    adaptiveSpeechManager.reset();
  }, []);

  return {
    isListening,
    transcript,
    error,
    confidence,
    isProcessing,
    startListening,
    stopListening,
    retryRecognition,
    getRecommendations,
    getMetrics,
    resetAdaptiveConfig,
    currentConfig: finalConfig,
  };
}
